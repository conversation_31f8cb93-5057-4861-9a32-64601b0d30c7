//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：main.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.23
//---------------------------------------------------------
#ifndef _MAIN_H
#define _MAIN_H

#include "spi.h"
#include "Smi240.h"
#include "uart_dma.h"
#include "uart_Irq.h"
#include "Timer.h"
#include "SetParaBao.h"
#include "flash.h"
#include "protocol.h"


typedef enum {
	

	Combination_Task=1,//组合惯导输出

	Pure_Task=0,//纯惯导输出

    Original_Task=2,//原始数据输出

	MAX_Task,
}WorkTask_e;

//#define WorkTask   Original_Task //原始数据输出
//#define WorkTask   Combination_Task //组合惯导输出
//#define WorkTask   Pure_Task //纯惯导输出*************




#endif /* _MAIN_H */