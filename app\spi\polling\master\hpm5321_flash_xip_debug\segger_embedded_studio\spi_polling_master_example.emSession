<!DOCTYPE CrossStudio_Session_File>
<session>
 <Bookmarks/>
 <Breakpoints groups="Breakpoints" active_group="Breakpoints">
  <BreakpointListItem trigger="" line="204" counter="0" hardwareBreakpoint="" isFunctionBreakpoint="false" action="" expression="" stopAll="false" group="Breakpoints" type="Breakpoint" state="4" filename="../../arithmetic/arithmetic.c" useHWbreakpoint="false"/>
 </Breakpoints>
 <ExecutionProfileWindow/>
 <FrameBuffer>
  <FrameBufferWindow width="0" keepAspectRatio="0" zoomToFitWindow="0" showGrid="0" addressSpace="" format="0" height="0" autoEvaluate="0" scaleFactor="1" refreshPeriod="0" name="spi_polling_master_example - hpm5321_Debug" addressText="" accessByDisplayWidth="0"/>
 </FrameBuffer>
 <Memory1>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="spi_polling_master_example - hpm5321_Debug" sizeText="" addressText=""/>
 </Memory1>
 <Memory2>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="spi_polling_master_example - hpm5321_Debug" sizeText="" addressText=""/>
 </Memory2>
 <Memory3>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="spi_polling_master_example - hpm5321_Debug" sizeText="" addressText=""/>
 </Memory3>
 <Memory4>
  <MemoryWindow addressSpace="" dataSize="1" autoEvaluate="0" viewMode="0" viewType="4" addressOrder="0" columnsText="" refreshPeriod="0" name="spi_polling_master_example - hpm5321_Debug" sizeText="" addressText=""/>
 </Memory4>
 <Project>
  <ProjectSessionItem path="spi_polling_master_example"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;application"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;application;Arithmetic"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;application;Drv"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;application;src"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;boards"/>
  <ProjectSessionItem path="spi_polling_master_example;spi_polling_master_example - hpm5321;boards;hpm5321"/>
 </Project>
 <Register1>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="spi_polling_master_example - hpm5321_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register1>
 <Register2>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="spi_polling_master_example - hpm5321_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register2>
 <Register3>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="spi_polling_master_example - hpm5321_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register3>
 <Register4>
  <RegisterWindow visibleNodes="ABI, RV32I/pc;ABI, RV32I/ra;ABI, RV32I/sp;ABI, RV32I/gp;ABI, RV32I/tp;ABI, RV32I/a0;ABI, RV32I/a1;ABI, RV32I/a2;ABI, RV32I/a3;ABI, RV32I/a4;ABI, RV32I/a5;ABI, RV32I/a6;ABI, RV32I/a7;ABI, RV32I/t0;ABI, RV32I/t1;ABI, RV32I/t2;ABI, RV32I/t3;ABI, RV32I/t4;ABI, RV32I/t5;ABI, RV32I/t6;ABI, RV32I/s0;ABI, RV32I/s1;ABI, RV32I/s2;ABI, RV32I/s3;ABI, RV32I/s4;ABI, RV32I/s5;ABI, RV32I/s6;ABI, RV32I/s7;ABI, RV32I/s8;ABI, RV32I/s9;ABI, RV32I/s10;ABI, RV32I/s11;CPU, RV32I/pc;CPU, RV32I/x1;CPU, RV32I/x2;CPU, RV32I/x3;CPU, RV32I/x4;CPU, RV32I/x5;CPU, RV32I/x6;CPU, RV32I/x7;CPU, RV32I/x8;CPU, RV32I/x9;CPU, RV32I/x10;CPU, RV32I/x11;CPU, RV32I/x12;CPU, RV32I/x13;CPU, RV32I/x14;CPU, RV32I/x15;CPU, RV32I/x16;CPU, RV32I/x17;CPU, RV32I/x18;CPU, RV32I/x19;CPU, RV32I/x20;CPU, RV32I/x21;CPU, RV32I/x22;CPU, RV32I/x23;CPU, RV32I/x24;CPU, RV32I/x25;CPU, RV32I/x26;CPU, RV32I/x27;CPU, RV32I/x28;CPU, RV32I/x29;CPU, RV32I/x30;CPU, RV32I/x31" binaryNodes="" asciiNodes="" openNodes="ABI, RV32I;CPU, RV32I" name="spi_polling_master_example - hpm5321_Debug" decimalNodes="" octalNodes="" unsignedNodes=""/>
 </Register4>
 <Threads>
  <ThreadsWindow showLists=""/>
 </Threads>
 <TraceWindow>
  <Trace enabled="Yes"/>
 </TraceWindow>
 <Watch1>
  <Watches active="1" update="Never">
   <Watchpoint expression="stSetPara" name="stSetPara" radix="-1" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="stTemperCompenPara" name="stTemperCompenPara" radix="16" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="SaveFlashDatabuf" name="SaveFlashDatabuf" radix="-1" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="ReadFlashDatabuf" name="ReadFlashDatabuf" radix="-1" linenumber="0" filename="../segger_embedded_studio"/>
   <Watchpoint expression="g_Navi" name="g_Navi" radix="-1" linenumber="170" filename="../../arithmetic/arithmetic.c"/>
   <Watchpoint expression="r_Gyro" name="r_Gyro" radix="-1" linenumber="197" filename="../../arithmetic/arithmetic.c"/>
   <Watchpoint expression="estimated_gyro_bias" name="estimated_gyro_bias" radix="-1" linenumber="31" filename="../../arithmetic/arithmetic.c"/>
   <Watchpoint expression="lp_combineData" name="lp_combineData" radix="16" linenumber="44" filename="../../arithmetic/arithmetic.c"/>
   <Watchpoint expression="combineData" name="combineData" radix="-1" linenumber="214" filename="../../arithmetic/arithmetic.c"/>
  </Watches>
 </Watch1>
 <Watch2>
  <Watches active="0" update="Never"/>
 </Watch2>
 <Watch3>
  <Watches active="0" update="Never"/>
 </Watch3>
 <Watch4>
  <Watches active="0" update="Never"/>
 </Watch4>
 <Files>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="168" useTextEdit="1" path="../../src/main.c" left="0" top="141" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../arithmetic/align.c" left="0" top="0" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="203" useTextEdit="1" path="../../arithmetic/arithmetic.c" left="0" top="186" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="56" useTextEdit="1" path="../../arithmetic/navi.c" left="0" top="43" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="22" useTextEdit="1" path="../../Drv/flash.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../Drv/spi.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../Drv/Timer.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../Drv/uart_dma.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="19" y="124" useTextEdit="1" path="../../Drv/Uart_Irq.c" left="0" top="106" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../src/AnnTempCompen.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="6" y="272" useTextEdit="1" path="../../src/compen.c" left="0" top="248" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../src/FirmwareUpdateFile.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="0" useTextEdit="1" path="../../src/matvecmath.c" left="0" top="0" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="35" y="59" useTextEdit="1" path="../../src/protocol.c" left="0" top="39" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="585" useTextEdit="1" path="../../src/SetParaBao.c" left="0" top="575" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="22" y="27" useTextEdit="1" path="../../src/Smi240.c" left="0" top="27" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="5" y="144" useTextEdit="1" path="../../../../../../boards/hpm5321/board.c" left="0" top="131" codecName="UTF-8"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="7" y="224" useTextEdit="1" path="../../Arithmetic/ZUPT.c" left="0" top="224" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="15" y="99" useTextEdit="1" path="../../src/Smi980.c" left="0" selected="1" top="70" codecName="Default"/>
  <SessionOpenFile windowGroup="DockEditLeft" x="0" y="60" useTextEdit="1" path="../../hpm_sdk_localized_for_hpm5321/drivers/inc/hpm_csr_drv.h" left="0" top="44" codecName="Default"/>
 </Files>
 <EMStudioWindow activeProject="spi_polling_master_example - hpm5321" fileDialogDefaultFilter="*.c" autoConnectTarget="J-Link" buildConfiguration="Debug" sessionSettings="" debugSearchFileMap="" fileDialogInitialDirectory="C:/Users/<USER>/Desktop/code/Smi240720/Smi240/app/spi/polling/master/Arithmetic" debugSearchPath="" autoConnectCapabilities="3199"/>
</session>
