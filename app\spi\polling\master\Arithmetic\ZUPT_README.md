# ZUPT静止检测功能说明

## 概述

ZUPT（Zero Velocity Update）静止检测功能用于检测IMU设备是否处于静止状态。该功能通过分析加速度计和陀螺仪数据的统计特性来判断设备的运动状态。

## 模块化设计

ZUPT功能已经从arithmetic.c中分离出来，形成独立的模块：

- **ZUPT.h**: ZUPT功能头文件，包含所有函数声明和宏定义
- **ZUPT.c**: ZUPT功能实现文件，包含所有函数实现
- **arithmetic.c**: 主算法文件，只包含ZUPT功能的调用

## 功能特性

1. **滑动窗口统计**：维护固定大小的IMU数据窗口，实时更新统计信息
2. **增量统计计算**：使用高效的增量方式计算均值和方差，避免重复计算
3. **方差检测**：计算窗口内加速度和角速度的方差，与预设阈值比较
4. **静止判断**：当所有轴的方差都低于阈值时，判定为静止状态
5. **角增量约束**：在静止状态下对角增量进行缩放约束，提高导航精度
6. **结构化数据管理**：使用专门的ZUPT数据结构体管理所有相关变量
7. **模块化设计**：独立的.c/.h文件，便于维护和复用

## 配置参数

```c
#define ZUPT_WINDOW_SIZE      50      // ZUPT检测窗口大小（样本数）
#define ZUPT_ACC_THRESHOLD    0.1     // 加速度方差阈值 (m/s²)²
#define ZUPT_GYRO_THRESHOLD   0.01    // 角速度方差阈值 (rad/s)²
```

### 参数说明

- **ZUPT_WINDOW_SIZE**: 滑动窗口大小，建议设置为采样率的1/4到1/2秒的数据量
- **ZUPT_ACC_THRESHOLD**: 加速度合成幅值方差阈值，根据传感器噪声水平调整
- **ZUPT_GYRO_THRESHOLD**: 角速度合成幅值方差阈值，根据陀螺仪噪声水平调整
- **ZUPT_ANGLE_SCALE_FACTOR**: ZUPT角增量缩放因子，静止时角增量缩放比例（默认0.1）

### 检测原理

新的检测方法使用合成幅值而不是各轴独立判断：

1. **加速度合成幅值**: `|a| = √(ax² + ay² + az²)`
2. **角速度合成幅值**: `|ω| = √(ωx² + ωy² + ωz²)`
3. **方差计算**: 对合成幅值序列计算方差
4. **静止判断**: 当合成幅值方差都低于阈值时判定为静止

这种方法的优势：
- 更符合物理直觉，考虑了三轴的综合运动状态
- 避免了单轴噪声导致的误判
- 对旋转运动更敏感

### ZUPT角增量约束原理

当设备处于静止状态时，理论上角增量应该为零。但由于陀螺仪噪声和漂移，实际测量的角增量不为零。ZUPT角增量约束通过缩放因子来减小这些误差：

```c
// 角增量约束实现
void ZUPTAngleConstraint(DELANG lp_DelSenbb[3])
{
    // 检查ZUPT标志位是否有效
    if (!GetZUPTFlag()) {
        return;  // ZUPT无效时不进行约束
    }

    // 对角增量使用缩放因子进行约束
    for (int i = 0; i < 3; i++) {
        lp_DelSenbb[i] *= ZUPT_ANGLE_SCALE_FACTOR;
    }
}
```

**约束效果**：
- 静止时：角增量被缩放到原来的10%（默认缩放因子0.1）
- 运动时：不进行约束，保持原始角增量
- 提高精度：减少静止状态下的姿态漂移

## 数据结构

### ZUPT结构体定义

```c
typedef struct s_ZUPT
{
    // 窗口管理
    COUNT WindowIndex;                      // 当前窗口索引
    COUNT SampleCount;                      // 当前样本计数
    BOOL isWindowFilled;                    // 窗口是否已填满

    // IMU数据滑动窗口
    float AccWindow[50][3];                 // 加速度滑动窗口
    float GyroWindow[50][3];                // 角速度滑动窗口

    // 增量统计量
    DPARA AccSum[3];                        // 加速度累加和
    DPARA GyroSum[3];                       // 角速度累加和
    DPARA AccSumSquare[3];                  // 加速度平方累加和
    DPARA GyroSumSquare[3];                 // 角速度平方累加和

    // 统计结果
    DPARA AccMean[3];                       // 加速度均值
    DPARA GyroMean[3];                      // 角速度均值
    DPARA AccVariance[3];                   // 加速度方差
    DPARA GyroVariance[3];                  // 角速度方差

    // 检测结果
    BOOL ZUPTFlag;                          // ZUPT静止检测标志
    BOOL isZUPTValid;                       // ZUPT检测是否有效

    // 调试信息
    COUNT DetectionCount;                   // 检测次数计数
    COUNT StaticCount;                      // 静止状态计数
    COUNT DynamicCount;                     // 运动状态计数
} ZUPT, *p_ZUPT;
```

## 使用方法

### 1. 包含头文件

在需要使用ZUPT功能的文件中包含头文件：

```c
#include "ZUPT.h"
```

### 2. 初始化

在系统启动时调用初始化函数：

```c
ZUPTInit(&g_ZUPT);
```

### 3. 函数调用

ZUPT检测功能已集成到主算法流程中（在arithmetic.c中）：

```c
#include "ZUPT.h"  // 包含ZUPT头文件

void INS600mAlgorithmEntry(void)
{
    IMUdataPredo();

    // ZUPT静止检测函数调用
    // 调用条件：初始零偏估计已完成
    if (bias_estimate_done) {
        ZUPTDetection(&combineData);
    }

    AlgorithmAct();
    NavDataOutputSet(&combineData, &g_Navi, &InavOutData);
}
```

### 4. 获取ZUPT状态

```c
// 获取基本状态
int zupt_status = GetZUPTFlag();
BOOL zupt_valid = GetZUPTValid();

if (zupt_valid && zupt_status) {
    printf("Device is stationary\n");
} else if (zupt_valid && !zupt_status) {
    printf("Device is moving\n");
} else {
    printf("ZUPT detection not ready\n");
}
```

### 5. 获取详细统计信息

```c
// 获取各轴统计信息
DPARA acc_mean[3], gyro_mean[3], acc_variance[3], gyro_variance[3];
GetZUPTStatistics(acc_mean, gyro_mean, acc_variance, gyro_variance);

printf("Acceleration variance: [%.6f, %.6f, %.6f]\n",
       acc_variance[0], acc_variance[1], acc_variance[2]);
printf("Gyroscope variance: [%.6f, %.6f, %.6f]\n",
       gyro_variance[0], gyro_variance[1], gyro_variance[2]);

// 获取合成幅值统计信息（用于ZUPT检测）
DPARA acc_mag_mean, gyro_mag_mean, acc_mag_var, gyro_mag_var;
GetZUPTMagnitudeStatistics(&acc_mag_mean, &gyro_mag_mean, &acc_mag_var, &gyro_mag_var);

printf("Acceleration magnitude variance: %.6f\n", acc_mag_var);
printf("Gyroscope magnitude variance: %.6f\n", gyro_mag_var);
```

### 6. ZUPT角增量约束

ZUPT角增量约束功能会在静止状态下自动调用：

```c
// 在NaviCompute函数中自动调用
ComputeDelSenbb(lp_Navi->r_Wnbb, lp_Navi->r_DelSenbb_1, lp_Navi->r_DelSenbb_2, lp_Navi->r_DelSenbb);

// ZUPT角增量约束函数调用
// 调用条件：ZUPT标志位有效时
ZUPTAngleConstraint(lp_Navi->r_DelSenbb);

ComputeQ(lp_Navi->r_DelSenbb, lp_Navi->Q);
```

### 7. 测试功能

可以调用测试函数验证ZUPT功能：

```c
TestZUPTFunction();
```

## 文件结构

```
Arithmetic/
├── ZUPT.h              # ZUPT功能头文件
├── ZUPT.c              # ZUPT功能实现文件
├── arithmetic.h        # 主算法头文件
├── arithmetic.c        # 主算法实现文件（包含ZUPT调用）
└── ZUPT_README.md      # ZUPT功能说明文档
```

### 文件依赖关系

```
arithmetic.c
    ├── #include "ZUPT.h"
    └── 调用 ZUPTInit(), ZUPTDetection()

ZUPT.c
    ├── #include "ZUPT.h"
    ├── #include "EXTERNGLOBALDATA.h"
    └── 实现所有ZUPT功能函数

ZUPT.h
    ├── #include "EXTERNGLOBALDATA.h"
    ├── #include "arithmetic.h"
    └── 声明所有ZUPT功能函数
```

## 工作原理

### 1. 数据窗口更新

- 每次调用`ZUPTDetection()`时，将当前IMU数据存入滑动窗口
- 窗口采用循环缓冲区方式，自动覆盖最旧的数据
- 只有窗口填满后才开始静止检测

### 2. 增量统计计算

使用高效的增量方式计算统计量：

```c
// 移除旧数据的贡献（当窗口已满时）
if (lp_ZUPT->isWindowFilled) {
    float old_acc = lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][j];
    lp_ZUPT->AccSum[j] -= old_acc;
    lp_ZUPT->AccSumSquare[j] -= old_acc * old_acc;
}

// 添加新数据的贡献
lp_ZUPT->AccSum[j] += current_acc[j];
lp_ZUPT->AccSumSquare[j] += current_acc[j] * current_acc[j];

// 计算均值和方差
lp_ZUPT->AccMean[j] = lp_ZUPT->AccSum[j] / window_size;
lp_ZUPT->AccVariance[j] = (lp_ZUPT->AccSumSquare[j] / window_size) -
                          (lp_ZUPT->AccMean[j] * lp_ZUPT->AccMean[j]);
```

### 3. 静止判断

静止条件：加速度合成幅值的方差和角速度合成幅值的方差都必须低于对应阈值

```c
// 计算合成幅值
float current_acc_magnitude = sqrt(current_acc[0] * current_acc[0] +
                                   current_acc[1] * current_acc[1] +
                                   current_acc[2] * current_acc[2]);

float current_gyro_magnitude = sqrt(current_gyro[0] * current_gyro[0] +
                                    current_gyro[1] * current_gyro[1] +
                                    current_gyro[2] * current_gyro[2]);

// 判断条件
BOOL is_static = (lp_ZUPT->AccMagnitudeVariance <= lp_ZUPT->AccThreshold) &&
                 (lp_ZUPT->GyroMagnitudeVariance <= lp_ZUPT->GyroThreshold);
```

## 调用时机

### ZUPT静止检测
- **调用位置**：在`IMUdataPredo()`调用后
- **调用条件**：初始零偏估计已完成（`bias_estimate_done == 1`）
- **调用频率**：每个IMU数据更新周期调用一次

### ZUPT角增量约束
- **调用位置**：纯惯导航阶段、`ComputeDelSenbb()`调用后
- **调用条件**：ZUPT标志位有效时（`ZUPT_flag == 1`）
- **调用频率**：每个导航计算周期调用一次
- **作用对象**：`lp_Navi->r_DelSenbb`角增量数组

## 注意事项

1. **阈值调整**：根据实际传感器的噪声特性调整阈值参数
2. **窗口大小**：窗口太小可能导致误判，窗口太大会增加检测延迟
3. **初始化**：确保在零偏估计完成后才开始ZUPT检测
4. **环境影响**：振动环境可能影响检测精度，需要相应调整阈值

## 返回值

- `GetZUPTFlag()` 返回值：
  - `1`：设备处于静止状态
  - `0`：设备处于运动状态或窗口未填满

- `GetZUPTValid()` 返回值：
  - `1`：ZUPT检测有效（窗口已填满）
  - `0`：ZUPT检测无效（窗口未填满）

- `GetZUPTStatistics()` 功能：
  - 获取当前窗口内的加速度和角速度均值、方差
  - 用于调试和监控ZUPT检测过程

## 应用场景

1. **惯性导航**：在静止状态下进行零速修正
2. **姿态估计**：静止时提高姿态解算精度
3. **功耗管理**：静止时降低算法计算频率
4. **标定校准**：静止状态下进行传感器校准

## 检测方法对比

### 原方法（各轴独立判断）
```c
// 需要所有轴都满足条件
for (int j = 0; j < 3; j++) {
    if (acc_variance[j] > threshold || gyro_variance[j] > threshold) {
        is_static = 0;  // 任一轴超阈值即判定为运动
        break;
    }
}
```

### 新方法（合成幅值判断）
```c
// 使用合成幅值的方差
BOOL is_static = (acc_magnitude_variance <= acc_threshold) &&
                 (gyro_magnitude_variance <= gyro_threshold);
```

### 优势对比

| 特性 | 原方法 | 新方法 |
|------|--------|--------|
| 物理意义 | 各轴独立 | 综合运动状态 |
| 抗噪声能力 | 较弱 | 较强 |
| 旋转敏感性 | 一般 | 更好 |
| 误判概率 | 较高 | 较低 |
| 计算复杂度 | 低 | 稍高 |
