/*
 * Copyright (c) 2021-2024 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */


#ifndef HPM_DMAMUX_SRC_H
#define HPM_DMAMUX_SRC_H

/* dma mux definitions */
#define HPM_DMA_SRC_GPTMR0_0                               (0x0UL)
#define HPM_DMA_SRC_GPTMR0_1                               (0x1UL)
#define HPM_DMA_SRC_GPTMR0_2                               (0x2UL)
#define HPM_DMA_SRC_GPTMR0_3                               (0x3UL)
#define HPM_DMA_SRC_GPTMR1_0                               (0x4UL)
#define HPM_DMA_SRC_GPTMR1_1                               (0x5UL)
#define HPM_DMA_SRC_GPTMR1_2                               (0x6UL)
#define HPM_DMA_SRC_GPTMR1_3                               (0x7UL)
#define HPM_DMA_SRC_GPTMR2_0                               (0x8UL)
#define HPM_DMA_SRC_GPTMR2_1                               (0x9UL)
#define HPM_DMA_SRC_GPTMR2_2                               (0xAUL)
#define HPM_DMA_SRC_GPTMR2_3                               (0xBUL)
#define HPM_DMA_SRC_GPTMR3_0                               (0xCUL)
#define HPM_DMA_SRC_GPTMR3_1                               (0xDUL)
#define HPM_DMA_SRC_GPTMR3_2                               (0xEUL)
#define HPM_DMA_SRC_GPTMR3_3                               (0xFUL)
#define HPM_DMA_SRC_UART0_RX                               (0x14UL)
#define HPM_DMA_SRC_UART0_TX                               (0x15UL)
#define HPM_DMA_SRC_UART1_RX                               (0x16UL)
#define HPM_DMA_SRC_UART1_TX                               (0x17UL)
#define HPM_DMA_SRC_UART2_RX                               (0x18UL)
#define HPM_DMA_SRC_UART2_TX                               (0x19UL)
#define HPM_DMA_SRC_UART3_RX                               (0x1AUL)
#define HPM_DMA_SRC_UART3_TX                               (0x1BUL)
#define HPM_DMA_SRC_UART4_RX                               (0x1CUL)
#define HPM_DMA_SRC_UART4_TX                               (0x1DUL)
#define HPM_DMA_SRC_UART5_RX                               (0x1EUL)
#define HPM_DMA_SRC_UART5_TX                               (0x1FUL)
#define HPM_DMA_SRC_UART6_RX                               (0x20UL)
#define HPM_DMA_SRC_UART6_TX                               (0x21UL)
#define HPM_DMA_SRC_UART7_RX                               (0x22UL)
#define HPM_DMA_SRC_UART7_TX                               (0x23UL)
#define HPM_DMA_SRC_I2C0                                   (0x24UL)
#define HPM_DMA_SRC_I2C1                                   (0x25UL)
#define HPM_DMA_SRC_I2C2                                   (0x26UL)
#define HPM_DMA_SRC_I2C3                                   (0x27UL)
#define HPM_DMA_SRC_SPI0_RX                                (0x28UL)
#define HPM_DMA_SRC_SPI0_TX                                (0x29UL)
#define HPM_DMA_SRC_SPI1_RX                                (0x2AUL)
#define HPM_DMA_SRC_SPI1_TX                                (0x2BUL)
#define HPM_DMA_SRC_SPI2_RX                                (0x2CUL)
#define HPM_DMA_SRC_SPI2_TX                                (0x2DUL)
#define HPM_DMA_SRC_SPI3_RX                                (0x2EUL)
#define HPM_DMA_SRC_SPI3_TX                                (0x2FUL)
#define HPM_DMA_SRC_MCAN0                                  (0x30UL)
#define HPM_DMA_SRC_MCAN1                                  (0x31UL)
#define HPM_DMA_SRC_MCAN2                                  (0x32UL)
#define HPM_DMA_SRC_MCAN3                                  (0x33UL)
#define HPM_DMA_SRC_MOT_0                                  (0x34UL)
#define HPM_DMA_SRC_MOT_1                                  (0x35UL)
#define HPM_DMA_SRC_MOT_2                                  (0x36UL)
#define HPM_DMA_SRC_MOT_3                                  (0x37UL)
#define HPM_DMA_SRC_MOT_4                                  (0x38UL)
#define HPM_DMA_SRC_MOT_5                                  (0x39UL)
#define HPM_DMA_SRC_MOT_6                                  (0x3AUL)
#define HPM_DMA_SRC_MOT_7                                  (0x3BUL)
#define HPM_DMA_SRC_XPI0_RX                                (0x3CUL)
#define HPM_DMA_SRC_XPI0_TX                                (0x3DUL)
#define HPM_DMA_SRC_DAC0                                   (0x3EUL)
#define HPM_DMA_SRC_DAC1                                   (0x3FUL)
#define HPM_DMA_SRC_ACMP0                                  (0x40UL)
#define HPM_DMA_SRC_ACMP1                                  (0x41UL)



#endif /* HPM_DMAMUX_SRC_H */
