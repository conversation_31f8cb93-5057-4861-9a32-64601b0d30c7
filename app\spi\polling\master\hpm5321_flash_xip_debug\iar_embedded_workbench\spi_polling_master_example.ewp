<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>GDeviceSelect</name>
                    <state>HPM5361xCBx	HPMicro HPM5361xCBx</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Debug\BrowseInfo</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>1</version>
                    <state>3</state>
                </option>
                <option>
                    <name>GRTDescription</name>
                    <state>A compact configuration of the C/C++14 runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>GRTConfigPath</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>1</version>
                    <state>3</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
                <option>
                    <name>GGeneralStack</name>
                    <state>0x4000</state>
                </option>
                <option>
                    <name>GHeapSize</name>
                    <state>0x4000</state>
                </option>
                <option>
                    <name>GNumCores</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>GDeviceSelectSlave</name>
                    <state>RV32	RV32</state>
                </option>
                <option>
                    <name>GGeneralAutoVectorSetup</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCoreIBASRadioSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceMultSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceAtomicSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCompactSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceFloatSelectSlave</name>
                    <version>1</version>
                    <state></state>
                </option>
                <option>
                    <name>GCoreDevice</name>
                    <state>rv32imac_Zba_Zbb_Zbc_Zbs</state>
                </option>
                <option>
                    <name>RadioStdOutErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioLibLowLev</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceUserLvlIntSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipASlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipBSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXandesperfSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceBitmanipSSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Debug\</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCountZeroesSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GCodeModelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseJalSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXZenVBitfieldsSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceNXPVirgoSupervisorSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceResumableNMISlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDevicePackedSIMDZpsfoperandSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceDspRadioSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCacheManagementSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCachePrefetchSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheZeroSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheEswinSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>8</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>ICore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>2</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCNoSizeConst</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>0</version>
                    <state>1111111</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptions</name>
                    <state>--diag_suppress Pa089</state>
                    <state>--diag_suppress Pe236</state>
                    <state>--diag_suppress Pe188</state>
                    <state>--diag_suppress Pe546</state>
                    <state>--diag_suppress Pe111</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    <state>FLASH_XIP=1</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLIC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCHTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLICSW=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_UART=y</state>
                    <state>HPMSOC_HAS_HPMSDK_I2C=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SPI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_CRC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TSNS=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MBX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_EWDG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAMUX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIOM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCAN=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PTPC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEIV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MMC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PWM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RDC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYNT=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TRGM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_USB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SDP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MON=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RNG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OTP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_KEYM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ADC16=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DAC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OPAMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ACMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYSCTL=y</state>
                    <state>HPMSOC_HAS_HPMSDK_IOC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLLCTLV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PPOR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PCFG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PGPR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PDGO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PMP=y</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/arch</state>
                    <state>$PROJ_DIR$\../../../../../../boards/hpm5321</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/drivers/inc</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/utils</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/components/debug_console</state>
                    <state>$PROJ_DIR$\../build_tmp/generated/include</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ICodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IASMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AsmCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/arch</state>
                    <state>$PROJ_DIR$\../../../../../../boards/hpm5321</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/drivers/inc</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/utils</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/components/debug_console</state>
                    <state>$PROJ_DIR$\../build_tmp/generated/include</state>
                </option>
                <option>
                    <name>AsmDefines</name>
                    <state>FLASH_XIP=1</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLIC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCHTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLICSW=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_UART=y</state>
                    <state>HPMSOC_HAS_HPMSDK_I2C=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SPI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_CRC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TSNS=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MBX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_EWDG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAMUX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIOM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCAN=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PTPC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEIV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MMC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PWM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RDC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYNT=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TRGM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_USB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SDP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MON=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RNG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OTP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_KEYM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ADC16=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DAC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OPAMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ACMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYSCTL=y</state>
                    <state>HPMSOC_HAS_HPMSDK_IOC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLLCTLV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PPOR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PCFG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PGPR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PDGO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PMP=y</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ACodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>demo.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>demo.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state>_flash_size=1M</state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\iar\flash_xip.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoVectorSetupSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILINKStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ILinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCoDenseJal</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Uninitialized###</state>
                </option>
            </data>
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>GDeviceSelect</name>
                    <state>HPM5361xCBx	HPMicro HPM5361xCBx</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Release\BrowseInfo</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>1</version>
                    <state>3</state>
                </option>
                <option>
                    <name>GRTDescription</name>
                    <state>A compact configuration of the C/C++14 runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>GRTConfigPath</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>1</version>
                    <state>3</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
                <option>
                    <name>GGeneralStack</name>
                    <state>0x4000</state>
                </option>
                <option>
                    <name>GHeapSize</name>
                    <state>0x4000</state>
                </option>
                <option>
                    <name>GNumCores</name>
                    <state></state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>GDeviceSelectSlave</name>
                    <state>RV32	RV32</state>
                </option>
                <option>
                    <name>GGeneralAutoVectorSetup</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCoreIBASRadioSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceMultSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceAtomicSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCompactSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceFloatSelectSlave</name>
                    <version>1</version>
                    <state></state>
                </option>
                <option>
                    <name>GCoreDevice</name>
                    <state>rv32imac_Zba_Zbb_Zbc_Zbs</state>
                </option>
                <option>
                    <name>RadioStdOutErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioLibLowLev</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceUserLvlIntSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipASlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipBSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXandesperfSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceBitmanipSSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Release\</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCountZeroesSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GCodeModelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseJalSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXZenVBitfieldsSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceNXPVirgoSupervisorSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceResumableNMISlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDevicePackedSIMDZpsfoperandSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceDspRadioSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCacheManagementSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCachePrefetchSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheZeroSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheEswinSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>8</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ICore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>2</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCNoSizeConst</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>2</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>0</version>
                    <state>1111111</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptions</name>
                    <state>--diag_suppress Pa089</state>
                    <state>--diag_suppress Pe236</state>
                    <state>--diag_suppress Pe188</state>
                    <state>--diag_suppress Pe546</state>
                    <state>--diag_suppress Pe111</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    <state>FLASH_XIP=1</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLIC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCHTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLICSW=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_UART=y</state>
                    <state>HPMSOC_HAS_HPMSDK_I2C=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SPI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_CRC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TSNS=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MBX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_EWDG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAMUX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIOM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCAN=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PTPC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEIV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MMC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PWM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RDC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYNT=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TRGM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_USB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SDP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MON=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RNG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OTP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_KEYM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ADC16=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DAC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OPAMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ACMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYSCTL=y</state>
                    <state>HPMSOC_HAS_HPMSDK_IOC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLLCTLV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PPOR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PCFG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PGPR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PDGO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PMP=y</state>
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/arch</state>
                    <state>$PROJ_DIR$\../../../../../../boards/hpm5321</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/drivers/inc</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/utils</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/components/debug_console</state>
                    <state>$PROJ_DIR$\../build_tmp/generated/include</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ICodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IASMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AsmCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/arch</state>
                    <state>$PROJ_DIR$\../../../../../../boards/hpm5321</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/drivers/inc</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/utils</state>
                    <state>$PROJ_DIR$\../../hpm_sdk_localized_for_hpm5321/components/debug_console</state>
                    <state>$PROJ_DIR$\../build_tmp/generated/include</state>
                </option>
                <option>
                    <name>AsmDefines</name>
                    <state>FLASH_XIP=1</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLIC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCHTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLICSW=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPTMR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_UART=y</state>
                    <state>HPMSOC_HAS_HPMSDK_I2C=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SPI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_CRC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TSNS=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MBX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_EWDG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAMUX=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DMAV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_GPIOM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MCAN=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PTPC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEIV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_QEO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MMC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PWM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RDC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYNT=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEI=y</state>
                    <state>HPMSOC_HAS_HPMSDK_TRGM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_USB=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SDP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SEC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_MON=y</state>
                    <state>HPMSOC_HAS_HPMSDK_RNG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OTP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_KEYM=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ADC16=y</state>
                    <state>HPMSOC_HAS_HPMSDK_DAC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_OPAMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_ACMP=y</state>
                    <state>HPMSOC_HAS_HPMSDK_SYSCTL=y</state>
                    <state>HPMSOC_HAS_HPMSDK_IOC=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PLLCTLV2=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PPOR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PCFG=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PGPR=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PDGO=y</state>
                    <state>HPMSOC_HAS_HPMSDK_PMP=y</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ACodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>demo.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>demo.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state>_flash_size=1M</state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\iar\flash_xip.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoVectorSetupSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILINKStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ILinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCoDenseJal</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Uninitialized###</state>
                </option>
            </data>
        </settings>
    </configuration>
      <group><name>application</name>
    <group><name>src</name>
      <file><name>E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/src/spi.c</name></file>
    </group>
  </group>
  <group><name>soc</name>
    <group><name>HPM5300</name>
      <group><name>HPM5361</name>
        <group><name>toolchains</name>
          <group><name>iar</name>
            <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\iar\startup.s</name></file>
          </group>
          <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\reset.c</name></file>
          <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\trap.c</name></file>
        </group>
        <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\system.c</name></file>
        <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_sysctl_drv.c</name></file>
        <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_l1c_drv.c</name></file>
        <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_clock_drv.c</name></file>
        <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_otp_drv.c</name></file>
        <group><name>boot</name>
          <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\boot\hpm_bootheader.c</name></file>
        </group>
      </group>
    </group>
  </group>
  <group><name>boards</name>
    <group><name>hpm5321</name>
      <file><name>E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/pinmux.c</name></file>
      <file><name>E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/board.c</name></file>
    </group>
  </group>
  <group><name>drivers</name>
    <group><name>src</name>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_uart_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_sdp_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_i2c_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_pmp_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_rng_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_gpio_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_spi_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_gptmr_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_pwm_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_pllctlv2_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_usb_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_acmp_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_adc16_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_pcfg_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_ptpc_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_mchtmr_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_tsns_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_dac_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_crc_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_mcan_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_qeiv2_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_enc_pos_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_sei_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_qeo_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_rdc_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_mmc_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_dmav2_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_ewdg_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_plb_drv.c</name></file>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\drivers\src\hpm_opamp_drv.c</name></file>
    </group>
  </group>
  <group><name>utils</name>
    <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\utils\hpm_sbrk.c</name></file>
    <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\utils\hpm_swap.c</name></file>
    <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\utils\hpm_ffssi.c</name></file>
    <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\utils\hpm_crc32.c</name></file>
  </group>
  <group><name>components</name>
    <group><name>debug_console</name>
      <file><name>$PROJ_DIR$\..\..\hpm_sdk_localized_for_hpm5321\components\debug_console\hpm_debug_console.c</name></file>
    </group>
  </group>

</project>