/***********************************************************************************************************************************/
/*COMPENSATION.C                                                                                                                 */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*GNSSlocusGen.m                                                                  */
/*  加计陀螺神经网络（X,Y,Z三个轴）参数                      */
/*******************************************************************************************************************************************/
//#include "appmain.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
//#include <math.h>
//#include <string.h>
#include <EXTERNGLOBALDATA.h>
#include "SetParaBao.h"





/****************************************************************************************************/
/*AccANNCompen_Z_Init                                                                             */
/*Z                                                                     */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                              */
/*                                                                                              */
/*                                                                                            */
/*1                                                                                                   */
/*                                                                                                  */
/************************************************************************************************************/
void ANNCompen_Init()
{
	GyroANNCompen_X_Init(&g_GyroANNCompen.ANNCompen_X);
	GyroANNCompen_Y_Init(&g_GyroANNCompen.ANNCompen_Y);
	GyroANNCompen_Z_Init(&g_GyroANNCompen.ANNCompen_Z);
	
	AccANNCompen_X_Init(&g_AccANNCompen.ANNCompen_X);
	AccANNCompen_Y_Init(&g_AccANNCompen.ANNCompen_Y);
	AccANNCompen_Z_Init(&g_AccANNCompen.ANNCompen_Z);
}




/****************************************************************************************************/
/*????:GyroANNCompen_X_Init                                                                                */
/*??????:X?????????                                                                                        */
/*???:  Ver 0.1                                                                                            */
/*????/??:                                                                                                 */
/*???:                                                                                                     */
/*????:?                                                                                                   */
/*????:?                                                                                                   */
/*????:??                                                                                                  */
/*??1:                                                                                                     */
/*???:                                                                                                      */
/************************************************************************************************************/
void GyroANNCompen_X_Init(p_ANNCompen lp_GyroANNCompen_X)
{
	IPARA i;
	//DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
 //                                                           1.821236908435821533e-01,1.097935914993286133e+00,8.922839164733886719e-01,3.009150326251983643e-01,-9.068603515625000000e-01,2.770628929138183594e-01,8.066113293170928955e-02,-1.294697914272546768e-02,
 //                                                           -3.282900154590606689e-01,4.850132763385772705e-01,1.065583303570747375e-01,4.433609843254089355e-01,-3.982755839824676514e-01,-4.471818506717681885e-01,-8.965563774108886719e-01,5.100839734077453613e-01
 //                                                        };
        DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.GyroNerve_X2Para.ANN_Dense_1_16
                                                         };

        //DPARA Dense1_Bias[DENSE_1_CELL_NUM]            = {
        //                                                    -1.104652211070060730e-01,
        //                                                    -1.037462726235389709e-01,
        //                                                    2.613495476543903351e-02,
        //                                                    -2.991047315299510956e-02,
        //                                                    7.149823755025863647e-02,
        //                                                    -3.372882306575775146e-02,
        //                                                    2.623285911977291107e-02,
        //                                                    2.087808996438980103e-01
        //                                                 };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM]            = {
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_1_8
                                                         };

        //DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
        //                                                    -2.474243193864822388e-01,1.250532388687133789e+00,-2.447439908981323242e+00,5.616603493690490723e-01,
        //                                                    4.687386155128479004e-01,1.236756816506385803e-01,-1.819990992546081543e+00,-5.432187318801879883e-01,
        //                                                    1.534589380025863647e-01,3.174656033515930176e-01,1.269648432731628418e+00,4.548846483230590820e-01,
        //                                                    -3.711236715316772461e-01,1.568071544170379639e-01,9.495952129364013672e-01,-2.195461839437484741e-01,
        //                                                    1.240634545683860779e-01,-3.107500970363616943e-01,7.581135630607604980e-02,-1.221293568611145020e+00,
        //                                                    9.478904604911804199e-01,3.325160145759582520e-01,-8.613536953926086426e-01,1.172637268900871277e-01,
        //                                                    -1.580902189016342163e-01,-8.975336551666259766e-01,1.295670121908187866e-01,-1.124553158879280090e-01,
        //                                                    -1.205653667449951172e+00,5.936180800199508667e-02,2.937989681959152222e-02,5.057379007339477539e-01
        //                                                    };
        DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_2_84
                                                            };

        //DPARA Dense2_Bias[DENSE_2_CELL_NUM]               = {
        //                                                    -6.619740743190050125e-03,
        //                                                    -4.621553048491477966e-02,
        //                                                    9.537152945995330811e-02,
        //                                                    1.272032260894775391e-01
        //                                                    };

        DPARA Dense2_Bias[DENSE_2_CELL_NUM]               = {
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_2_4
                                                            };

        //DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
        //                                                    -1.425761699676513672e+00,
        //                                                    -1.378338813781738281e+00,
        //                                                    -1.466918110847473145e+00,
        //                                                    7.153360843658447266e-01
        //                                                  };
        DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.GyroNerve_X1Para.ANN_Dense_3_4,
                                                          };

        //lp_GyroANNCompen_X -> Dense3_Bias               =       1.693286001682281494e-01 ;
          lp_GyroANNCompen_X -> Dense3_Bias               =       stSetPara.TemperCompenData.GyroNerve_X0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_X -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	

        //lp_GyroANNCompen_X -> Normalized_Temp_Max =83.25;
        //lp_GyroANNCompen_X -> Normalized_Temp_Min =-39.3125;
        //lp_GyroANNCompen_X -> Normalized_Temp_Mean =0.51864949252337;
        //lp_GyroANNCompen_X -> Normalized_Temp_Diff_Max =1.776323750000003;
        //lp_GyroANNCompen_X -> Normalized_Temp_Diff_Min =-1.9038387500000056;
        //lp_GyroANNCompen_X -> Normalized_Temp_Diff_Mean =0.5186955224127422;
        //lp_GyroANNCompen_X -> Normalized_Output_Max =0.004669904716197622;
        //lp_GyroANNCompen_X -> Normalized_Output_Min =0.0031348055856419976;
        //lp_GyroANNCompen_X -> Normalized_Output_Mean =0.3666060772373639;
        lp_GyroANNCompen_X -> Normalized_Temp_Max =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempmax;
        lp_GyroANNCompen_X -> Normalized_Temp_Min =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempmin;
        lp_GyroANNCompen_X -> Normalized_Temp_Mean =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempmean;
        lp_GyroANNCompen_X -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempdiffmax;
        lp_GyroANNCompen_X -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempdiffmin;
        lp_GyroANNCompen_X -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.GyroNerve_X2Para.ntempdiffmean;
        lp_GyroANNCompen_X -> Normalized_Output_Max =stSetPara.TemperCompenData.GyroNerve_X2Para.nrowmax;
        lp_GyroANNCompen_X -> Normalized_Output_Min =stSetPara.TemperCompenData.GyroNerve_X2Para.nrowmin;
        lp_GyroANNCompen_X -> Normalized_Output_Mean =stSetPara.TemperCompenData.GyroNerve_X2Para.nrowmean;


	//lp_GyroANNCompen_X -> Correct_Value = 3.791381947209071e-03;
        lp_GyroANNCompen_X -> Correct_Value = stSetPara.TemperCompenData.GyroNerve_X2Para.biascv;
}

/*********************************************???????*******************************************************/
/*?????????GyroANNCompen_Y_Init                                                                            */
/*??????????????Y?????????????????                                                                         */
/*????  Ver 0.1                                                                                         */
/*??????/???                                                                                           */
/*?????                                                                                                  */
/*???????????                                                                                              */
/*???????????                                                                                              */
/*?????????????                                                                                            */
/*???1??                                                                                                   */
/*???????                                                                                                  */
/************************************************************************************************************/
void GyroANNCompen_Y_Init(p_ANNCompen lp_GyroANNCompen_Y)
{
	IPARA i;
      	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.GyroNerve_Y2Para.ANN_Dense_1_16
                                                            };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_1_8
                                                            };
        DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_2_84
                                                            };
        DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_2_4
                                                            };
        DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Y1Para.ANN_Dense_3_4,
                                                            };
        lp_GyroANNCompen_Y ->Dense3_Bias = stSetPara.TemperCompenData.GyroNerve_Y0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_Y -> Dense3_Mat[i] = Dense3_Mat[i];
	}

        //lp_GyroANNCompen_Y -> Normalized_Temp_Max =82.625;
        //lp_GyroANNCompen_Y -> Normalized_Temp_Min =-40.549125;
        //lp_GyroANNCompen_Y -> Normalized_Temp_Mean =0.5174329524654273;
        //lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Max =1.7909962499999992;
        //lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Min =-1.9111106249999992;
        //lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Mean =0.5177404115815234;
        //lp_GyroANNCompen_Y -> Normalized_Output_Max =-0.00032296288568025254;
        //lp_GyroANNCompen_Y -> Normalized_Output_Min =-0.0015407464318433766;
        //lp_GyroANNCompen_Y -> Normalized_Output_Mean =0.5065172269441219;
        lp_GyroANNCompen_Y -> Normalized_Temp_Max =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempmax;
        lp_GyroANNCompen_Y -> Normalized_Temp_Min =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempmin;
        lp_GyroANNCompen_Y -> Normalized_Temp_Mean =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempmean;
        lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempdiffmax;
        lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempdiffmin;
        lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.GyroNerve_Y2Para.ntempdiffmean;
        lp_GyroANNCompen_Y -> Normalized_Output_Max =stSetPara.TemperCompenData.GyroNerve_Y2Para.nrowmax;
        lp_GyroANNCompen_Y -> Normalized_Output_Min =stSetPara.TemperCompenData.GyroNerve_Y2Para.nrowmin;
        lp_GyroANNCompen_Y -> Normalized_Output_Mean =stSetPara.TemperCompenData.GyroNerve_Y2Para.nrowmean;
	
	

	//lp_GyroANNCompen_Y -> Correct_Value = -1.017192067783518e-03;
        lp_GyroANNCompen_Y -> Correct_Value = stSetPara.TemperCompenData.GyroNerve_Y2Para.biascv;
}

/****************************************************************************************************/
/*????:GyroANNCompen_Z_Init                                                                            */
/*??????:Z?????????                                                                         */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
void GyroANNCompen_Z_Init(p_ANNCompen lp_GyroANNCompen_Z)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.GyroNerve_Z2Para.ANN_Dense_1_16
                                                          };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_1_8
                                                          };
      DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_2_84
                                                          };
      DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_2_4
                                                          };
      DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.GyroNerve_Z1Para.ANN_Dense_3_4,
                                                          };
      lp_GyroANNCompen_Z -> Dense3_Bias = stSetPara.TemperCompenData.GyroNerve_Z0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_Z -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
        //lp_GyroANNCompen_Z -> Normalized_Temp_Max =83.375;
        //lp_GyroANNCompen_Z -> Normalized_Temp_Min =-39.6875;
        //lp_GyroANNCompen_Z -> Normalized_Temp_Mean =0.5162538132488862;
        //lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Max =1.7983812500000056;
        //lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Min =-1.903713124999996;
        //lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Mean =0.515840558799877;
        //lp_GyroANNCompen_Z -> Normalized_Output_Max =0.002121583134988697;
        //lp_GyroANNCompen_Z -> Normalized_Output_Min =0.0011065712682015736;
        //lp_GyroANNCompen_Z -> Normalized_Output_Mean =0.44640655370767224;
        lp_GyroANNCompen_Z -> Normalized_Temp_Max =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempmax;
        lp_GyroANNCompen_Z -> Normalized_Temp_Min =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempmin;
        lp_GyroANNCompen_Z -> Normalized_Temp_Mean =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempmean;
        lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempdiffmax;
        lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempdiffmin;
        lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.GyroNerve_Z2Para.ntempdiffmean;
        lp_GyroANNCompen_Z -> Normalized_Output_Max =stSetPara.TemperCompenData.GyroNerve_Z2Para.nrowmax;
        lp_GyroANNCompen_Z -> Normalized_Output_Min =stSetPara.TemperCompenData.GyroNerve_Z2Para.nrowmin;
        lp_GyroANNCompen_Z -> Normalized_Output_Mean =stSetPara.TemperCompenData.GyroNerve_Z2Para.nrowmean;


	//lp_GyroANNCompen_Z -> Correct_Value = 1.490840507425257e-03;
        lp_GyroANNCompen_Z -> Correct_Value = stSetPara.TemperCompenData.GyroNerve_Z2Para.biascv;
}

/*********************************************???????*******************************************************/
/*?????????AccANNCompen_X_Init                                                                            */
/*??????????????X???????????????????                                                                         */
/*????  Ver 0.1                                                                                         */
/*??????/???                                                                                           */
/*?????                                                                                                  */
/*???????????                                                                                              */
/*???????????                                                                                              */
/*?????????????                                                                                            */
/*???1??                                                                                                   */
/*???????                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_X_Init(p_ANNCompen lp_AccANNCompen_X)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.AccNerve_X2Para.ANN_Dense_1_16
                                                            };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_1_8
                                                            };
        DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_2_84
                                                            };
        DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_2_4
                                                            };
        DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.AccNerve_X1Para.ANN_Dense_3_4
                                                            };
        lp_AccANNCompen_X -> Dense3_Bias =      stSetPara.TemperCompenData.AccNerve_X0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_X -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
        //lp_AccANNCompen_X -> Normalized_Temp_Max =81.40300625;
        //lp_AccANNCompen_X -> Normalized_Temp_Min =-46.7502875;
        //lp_AccANNCompen_X -> Normalized_Temp_Mean =0.519156879860803;
        //lp_AccANNCompen_X -> Normalized_Temp_Diff_Max =1.8766851562499909;
        //lp_AccANNCompen_X -> Normalized_Temp_Diff_Min =-1.9579447499999958;
        //lp_AccANNCompen_X -> Normalized_Temp_Diff_Mean =0.5120364419680857;
        //lp_AccANNCompen_X -> Normalized_Output_Max =-0.009549628512147428;
        //lp_AccANNCompen_X -> Normalized_Output_Min =-0.010574501552221002;
        //lp_AccANNCompen_X -> Normalized_Output_Mean =0.4825083972831216;
        lp_AccANNCompen_X -> Normalized_Temp_Max =stSetPara.TemperCompenData.AccNerve_X2Para.ntempmax;
        lp_AccANNCompen_X -> Normalized_Temp_Min =stSetPara.TemperCompenData.AccNerve_X2Para.ntempmin;
        lp_AccANNCompen_X -> Normalized_Temp_Mean =stSetPara.TemperCompenData.AccNerve_X2Para.ntempmean;
        lp_AccANNCompen_X -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.AccNerve_X2Para.ntempdiffmax;
        lp_AccANNCompen_X -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.AccNerve_X2Para.ntempdiffmin;
        lp_AccANNCompen_X -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.AccNerve_X2Para.ntempdiffmean;
        lp_AccANNCompen_X -> Normalized_Output_Max =stSetPara.TemperCompenData.AccNerve_X2Para.nrowmax;
        lp_AccANNCompen_X -> Normalized_Output_Min =stSetPara.TemperCompenData.AccNerve_X2Para.nrowmin;
        lp_AccANNCompen_X -> Normalized_Output_Mean =stSetPara.TemperCompenData.AccNerve_X2Para.nrowmean;


	//lp_AccANNCompen_X -> Correct_Value = -8.980970234927410e-03;
        lp_AccANNCompen_X -> Correct_Value = stSetPara.TemperCompenData.AccNerve_X2Para.biascv;
}

/*********************************************???????*******************************************************/
/*?????????AccANNCompen_Y_Init                                                                            */
/*??????????????Y???????????????????                                                                         */
/*????  Ver 0.1                                                                                         */
/*??????/???                                                                                           */
/*?????                                                                                                  */
/*???????????                                                                                              */
/*???????????                                                                                              */
/*?????????????                                                                                            */
/*???1??                                                                                                   */
/*???????                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_Y_Init(p_ANNCompen lp_AccANNCompen_Y)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.AccNerve_Y2Para.ANN_Dense_1_16
                                                            };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_1_8
                                                            };
        DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_2_84
                                                            };
        DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_2_4
                                                            };
        DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.AccNerve_Y1Para.ANN_Dense_3_4
                                                            };
        lp_AccANNCompen_Y -> Dense3_Bias =      stSetPara.TemperCompenData.AccNerve_Y0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_Y -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
        //lp_AccANNCompen_Y -> Normalized_Temp_Max =81.40300625;
        //lp_AccANNCompen_Y -> Normalized_Temp_Min =-46.7502875;
        //lp_AccANNCompen_Y -> Normalized_Temp_Mean =0.519156879860803;
        //lp_AccANNCompen_Y -> Normalized_Temp_Diff_Max =1.8766851562499909;
        //lp_AccANNCompen_Y -> Normalized_Temp_Diff_Min =-1.9579447499999958;
        //lp_AccANNCompen_Y -> Normalized_Temp_Diff_Mean =0.5120364419680857;
        //lp_AccANNCompen_Y -> Normalized_Output_Max =0.00174564094415846;
        //lp_AccANNCompen_Y -> Normalized_Output_Min =-0.0003242309209411611;
        //lp_AccANNCompen_Y -> Normalized_Output_Mean =0.5162623200010672;
        lp_AccANNCompen_Y -> Normalized_Temp_Max =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempmax;
        lp_AccANNCompen_Y -> Normalized_Temp_Min =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempmin;
        lp_AccANNCompen_Y -> Normalized_Temp_Mean =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempmean;
        lp_AccANNCompen_Y -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempdiffmax;
        lp_AccANNCompen_Y -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempdiffmin;
        lp_AccANNCompen_Y -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.AccNerve_Y2Para.ntempdiffmean;
        lp_AccANNCompen_Y -> Normalized_Output_Max =stSetPara.TemperCompenData.AccNerve_Y2Para.nrowmax;
        lp_AccANNCompen_Y -> Normalized_Output_Min =stSetPara.TemperCompenData.AccNerve_Y2Para.nrowmin;
        lp_AccANNCompen_Y -> Normalized_Output_Mean =stSetPara.TemperCompenData.AccNerve_Y2Para.nrowmean;

	//lp_AccANNCompen_Y -> Correct_Value = 7.182322704847035e-05;
        lp_AccANNCompen_Y -> Correct_Value = stSetPara.TemperCompenData.AccNerve_Y2Para.biascv;
}

/****************************************************************************************************/
/*????:AccANNCompen_Z_Init                                                                             */
/*??????:Z???????????                                                                     */
/*???:  Ver 0.1                                                                                         */
/*????/??:                                                                                           */
/*???:                                                                                                  */
/*????:?                                                                                              */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:                                                                                                   */
/*???:                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_Z_Init(p_ANNCompen lp_AccANNCompen_Z)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_1,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_2,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_3,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_4,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_5,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_6,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_7,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_8,
                                                            stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_9,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_10,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_11,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_12,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_13,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_14,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_15,stSetPara.TemperCompenData.AccNerve_Z2Para.ANN_Dense_1_16
                                                          };
        DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_1,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_2,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_3,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_4,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_5,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_6,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_7,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_1_8
                                                          };
        DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_11,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_12,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_13,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_14,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_21,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_22,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_23,stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Dense_2_24,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_31,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_32,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_33,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_34,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_41,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_42,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_43,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_44,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_51,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_52,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_53,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_54,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_61,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_62,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_63,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_64,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_71,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_72,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_73,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_74,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_81,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_82,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_83,stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_2_84
                                                          };
        DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_2_1,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_2_2,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_2_3,
                                                            stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_2_4
                                                          };
        DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_3_1,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_3_2,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_3_3,
                                                            stSetPara.TemperCompenData.AccNerve_Z1Para.ANN_Dense_3_4
                                                          };
        lp_AccANNCompen_Z -> Dense3_Bias =      stSetPara.TemperCompenData.AccNerve_Z0Para.ANN_Bias_3;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_Z -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
        //lp_AccANNCompen_Z -> Normalized_Temp_Max =81.40300625;
        //lp_AccANNCompen_Z -> Normalized_Temp_Min =-46.7502875;
        //lp_AccANNCompen_Z -> Normalized_Temp_Mean =0.519156879860803;
        //lp_AccANNCompen_Z -> Normalized_Temp_Diff_Max =1.8766851562499909;
        //lp_AccANNCompen_Z -> Normalized_Temp_Diff_Min =-1.9579447499999958;
        //lp_AccANNCompen_Z -> Normalized_Temp_Diff_Mean =0.5120364419680857;
        //lp_AccANNCompen_Z -> Normalized_Output_Max =1.000839730614993;
        //lp_AccANNCompen_Z -> Normalized_Output_Min =0.9992623792165084;
        //lp_AccANNCompen_Z -> Normalized_Output_Mean =0.4275915616761939;
        lp_AccANNCompen_Z -> Normalized_Temp_Max =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempmax;
        lp_AccANNCompen_Z -> Normalized_Temp_Min =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempmin;
        lp_AccANNCompen_Z -> Normalized_Temp_Mean =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempmean;
        lp_AccANNCompen_Z -> Normalized_Temp_Diff_Max =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempdiffmax;
        lp_AccANNCompen_Z -> Normalized_Temp_Diff_Min =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempdiffmin;
        lp_AccANNCompen_Z -> Normalized_Temp_Diff_Mean =stSetPara.TemperCompenData.AccNerve_Z2Para.ntempdiffmean;
        lp_AccANNCompen_Z -> Normalized_Output_Max =stSetPara.TemperCompenData.AccNerve_Z2Para.nrowmax;
        lp_AccANNCompen_Z -> Normalized_Output_Min =stSetPara.TemperCompenData.AccNerve_Z2Para.nrowmin;
        lp_AccANNCompen_Z -> Normalized_Output_Mean =stSetPara.TemperCompenData.AccNerve_Z2Para.nrowmean;

	//lp_AccANNCompen_Z -> Correct_Value = 1.000440757218046e+00;
        lp_AccANNCompen_Z -> Correct_Value = stSetPara.TemperCompenData.AccNerve_Z2Para.biascv;
}
/*********************************************???????*******************************************************/
/*????????ANN_Predcit                                                                                       */
/*?????????????????????????                                                                                */
/*????  Ver 0.1                                                                                         */
/*??????/???                                                                                           */
/*?????                                                                                                  */
/*???????????                                                                                              */
/*???????????                                                                                              */
/*?????????????                                                                                            */
/*???1??                                                                                                   */
/*???????                                                                                                  */
/************************************************************************************************************/
DPARA ANN_Predict(DPARA Temp,DPARA Temp_Diff,p_ANNCompen lp_ANNCompen)
{
    IPARA i;
    DPARA Output1[DENSE_1_CELL_NUM] = {0};
    DPARA Output2[DENSE_2_CELL_NUM] = {0};
    DPARA Output3 = 0.0;
    DPARA RT_Bias;
    //DPARA Output4 = 0.0;

    DPARA Normalized_Temp, Normalized_Temp_Diff;
    DPARA Input1[2];
    //
    Normalized_Temp = (Temp - lp_ANNCompen -> Normalized_Temp_Min) / (lp_ANNCompen -> Normalized_Temp_Max - lp_ANNCompen -> Normalized_Temp_Min);
    Normalized_Temp = Normalized_Temp - lp_ANNCompen -> Normalized_Temp_Mean;

    Normalized_Temp_Diff = (Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Min) / (lp_ANNCompen->Normalized_Temp_Diff_Max - lp_ANNCompen->Normalized_Temp_Diff_Min);
    Normalized_Temp_Diff = Normalized_Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Mean;

    Input1[0] = Normalized_Temp;
    Input1[1] = Normalized_Temp_Diff;
    //
    /*for(i = 0; i < DENSE_1_CELL_NUM;i++)
    {
        Output1[i] = Normalized_Temp * lp_ANNCompen -> Dense1_Mat[i] + lp_ANNCompen -> Dense1_Bias[i];
    }*/
    Mat_Mul(Input1, (double *)lp_ANNCompen -> Dense1_Mat, Output1, 1, INPUT_DIM, DENSE_1_CELL_NUM);
    for (i = 0; i < DENSE_1_CELL_NUM; i++)
    {
        Output1[i] += lp_ANNCompen->Dense1_Bias[i];
    }
    Relu(Output1, DENSE_1_CELL_NUM);
    //
    Mat_Mul(Output1, (double *)lp_ANNCompen -> Dense2_Mat, Output2, 1,DENSE_1_CELL_NUM, DENSE_2_CELL_NUM);
    for(i = 0;i < DENSE_2_CELL_NUM;i++)
    {
        Output2[i] += lp_ANNCompen -> Dense2_Bias[i];
    }
    Relu(Output2, DENSE_2_CELL_NUM);
    //
    MultiDim_Vec_Dot(Output2, (double *)lp_ANNCompen -> Dense3_Mat, &Output3,DENSE_2_CELL_NUM);
    Output3 = Output3 + lp_ANNCompen -> Dense3_Bias;
    RT_Bias = (Output3 + lp_ANNCompen -> Normalized_Output_Mean) * (lp_ANNCompen -> Normalized_Output_Max - lp_ANNCompen -> Normalized_Output_Min) + lp_ANNCompen -> Normalized_Output_Min;
    RT_Bias = RT_Bias - lp_ANNCompen -> Correct_Value;
    return RT_Bias;
}

