//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.23
//---------------------------------------------------------

#include "uart_dma.h"

//RS232
ATTR_PLACE_AT_NONCACHEABLE uint8_t uart_Txbuff[TEST_BUFFER_SIZE]={0};
ATTR_PLACE_AT_NONCACHEABLE uint8_t uart_Rxbuff[TEST_BUFFER_SIZE]={0};
volatile bool uart_tx_dma_done=true;
volatile bool uart_rx_dma_done;


hpm_stat_t uart_tx_trigger_dma(DMA_Type *dma_ptr,
                    uint8_t ch_num,
                    UART_Type *uart_ptr,
                    uint32_t src,
                    uint32_t size)
{
    dma_handshake_config_t config;

    dma_default_handshake_config(dma_ptr, &config);
    config.ch_index = ch_num;
    config.dst = (uint32_t)&uart_ptr->THR;
    config.dst_fixed = true;
    config.src = src;
    config.src_fixed = false;
    config.data_width = DMA_TRANSFER_WIDTH_BYTE;
    config.size_in_byte = size;

    return dma_setup_handshake(dma_ptr, &config, true);
}

hpm_stat_t uart_rx_trigger_dma(DMA_Type *dma_ptr,
                    uint8_t ch_num,
                    UART_Type *uart_ptr,
                    uint32_t dst,
                    uint32_t size)
{
    dma_handshake_config_t config;

    dma_default_handshake_config(dma_ptr, &config);
    config.ch_index = ch_num;
    config.dst = dst;
    config.dst_fixed = false;
    config.src = (uint32_t)&uart_ptr->RBR;
    config.src_fixed = true;
    config.data_width = DMA_TRANSFER_WIDTH_BYTE;
    config.size_in_byte = size;

    return dma_setup_handshake(dma_ptr, &config, true);
}

void UartDMASend(char *txbuf, int size)
{
    memcpy(uart_Txbuff, txbuf, size);
    uart_tx_trigger_dma(TEST_UART_DMA_CONTROLLER,
                          TEST_UART_TX_DMA_CHN,
                          TEST_UART,
                          core_local_mem_to_sys_address(BOARD_RUNNING_CORE, (uint32_t)uart_Txbuff),
                          size);
}


void UartDMARecStart(int BagSize)
{
    uart_rx_trigger_dma(TEST_UART_DMA_CONTROLLER,
                          TEST_UART_RX_DMA_CHN,
                          TEST_UART,
                          core_local_mem_to_sys_address(BOARD_RUNNING_CORE, (uint32_t)uart_Rxbuff),
                          BagSize);
}

void dma_isr(void)
{
    volatile hpm_stat_t stat_rx_chn, stat_tx_chn;
    stat_rx_chn = dma_check_transfer_status(TEST_UART_DMA_CONTROLLER, TEST_UART_RX_DMA_CHN);
    if(stat_rx_chn & DMA_CHANNEL_STATUS_TC)
    {
        uart_rx_dma_done = true;
    }


    stat_tx_chn = dma_check_transfer_status(TEST_UART_DMA_CONTROLLER, TEST_UART_TX_DMA_CHN);
    if(stat_tx_chn & DMA_CHANNEL_STATUS_TC)
    {
        uart_tx_dma_done = true;
    }
}
SDK_DECLARE_EXT_ISR_M(TEST_UART_DMA_IRQ, dma_isr)

void UartDMAInit(void)
{
    hpm_stat_t stat;
    uart_config_t config = {0};

    printf("UART DMA example\n");
    printf("UART will send back received characters, echo every %d bytes\n", TEST_BUFFER_SIZE);

    board_init_uart(TEST_UART);//rs232

    uart_default_config(TEST_UART, &config);
    config.fifo_enable = true;
    config.dma_enable = true;
    config.src_freq_in_hz = clock_get_frequency(TEST_UART_CLK_NAME);
    config.tx_fifo_level = uart_tx_fifo_trg_not_full;
    config.rx_fifo_level = uart_rx_fifo_trg_not_empty;
    stat = uart_init(TEST_UART, &config);

    intc_m_enable_irq_with_priority(TEST_UART_DMA_IRQ, 2);

    //rs232
    dmamux_config(TEST_UART_DMAMUX_CONTROLLER, TEST_UART_RX_DMAMUX_CHN, TEST_UART_RX_DMA_REQ, true);
    dmamux_config(TEST_UART_DMAMUX_CONTROLLER, TEST_UART_TX_DMAMUX_CHN, TEST_UART_TX_DMA_REQ, true);
}


