//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：Timer.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.25
//---------------------------------------------------------
#ifndef _TIMER_H
#define _TIMER_H

#include <stdio.h>
#include "board.h"
#include "hpm_sysctl_drv.h"
#include "hpm_gptmr_drv.h"
#include "hpm_debug_console.h"

#define APP_BOARD_GPTMR               BOARD_GPTMR//HPM_GPTMR0
#define APP_BOARD_GPTMR_CH            BOARD_GPTMR_CHANNEL //0通道
#define APP_BOARD_GPTMR_IRQ           BOARD_GPTMR_IRQ//IRQn_GPTMR0
#define APP_BOARD_GPTMR_CLOCK         BOARD_GPTMR_CLK_NAME//clock_gptmr0

#define APP_TICK_MS                   (5)//对应200Hz输出频率

extern uint8_t fpga_syn;

void Timer_Init(void);


#endif