//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart_Irq.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.24
//---------------------------------------------------------

#ifndef _UART_IRQ_H
#define _UART_IRQ_H

#include "board.h"
#include "hpm_clock_drv.h"
#include "hpm_uart_drv.h"

extern uint8_t g_StartUpdateFirm;// //开始升级标志 1:开始升级 0:结束升级

#define BUFFER_SIZE_TX                          (1024 * 1)
#define BUFFER_SIZE_RX                          (1024 * 1)
#define	U4RX_MAXCOUNT                           (1024 * 4)	


#define TEST_UART_MAX_BUFFER_SIZE           (20)
#define TEST_UART                           HPM_UART6
#define TEST_UART_IRQ                       IRQn_UART6
#define TEST_UART_CLK_NAME                  clock_uart6

extern uint32_t  g_baudrate;//串口波特率

void UartIrqSendMsg(char *txbuf, int size);
void analysisRxdata(void);
void UartIrqInit(void);


#endif