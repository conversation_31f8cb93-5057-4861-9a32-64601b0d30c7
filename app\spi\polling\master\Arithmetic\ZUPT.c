//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// 文件名称：ZUPT.c
// 文件标识：
// 文件摘要：ZUPT静止检测功能实现文件
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2025.01.15
//---------------------------------------------------------
#include "ZUPT.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

void ZUPTInit(p_ZUPT lp_ZUPT)
{
    // 初始化窗口管理
    lp_ZUPT->WindowIndex = 0;
    lp_ZUPT->SampleCount = 0;
    lp_ZUPT->isWindowFilled = 0;
    
    // 初始化滑动窗口数据
    for (int i = 0; i < ZUPT_WINDOW_SIZE; i++) {
        for (int j = 0; j < 3; j++) {
            lp_ZUPT->AccWindow[i][j] = 0.0f;
            lp_ZUPT->GyroWindow[i][j] = 0.0f;
        }
    }
    
    // 初始化增量统计量
    for (int j = 0; j < 3; j++) {
        lp_ZUPT->AccSum[j] = 0.0;
        lp_ZUPT->GyroSum[j] = 0.0;
        lp_ZUPT->AccSumSquare[j] = 0.0;
        lp_ZUPT->GyroSumSquare[j] = 0.0;
        lp_ZUPT->AccMean[j] = 0.0;
        lp_ZUPT->GyroMean[j] = 0.0;
        lp_ZUPT->AccVariance[j] = 0.0;
        lp_ZUPT->GyroVariance[j] = 0.0;
    }

    // 初始化合成幅值统计量
    lp_ZUPT->AccMagnitudeSum = 0.0;
    lp_ZUPT->GyroMagnitudeSum = 0.0;
    lp_ZUPT->AccMagnitudeSumSquare = 0.0;
    lp_ZUPT->GyroMagnitudeSumSquare = 0.0;
    lp_ZUPT->AccMagnitudeMean = 0.0;
    lp_ZUPT->GyroMagnitudeMean = 0.0;
    lp_ZUPT->AccMagnitudeVariance = 0.0;
    lp_ZUPT->GyroMagnitudeVariance = 0.0;
    
    // 初始化检测结果
    lp_ZUPT->ZUPTFlag = 0;
    lp_ZUPT->isZUPTValid = 0;
    
    // 初始化调试信息
    lp_ZUPT->DetectionCount = 0;
    lp_ZUPT->StaticCount = 0;
    lp_ZUPT->DynamicCount = 0;
}

void ZUPTDetection(p_CombineDataTypeDef lp_combineData)
{
    p_ZUPT lp_ZUPT = &g_ZUPT;
    
    // 获取当前IMU数据
    float current_acc[3] = {lp_combineData->accel_x, lp_combineData->accel_y, lp_combineData->accel_z};
    float current_gyro[3] = {lp_combineData->gyro_x, lp_combineData->gyro_y, lp_combineData->gyro_z};
    
    // 如果窗口已满，需要先减去要被覆盖的旧数据的贡献
    if (lp_ZUPT->isWindowFilled) {
        // 计算要被覆盖的旧数据的合成幅值
        float old_acc_magnitude = sqrt(lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][0] * lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][0] +
                                       lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][1] * lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][1] +
                                       lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][2] * lp_ZUPT->AccWindow[lp_ZUPT->WindowIndex][2]);

        float old_gyro_magnitude = sqrt(lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][0] * lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][0] +
                                        lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][1] * lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][1] +
                                        lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][2] * lp_ZUPT->GyroWindow[lp_ZUPT->WindowIndex][2]);

        // 减去旧合成幅值的贡献
        lp_ZUPT->AccMagnitudeSum -= old_acc_magnitude;
        lp_ZUPT->GyroMagnitudeSum -= old_gyro_magnitude;
        lp_ZUPT->AccMagnitudeSumSquare -= old_acc_magnitude * old_acc_magnitude;
        lp_ZUPT->GyroMagnitudeSumSquare -= old_gyro_magnitude * old_gyro_magnitude;
    }
    
    // 计算当前数据的合成幅值
    float current_acc_magnitude = sqrt(current_acc[0] * current_acc[0] +
                                       current_acc[1] * current_acc[1] +
                                       current_acc[2] * current_acc[2]);

    float current_gyro_magnitude = sqrt(current_gyro[0] * current_gyro[0] +
                                        current_gyro[1] * current_gyro[1] +
                                        current_gyro[2] * current_gyro[2]);

    // 增量更新合成幅值统计量
    lp_ZUPT->AccMagnitudeSum += current_acc_magnitude;
    lp_ZUPT->GyroMagnitudeSum += current_gyro_magnitude;
    lp_ZUPT->AccMagnitudeSumSquare += current_acc_magnitude * current_acc_magnitude;
    lp_ZUPT->GyroMagnitudeSumSquare += current_gyro_magnitude * current_gyro_magnitude;
    
    // 更新窗口管理
    lp_ZUPT->WindowIndex = (lp_ZUPT->WindowIndex + 1) % ZUPT_WINDOW_SIZE;
    
    if (!lp_ZUPT->isWindowFilled) {
        lp_ZUPT->SampleCount++;
        if (lp_ZUPT->SampleCount >= ZUPT_WINDOW_SIZE) {
            lp_ZUPT->isWindowFilled = 1;
        }
    }
    
    // 更新检测计数
    lp_ZUPT->DetectionCount++;
    
    // 只有在窗口填满后才进行静止检测
    if (lp_ZUPT->isWindowFilled) {
        lp_ZUPT->isZUPTValid = 1;
        
        // 使用增量方式计算均值和方差
        // 计算合成幅值的均值和方差
        lp_ZUPT->AccMagnitudeMean = lp_ZUPT->AccMagnitudeSum / ZUPT_WINDOW_SIZE;
        lp_ZUPT->GyroMagnitudeMean = lp_ZUPT->GyroMagnitudeSum / ZUPT_WINDOW_SIZE;

        lp_ZUPT->AccMagnitudeVariance = (lp_ZUPT->AccMagnitudeSumSquare / ZUPT_WINDOW_SIZE) -
                                        (lp_ZUPT->AccMagnitudeMean * lp_ZUPT->AccMagnitudeMean);
        lp_ZUPT->GyroMagnitudeVariance = (lp_ZUPT->GyroMagnitudeSumSquare / ZUPT_WINDOW_SIZE) -
                                         (lp_ZUPT->GyroMagnitudeMean * lp_ZUPT->GyroMagnitudeMean);
        
        // 判断是否处于静止状态
        // 使用加速度和角速度合成幅值的方差进行判断
        BOOL is_static = (lp_ZUPT->AccMagnitudeVariance <= ZUPT_ACC_THRESHOLD) &&
                         (lp_ZUPT->GyroMagnitudeVariance <= ZUPT_GYRO_THRESHOLD);
        
        // 更新ZUPT标志和统计
        lp_ZUPT->ZUPTFlag = is_static;
        if (is_static) {
            lp_ZUPT->StaticCount++;
        } else {
            lp_ZUPT->DynamicCount++;
        }
    } else {
        // 窗口未填满时，默认不是静止状态
        lp_ZUPT->isZUPTValid = 0;
        lp_ZUPT->ZUPTFlag = 0;
    }
}

int GetZUPTFlag(void)
{
    return g_ZUPT.ZUPTFlag;
}

BOOL GetZUPTValid(void)
{
    return g_ZUPT.isZUPTValid;
}

void GetZUPTStatistics(DPARA *acc_mean, DPARA *gyro_mean, DPARA *acc_variance, DPARA *gyro_variance)
{
    if (acc_mean != NULL) {
        for (int i = 0; i < 3; i++) {
            acc_mean[i] = g_ZUPT.AccMean[i];
        }
    }

    if (gyro_mean != NULL) {
        for (int i = 0; i < 3; i++) {
            gyro_mean[i] = g_ZUPT.GyroMean[i];
        }
    }

    if (acc_variance != NULL) {
        for (int i = 0; i < 3; i++) {
            acc_variance[i] = g_ZUPT.AccVariance[i];
        }
    }

    if (gyro_variance != NULL) {
        for (int i = 0; i < 3; i++) {
            gyro_variance[i] = g_ZUPT.GyroVariance[i];
        }
    }
}

void GetZUPTMagnitudeStatistics(DPARA *acc_magnitude_mean, DPARA *gyro_magnitude_mean,
                                DPARA *acc_magnitude_variance, DPARA *gyro_magnitude_variance)
{
    if (acc_magnitude_mean != NULL) {
        *acc_magnitude_mean = g_ZUPT.AccMagnitudeMean;
    }

    if (gyro_magnitude_mean != NULL) {
        *gyro_magnitude_mean = g_ZUPT.GyroMagnitudeMean;
    }

    if (acc_magnitude_variance != NULL) {
        *acc_magnitude_variance = g_ZUPT.AccMagnitudeVariance;
    }

    if (gyro_magnitude_variance != NULL) {
        *gyro_magnitude_variance = g_ZUPT.GyroMagnitudeVariance;
    }
}

void GetZUPTDebugInfo(COUNT *detection_count, COUNT *static_count, COUNT *dynamic_count)
{
    if (detection_count != NULL) {
        *detection_count = g_ZUPT.DetectionCount;
    }
    
    if (static_count != NULL) {
        *static_count = g_ZUPT.StaticCount;
    }
    
    if (dynamic_count != NULL) {
        *dynamic_count = g_ZUPT.DynamicCount;
    }
}

void SetZUPTThresholds(DPARA acc_threshold, DPARA gyro_threshold)
{
    // 使用预定义常量，此函数暂时为空
    (void)acc_threshold;
    (void)gyro_threshold;
}

void ResetZUPTStatistics(void)
{
    g_ZUPT.DetectionCount = 0;
    g_ZUPT.StaticCount = 0;
    g_ZUPT.DynamicCount = 0;
}

void ZUPTAngleConstraint(DELANG lp_DelSenbb[3])
{
    // 检查ZUPT标志位是否有效
    if (!GetZUPTFlag()) {
        // ZUPT标志位无效时，不进行角增量约束
        return;
    }

    // 对角增量使用缩放因子进行约束
    // 当设备处于静止状态时，角增量应该很小，通过缩放因子进一步减小
    for (int i = 0; i < 3; i++) {
        lp_DelSenbb[i] *= ZUPT_ANGLE_SCALE_FACTOR;
    }
}

void TestZUPTFunction(void)
{
    // 测试ZUPT功能的简单测试函数
    CombineDataTypeDef test_data = {0};
    DPARA acc_mean[3], gyro_mean[3], acc_variance[3], gyro_variance[3];

    // 初始化ZUPT结构体
    ZUPTInit(&g_ZUPT);

    // 模拟静止状态的IMU数据（小幅度变化）
    printf("Testing ZUPT function with static data...\n");

    // 填充窗口，模拟静止状态数据
    for (int i = 0; i < ZUPT_WINDOW_SIZE + 10; i++) {
        // 静止状态：加速度接近重力加速度，角速度接近0，加入小量噪声
        test_data.accel_x = 0.0f + (float)(rand() % 100 - 50) * 0.001f;  // ±0.05 m/s²
        test_data.accel_y = 0.0f + (float)(rand() % 100 - 50) * 0.001f;
        test_data.accel_z = 9.8f + (float)(rand() % 100 - 50) * 0.001f;

        test_data.gyro_x = 0.0f + (float)(rand() % 100 - 50) * 0.0001f;  // ±0.005 rad/s
        test_data.gyro_y = 0.0f + (float)(rand() % 100 - 50) * 0.0001f;
        test_data.gyro_z = 0.0f + (float)(rand() % 100 - 50) * 0.0001f;

        ZUPTDetection(&test_data);

        if (i >= ZUPT_WINDOW_SIZE) {
            DPARA acc_mag_mean, gyro_mag_mean, acc_mag_var, gyro_mag_var;
            GetZUPTStatistics(acc_mean, gyro_mean, acc_variance, gyro_variance);
            GetZUPTMagnitudeStatistics(&acc_mag_mean, &gyro_mag_mean, &acc_mag_var, &gyro_mag_var);
            printf("Sample %d: ZUPT_flag = %d, Valid = %d\n", i, GetZUPTFlag(), GetZUPTValid());
            printf("  Acc variance: [%.6f, %.6f, %.6f]\n", acc_variance[0], acc_variance[1], acc_variance[2]);
            printf("  Gyro variance: [%.6f, %.6f, %.6f]\n", gyro_variance[0], gyro_variance[1], gyro_variance[2]);
            printf("  Acc magnitude variance: %.6f, Gyro magnitude variance: %.6f\n", acc_mag_var, gyro_mag_var);
        }
    }

    printf("Testing ZUPT function with dynamic data...\n");

    // 重新初始化ZUPT结构体
    ZUPTInit(&g_ZUPT);

    // 填充窗口，模拟运动状态数据
    for (int i = 0; i < ZUPT_WINDOW_SIZE + 10; i++) {
        // 运动状态：较大的加速度和角速度变化
        test_data.accel_x = (float)(rand() % 2000 - 1000) * 0.01f;  // ±10 m/s²
        test_data.accel_y = (float)(rand() % 2000 - 1000) * 0.01f;
        test_data.accel_z = 9.8f + (float)(rand() % 2000 - 1000) * 0.01f;

        test_data.gyro_x = (float)(rand() % 2000 - 1000) * 0.001f;  // ±2 rad/s
        test_data.gyro_y = (float)(rand() % 2000 - 1000) * 0.001f;
        test_data.gyro_z = (float)(rand() % 2000 - 1000) * 0.001f;

        ZUPTDetection(&test_data);

        if (i >= ZUPT_WINDOW_SIZE) {
            DPARA acc_mag_mean, gyro_mag_mean, acc_mag_var, gyro_mag_var;
            GetZUPTStatistics(acc_mean, gyro_mean, acc_variance, gyro_variance);
            GetZUPTMagnitudeStatistics(&acc_mag_mean, &gyro_mag_mean, &acc_mag_var, &gyro_mag_var);
            printf("Sample %d: ZUPT_flag = %d, Valid = %d\n", i, GetZUPTFlag(), GetZUPTValid());
            printf("  Acc variance: [%.6f, %.6f, %.6f]\n", acc_variance[0], acc_variance[1], acc_variance[2]);
            printf("  Gyro variance: [%.6f, %.6f, %.6f]\n", gyro_variance[0], gyro_variance[1], gyro_variance[2]);
            printf("  Acc magnitude variance: %.6f, Gyro magnitude variance: %.6f\n", acc_mag_var, gyro_mag_var);
        }
    }

    printf("ZUPT function test completed.\n");
    printf("Total detections: %d, Static: %d, Dynamic: %d\n",
           g_ZUPT.DetectionCount, g_ZUPT.StaticCount, g_ZUPT.DynamicCount);
}

void TestZUPTAngleConstraint(void)
{
    printf("Testing ZUPT Angle Constraint function...\n");

    // 初始化ZUPT结构体
    ZUPTInit(&g_ZUPT);

    // 模拟角增量数据
    DELANG test_angles[3] = {0.1, 0.05, -0.08};  // 模拟角增量（弧度）
    DELANG original_angles[3];

    // 保存原始角增量
    for (int i = 0; i < 3; i++) {
        original_angles[i] = test_angles[i];
    }

    printf("Original angles: [%.6f, %.6f, %.6f] rad\n",
           original_angles[0], original_angles[1], original_angles[2]);

    // 测试1：ZUPT标志位无效时（运动状态）
    g_ZUPT.ZUPTFlag = 0;  // 设置为运动状态
    ZUPTAngleConstraint(test_angles);
    printf("Motion state (ZUPT=0): [%.6f, %.6f, %.6f] rad (no constraint)\n",
           test_angles[0], test_angles[1], test_angles[2]);

    // 恢复原始角增量
    for (int i = 0; i < 3; i++) {
        test_angles[i] = original_angles[i];
    }

    // 测试2：ZUPT标志位有效时（静止状态）
    g_ZUPT.ZUPTFlag = 1;  // 设置为静止状态
    ZUPTAngleConstraint(test_angles);
    printf("Static state (ZUPT=1): [%.6f, %.6f, %.6f] rad (with constraint)\n",
           test_angles[0], test_angles[1], test_angles[2]);

    // 计算缩放比例
    printf("Scale factor applied: %.1f\n", ZUPT_ANGLE_SCALE_FACTOR);
    printf("Constraint ratio: %.1f%% of original\n", ZUPT_ANGLE_SCALE_FACTOR * 100);

    // 验证缩放效果
    BOOL constraint_correct = 1;
    for (int i = 0; i < 3; i++) {
        DPARA expected = original_angles[i] * ZUPT_ANGLE_SCALE_FACTOR;
        if (fabs(test_angles[i] - expected) > 1e-10) {
            constraint_correct = 0;
            break;
        }
    }

    printf("Constraint function test: %s\n",
           constraint_correct ? "PASSED" : "FAILED");

    printf("ZUPT Angle Constraint test completed.\n");
}


