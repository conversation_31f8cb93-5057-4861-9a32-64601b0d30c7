//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：Spi.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.29
//---------------------------------------------------------
#ifndef _SPI_H
#define _SPI_H

#include "board.h"
#include "hpm_debug_console.h"
#include "hpm_spi_drv.h"
#include "hpm_clock_drv.h"
#include "Smi240.h"

#define BOARD_SLAVE_APP_SPI_BASE2  HPM_SPI2
extern spi_control_config_t control_config;
extern spi_control_config_t control_Slave_config;


typedef enum {
    spi_op_write = 0,
    spi_op_read,
    spi_op_no_data
} spi_op_t;

void spi_transfer_mode_print(spi_control_config_t *config);
void spi_master_command_dump(spi_control_config_t *config, uint8_t *cmd);
void spi_master_address_dump(spi_control_config_t *config, uint32_t *addr);
void spi_master_data_dump(spi_op_t op, uint32_t datalen, uint8_t *buff,  uint32_t size);

void spi_master_frame_dump(uint32_t datalen,
                           spi_control_config_t *config,
                           uint8_t *cmd, uint32_t *addr,
                           uint8_t *wbuff,  uint32_t wsize, uint8_t *rbuff,  uint32_t rsize);




void SpiInitMaster(void);

void SpiInitSlave(void);
void SpiSlaveSend(char *txbuf, int size);


uint32_t Smi980SpiTransfer(uint32_t tx_data, uint32_t *rx_data);

#endif /* _SPI_H */