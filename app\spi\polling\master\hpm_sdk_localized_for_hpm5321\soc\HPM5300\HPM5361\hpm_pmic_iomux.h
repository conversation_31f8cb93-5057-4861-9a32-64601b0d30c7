/*
 * Copyright (c) 2021-2024 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */


#ifndef HPM_PMIC_IOMUX_H
#define HPM_PMIC_IOMUX_H

/* PIOC_PY00_FUNC_CTL function mux definitions */
#define IOC_PY00_FUNC_CTL_PGPIO_Y_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY00_FUNC_CTL_PGPIO_Y_00          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY00_FUNC_CTL_PUART_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY00_FUNC_CTL_PUART_TXD           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY00_FUNC_CTL_PTMR_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY00_FUNC_CTL_PTMR_COMP_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY00_FUNC_CTL_SOC_GPIO_Y_00        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY00_FUNC_CTL_SOC_GPIO_Y_00       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY01_FUNC_CTL function mux definitions */
#define IOC_PY01_FUNC_CTL_PGPIO_Y_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY01_FUNC_CTL_PGPIO_Y_01          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY01_FUNC_CTL_PUART_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY01_FUNC_CTL_PUART_RXD           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY01_FUNC_CTL_PTMR_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY01_FUNC_CTL_PTMR_COMP_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY01_FUNC_CTL_SOC_GPIO_Y_01        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY01_FUNC_CTL_SOC_GPIO_Y_01       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY02_FUNC_CTL function mux definitions */
#define IOC_PY02_FUNC_CTL_PGPIO_Y_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY02_FUNC_CTL_PGPIO_Y_02          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY02_FUNC_CTL_PUART_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY02_FUNC_CTL_PUART_RTS           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY02_FUNC_CTL_PTMR_COMP_2          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY02_FUNC_CTL_PTMR_COMP_2         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY02_FUNC_CTL_SOC_GPIO_Y_02        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY02_FUNC_CTL_SOC_GPIO_Y_02       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY03_FUNC_CTL function mux definitions */
#define IOC_PY03_FUNC_CTL_PGPIO_Y_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY03_FUNC_CTL_PGPIO_Y_03          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY03_FUNC_CTL_PUART_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY03_FUNC_CTL_PUART_CTS           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY03_FUNC_CTL_PTMR_COMP_3          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY03_FUNC_CTL_PTMR_COMP_3         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY03_FUNC_CTL_SOC_GPIO_Y_03        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY03_FUNC_CTL_SOC_GPIO_Y_03       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY04_FUNC_CTL function mux definitions */
#define IOC_PY04_FUNC_CTL_PGPIO_Y_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY04_FUNC_CTL_PGPIO_Y_04          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY04_FUNC_CTL_PTMR_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY04_FUNC_CTL_PTMR_COMP_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY04_FUNC_CTL_SOC_GPIO_Y_04        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY04_FUNC_CTL_SOC_GPIO_Y_04       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY05_FUNC_CTL function mux definitions */
#define IOC_PY05_FUNC_CTL_PGPIO_Y_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY05_FUNC_CTL_PGPIO_Y_05          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY05_FUNC_CTL_PEWDG_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY05_FUNC_CTL_PEWDG_RST           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY05_FUNC_CTL_PTMR_CAPT_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY05_FUNC_CTL_PTMR_CAPT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY05_FUNC_CTL_SOC_GPIO_Y_05        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY05_FUNC_CTL_SOC_GPIO_Y_05       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY06_FUNC_CTL function mux definitions */
#define IOC_PY06_FUNC_CTL_PGPIO_Y_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY06_FUNC_CTL_PGPIO_Y_06          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY06_FUNC_CTL_PTMR_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY06_FUNC_CTL_PTMR_COMP_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY06_FUNC_CTL_SOC_GPIO_Y_06        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY06_FUNC_CTL_SOC_GPIO_Y_06       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)

/* PIOC_PY07_FUNC_CTL function mux definitions */
#define IOC_PY07_FUNC_CTL_PGPIO_Y_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY07_FUNC_CTL_PGPIO_Y_07          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY07_FUNC_CTL_PTMR_CAPT_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY07_FUNC_CTL_PTMR_CAPT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY07_FUNC_CTL_SOC_GPIO_Y_07        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3) /* dropped macro, please use macro with prefix PIOC_ */
#define PIOC_PY07_FUNC_CTL_SOC_GPIO_Y_07       IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)


#endif /* HPM_PMIC_IOMUX_H */
