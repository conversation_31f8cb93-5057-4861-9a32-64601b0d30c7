#include "Smi980.h"
#include "board.h"
#include "hpm_spi_drv.h"
#include <stdio.h>

// 外部SPI传输函数声明
extern int Smi980SpiTransfer(uint32_t tx_data, uint32_t *rx_data);

// CRC3校验函数
uint8_t Smi980_CRC3_Correct(uint32_t frame)
{
    uint8_t crc = 0;
    uint32_t data = frame >> 3;
    
    for (int i = 28; i >= 0; i--) {
        uint8_t bit = (data >> i) & 1;
        uint8_t msb = (crc >> 2) & 1;
        crc = ((crc << 1) | bit) & 0x07;
        if (msb) {
            crc ^= 0x03;
        }
    }
    
    return crc;
}

// 构建SPI输入帧
uint32_t Smi980_BuildInFrame_Correct(uint8_t addr, uint8_t rw, uint32_t data)
{
    uint32_t frame = 0;
    frame |= ((uint32_t)(addr & 0x1F)) << 27;
    frame |= ((uint32_t)(rw & 0x07)) << 24;
    frame |= (data & 0x1FFFFF) << 3;
    
    uint8_t crc = Smi980_CRC3_Correct(frame);
    frame |= crc & 0x07;
    
    return frame;
}

// SMI980传感器初始化
int Smi980_Init(void)
{
    uint32_t response = 0;
    
    // 上电延时
    board_delay_ms(50);
    
    // 协议选择
    uint32_t protocol_cmd = 0x10000004;
    Smi980SpiTransfer(protocol_cmd, &response);
    board_delay_ms(20);
    
    // EOC命令
    uint32_t eoc_cmd = 0x0C50000D;
    Smi980SpiTransfer(eoc_cmd, &response);
    board_delay_ms(50);
    
    // 配置指令
    uint32_t config1 = 0x08100004;
    Smi980SpiTransfer(config1, &response);
    
    uint32_t config2 = 0x0D400020;
    Smi980SpiTransfer(config2, &response);
    
    // 等待传感器准备
    board_delay_ms(1000);
    
    return 0;
}

// SMI980传感器数据读取
void Smi980_ReadData(float *ImuData)
{
    uint32_t response = 0;
     float ImuData1[7] = { 0 }; 
    // 发送配置指令
    uint32_t config1 = 0x08100004;
    Smi980SpiTransfer(config1, &response);
    
    uint32_t config2 = 0x0D400020;
    Smi980SpiTransfer(config2, &response);
    
    //board_delay_ms(50);
    
    // 读取传感器数据
    uint32_t read_commands[] = {
        0x23000000,  // ACC X
        0x33000000,  // ACC Y  
        0xB3000000,  // ACC Z
        0x13000000,  // GYR X
        0x93000000,  // GYR Y
        0xA3000000,  // GYR Z
        0x9B000010   // TEMP
    };
    
    for(int i = 0; i < 7; i++) {
        Smi980SpiTransfer(read_commands[i], &response);
        
        // 解析数据
        uint32_t data_field = (response >> 4) & 0x7FFFFF;
        int16_t raw_data = (int16_t)(data_field & 0xFFFF);
        
        if(i < 3) {
            // 加速度数据 (16位配置，6g范围，5000 LSB/g)
            ImuData1[i]= raw_data / 5000.0f;
        //  printf("ACC_%c: %.4f g\n", 'X'+i, ImuData[i]);
        } else if(i < 6) {
            //陀螺仪数据 (16位配置，300°/s范围，100 LSB/°/s)
           ImuData1[i] = raw_data / 100.0f;
            //printf("GYR_%c: %.2f 度/s\n", 'X'+(i-3), ImuData[i]);
        } else {
            // 温度数据 (16位配置，200 LSB/K)
            ImuData1[i]= raw_data / 200.0f + 50.0f;
           // printf("TEMP: %.1f 摄氏度\n", ImuData[i]);
        }
        ImuData[0]=ImuData1[6];
        ImuData[1]=ImuData1[0];
        ImuData[2]=ImuData1[1];
        ImuData[3]=ImuData1[2];
        ImuData[4]=ImuData1[3];
        ImuData[5]=ImuData1[4];
        ImuData[6]=ImuData1[5];
       // board_delay_ms(5);
    }
    
   
}
