{"target": {"name": "spi_polling_master_example", "sources": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/src/spi.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/iar/startup.s,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/pinmux.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/board.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/reset.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/trap.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/system.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_sysctl_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_l1c_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_clock_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_otp_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot/hpm_bootheader.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_uart_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sdp_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_i2c_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pmp_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rng_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gpio_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_spi_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gptmr_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pwm_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pllctlv2_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_usb_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_acmp_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_adc16_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pcfg_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ptpc_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mchtmr_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_tsns_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dac_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_crc_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mcan_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeiv2_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_enc_pos_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sei_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeo_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rdc_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mmc_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dmav2_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ewdg_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_plb_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_opamp_drv.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_sbrk.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_swap.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_ffssi.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_crc32.c,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/hpm_debug_console.c", "includes": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/.,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/.,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/.,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/.,E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include", "defines": "FLASH_XIP=1,<PERSON>MS<PERSON>_HAS_HPMSDK_GPIO=y,HPMSOC_HAS_HPMSDK_PLIC=y,HPMSOC_HAS_HPMSDK_MCHTMR=y,HPMSOC_HAS_HPMSDK_PLICSW=y,HPMSOC_HAS_HPMSDK_GPTMR=y,HPMSOC_HAS_HPMSDK_UART=y,HPMSOC_HAS_HPMSDK_I2C=y,HPMSOC_HAS_HPMSDK_SPI=y,HPMSOC_HAS_HPMSDK_CRC=y,HPMSOC_HAS_HPMSDK_TSNS=y,HPMSOC_HAS_HPMSDK_MBX=y,HPMSOC_HAS_HPMSDK_EWDG=y,HPMSOC_HAS_HPMSDK_DMAMUX=y,HPMSOC_HAS_HPMSDK_DMAV2=y,HPMSOC_HAS_HPMSDK_GPIOM=y,HPMSOC_HAS_HPMSDK_MCAN=y,HPMSOC_HAS_HPMSDK_PTPC=y,HPMSOC_HAS_HPMSDK_QEIV2=y,HPMSOC_HAS_HPMSDK_QEO=y,HPMSOC_HAS_HPMSDK_MMC=y,HPMSOC_HAS_HPMSDK_PWM=y,HPMSOC_HAS_HPMSDK_RDC=y,HPMSOC_HAS_HPMSDK_PLB=y,HPMSOC_HAS_HPMSDK_SYNT=y,HPMSOC_HAS_HPMSDK_SEI=y,HPMSOC_HAS_HPMSDK_TRGM=y,HPMSOC_HAS_HPMSDK_USB=y,HPMSOC_HAS_HPMSDK_SDP=y,HPMSOC_HAS_HPMSDK_SEC=y,HPMSOC_HAS_HPMSDK_MON=y,HPMSOC_HAS_HPMSDK_RNG=y,HPMSOC_HAS_HPMSDK_OTP=y,HPMSOC_HAS_HPMSDK_KEYM=y,HPMSOC_HAS_HPMSDK_ADC16=y,HPMSOC_HAS_HPMSDK_DAC=y,HPMSOC_HAS_HPMSDK_OPAMP=y,HPMSOC_HAS_HPMSDK_ACMP=y,HPMSOC_HAS_HPMSDK_SYSCTL=y,HPMSOC_HAS_HPMSDK_IOC=y,HPMSOC_HAS_HPMSDK_PLLCTLV2=y,HPMSOC_HAS_HPMSDK_PPOR=y,HPMSOC_HAS_HPMSDK_PCFG=y,HPMSOC_HAS_HPMSDK_PGPR=y,HPMSOC_HAS_HPMSDK_PDGO=y,HPMSOC_HAS_HPMSDK_PMP=y", "linker": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/iar/flash_xip.icf", "link_symbols": "_flash_size=1M", "sdk_base": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321", "board": "hpm5321", "board_dir": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321", "soc": "HPM5361", "register_definition": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_iar_reg.xml", "cpu_register_definition": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_iar_riscv_cpu_regs.xml", "post_build_command": "", "heap_size": "0x4000", "stack_size": "0x4000", "cplusplus": "11", "gcc_opt_level": "", "target_device_name": "HPM5361xCBx\tHPMicro HPM5361xCBx", "cflags": "", "compiler_abi": "ilp32", "compiler_arch": "rv32imac_Zba_Zbb_Zbc_Zbs", "compiler_arch_exts_csr": "1", "compiler_arch_exts_fencei": "1", "iar_link_input": "", "enable_dsp": "0", "enable_andesperf": "0", "enable_cpp_exceptions": "", "extra_iar_options": "--diag_suppress Pa089, --diag_suppress Pe236, --diag_suppress Pe188, --diag_suppress Pe546, --diag_suppress Pe111", "linked_project_name": "", "linked_project_path": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/", "is_secondary_core": "0", "cc_preinclude": "", "asm_preinclude": "", "iar_opt_no_size_const": ""}}