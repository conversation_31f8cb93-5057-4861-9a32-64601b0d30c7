/*
 * Copyright (c) 2021-2024 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */


#ifndef HPM_IOMUX_H
#define HPM_IOMUX_H

/* IOC_PA00_FUNC_CTL function mux definitions */
#define IOC_PA00_FUNC_CTL_GPIO_A_00            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA00_FUNC_CTL_GPTMR1_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA00_FUNC_CTL_UART0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA00_FUNC_CTL_MCAN0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA00_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA00_FUNC_CTL_PWM1_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA00_FUNC_CTL_TRGM0_P_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA00_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA00_FUNC_CTL_SYSCTL_CLK_OBS_0     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA01_FUNC_CTL function mux definitions */
#define IOC_PA01_FUNC_CTL_GPIO_A_01            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA01_FUNC_CTL_GPTMR1_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA01_FUNC_CTL_UART0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA01_FUNC_CTL_MCAN0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA01_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA01_FUNC_CTL_PWM1_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA01_FUNC_CTL_TRGM0_P_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA01_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA01_FUNC_CTL_SYSCTL_CLK_OBS_1     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA02_FUNC_CTL function mux definitions */
#define IOC_PA02_FUNC_CTL_GPIO_A_02            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA02_FUNC_CTL_GPTMR1_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA02_FUNC_CTL_UART0_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA02_FUNC_CTL_UART0_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA02_FUNC_CTL_I2C0_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA02_FUNC_CTL_MCAN0_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA02_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA02_FUNC_CTL_PWM1_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA02_FUNC_CTL_TRGM0_P_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA02_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA02_FUNC_CTL_QEI1_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA02_FUNC_CTL_SYSCTL_CLK_OBS_2     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA03_FUNC_CTL function mux definitions */
#define IOC_PA03_FUNC_CTL_GPIO_A_03            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA03_FUNC_CTL_GPTMR1_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA03_FUNC_CTL_UART0_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA03_FUNC_CTL_I2C0_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA03_FUNC_CTL_SPI3_CS_3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA03_FUNC_CTL_MCAN1_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA03_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA03_FUNC_CTL_PWM1_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA03_FUNC_CTL_TRGM0_P_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA03_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA03_FUNC_CTL_QEI1_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA03_FUNC_CTL_SYSCTL_CLK_OBS_3     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA04_FUNC_CTL function mux definitions */
#define IOC_PA04_FUNC_CTL_GPIO_A_04            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA04_FUNC_CTL_UART1_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA04_FUNC_CTL_SPI0_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA04_FUNC_CTL_MCAN1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA04_FUNC_CTL_PWM0_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA04_FUNC_CTL_PWM1_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA04_FUNC_CTL_TRGM0_P_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA04_FUNC_CTL_RDC0_EXC_P           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA04_FUNC_CTL_QEI1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA04_FUNC_CTL_QEO1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA04_FUNC_CTL_SEI1_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA04_FUNC_CTL_JTAG_TDO             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA05_FUNC_CTL function mux definitions */
#define IOC_PA05_FUNC_CTL_GPIO_A_05            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA05_FUNC_CTL_GPTMR1_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA05_FUNC_CTL_UART1_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA05_FUNC_CTL_UART1_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA05_FUNC_CTL_SPI0_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA05_FUNC_CTL_MCAN1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA05_FUNC_CTL_PWM0_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA05_FUNC_CTL_PWM1_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA05_FUNC_CTL_TRGM0_P_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA05_FUNC_CTL_RDC0_EXC_N           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA05_FUNC_CTL_QEI1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA05_FUNC_CTL_QEO1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA05_FUNC_CTL_SEI1_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA05_FUNC_CTL_JTAG_TDI             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA06_FUNC_CTL function mux definitions */
#define IOC_PA06_FUNC_CTL_GPIO_A_06            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA06_FUNC_CTL_GPTMR0_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA06_FUNC_CTL_UART1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA06_FUNC_CTL_I2C1_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA06_FUNC_CTL_SPI0_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA06_FUNC_CTL_PWM0_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA06_FUNC_CTL_PWM1_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA06_FUNC_CTL_TRGM0_P_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA06_FUNC_CTL_QEI1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA06_FUNC_CTL_QEO1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA06_FUNC_CTL_SEI1_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA06_FUNC_CTL_JTAG_TCK             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA07_FUNC_CTL function mux definitions */
#define IOC_PA07_FUNC_CTL_GPIO_A_07            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA07_FUNC_CTL_GPTMR0_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA07_FUNC_CTL_UART1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA07_FUNC_CTL_I2C1_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA07_FUNC_CTL_SPI0_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA07_FUNC_CTL_PWM0_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA07_FUNC_CTL_PWM1_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA07_FUNC_CTL_TRGM0_P_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA07_FUNC_CTL_QEI1_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA07_FUNC_CTL_SEI1_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA07_FUNC_CTL_JTAG_TMS             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA08_FUNC_CTL function mux definitions */
#define IOC_PA08_FUNC_CTL_GPIO_A_08            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA08_FUNC_CTL_GPTMR0_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA08_FUNC_CTL_UART2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA08_FUNC_CTL_I2C2_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA08_FUNC_CTL_SPI3_CS_2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA08_FUNC_CTL_MCAN2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA08_FUNC_CTL_PWM0_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA08_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA08_FUNC_CTL_JTAG_TRST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA09_FUNC_CTL function mux definitions */
#define IOC_PA09_FUNC_CTL_GPIO_A_09            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA09_FUNC_CTL_GPTMR0_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA09_FUNC_CTL_UART2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA09_FUNC_CTL_I2C2_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA09_FUNC_CTL_SPI3_CS_1            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA09_FUNC_CTL_MCAN2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA09_FUNC_CTL_PWM0_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA09_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA09_FUNC_CTL_SOC_REF0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA10_FUNC_CTL function mux definitions */
#define IOC_PA10_FUNC_CTL_GPIO_A_10            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA10_FUNC_CTL_GPTMR0_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA10_FUNC_CTL_UART2_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA10_FUNC_CTL_UART2_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA10_FUNC_CTL_SPI3_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA10_FUNC_CTL_MCAN2_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA10_FUNC_CTL_PWM0_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA10_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA10_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA10_FUNC_CTL_QEI1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA10_FUNC_CTL_QEO0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA10_FUNC_CTL_SEI1_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA11_FUNC_CTL function mux definitions */
#define IOC_PA11_FUNC_CTL_GPIO_A_11            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA11_FUNC_CTL_UART2_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA11_FUNC_CTL_SPI3_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA11_FUNC_CTL_PWM0_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA11_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA11_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA11_FUNC_CTL_QEI1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA11_FUNC_CTL_QEO0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA11_FUNC_CTL_SEI1_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA11_FUNC_CTL_EWDG0_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA12_FUNC_CTL function mux definitions */
#define IOC_PA12_FUNC_CTL_GPIO_A_12            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA12_FUNC_CTL_UART3_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA12_FUNC_CTL_I2C3_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA12_FUNC_CTL_SPI3_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA12_FUNC_CTL_PWM0_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA12_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA12_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA12_FUNC_CTL_RDC0_EXC_P           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA12_FUNC_CTL_QEI1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA12_FUNC_CTL_QEO0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA12_FUNC_CTL_SEI1_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA13_FUNC_CTL function mux definitions */
#define IOC_PA13_FUNC_CTL_GPIO_A_13            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA13_FUNC_CTL_GPTMR1_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA13_FUNC_CTL_UART3_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA13_FUNC_CTL_UART3_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA13_FUNC_CTL_I2C3_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA13_FUNC_CTL_SPI3_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA13_FUNC_CTL_MCAN3_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA13_FUNC_CTL_PWM0_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA13_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA13_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA13_FUNC_CTL_RDC0_EXC_N           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA13_FUNC_CTL_QEI1_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA13_FUNC_CTL_SEI1_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA14_FUNC_CTL function mux definitions */
#define IOC_PA14_FUNC_CTL_GPIO_A_14            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA14_FUNC_CTL_UART3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA14_FUNC_CTL_SPI3_DAT2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA14_FUNC_CTL_MCAN3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA14_FUNC_CTL_PWM0_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA14_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA14_FUNC_CTL_QEI1_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA14_FUNC_CTL_EWDG1_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA15_FUNC_CTL function mux definitions */
#define IOC_PA15_FUNC_CTL_GPIO_A_15            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA15_FUNC_CTL_GPTMR0_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA15_FUNC_CTL_UART3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA15_FUNC_CTL_SPI3_DAT3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA15_FUNC_CTL_MCAN3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA15_FUNC_CTL_PWM0_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA15_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA15_FUNC_CTL_QEI1_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA15_FUNC_CTL_SOC_REF0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA16_FUNC_CTL function mux definitions */
#define IOC_PA16_FUNC_CTL_GPIO_A_16            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA16_FUNC_CTL_GPTMR3_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA16_FUNC_CTL_UART4_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA16_FUNC_CTL_MCAN0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA16_FUNC_CTL_PWM0_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA16_FUNC_CTL_PWM1_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA16_FUNC_CTL_TRGM0_P_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA16_FUNC_CTL_QEO0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA16_FUNC_CTL_SEI1_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA17_FUNC_CTL function mux definitions */
#define IOC_PA17_FUNC_CTL_GPIO_A_17            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA17_FUNC_CTL_GPTMR3_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA17_FUNC_CTL_UART4_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA17_FUNC_CTL_MCAN0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA17_FUNC_CTL_PWM0_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA17_FUNC_CTL_PWM1_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA17_FUNC_CTL_TRGM0_P_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA17_FUNC_CTL_QEO0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA17_FUNC_CTL_SEI1_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA18_FUNC_CTL function mux definitions */
#define IOC_PA18_FUNC_CTL_GPIO_A_18            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA18_FUNC_CTL_GPTMR3_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA18_FUNC_CTL_UART4_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA18_FUNC_CTL_UART4_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA18_FUNC_CTL_I2C0_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA18_FUNC_CTL_MCAN0_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA18_FUNC_CTL_PWM0_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA18_FUNC_CTL_PWM1_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA18_FUNC_CTL_TRGM0_P_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA18_FUNC_CTL_QEO0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA18_FUNC_CTL_SEI1_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA19_FUNC_CTL function mux definitions */
#define IOC_PA19_FUNC_CTL_GPIO_A_19            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA19_FUNC_CTL_GPTMR3_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA19_FUNC_CTL_UART4_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA19_FUNC_CTL_I2C0_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA19_FUNC_CTL_SPI1_CS_3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA19_FUNC_CTL_MCAN1_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA19_FUNC_CTL_PWM0_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA19_FUNC_CTL_PWM1_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA19_FUNC_CTL_TRGM0_P_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA19_FUNC_CTL_SEI1_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA20_FUNC_CTL function mux definitions */
#define IOC_PA20_FUNC_CTL_GPIO_A_20            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA20_FUNC_CTL_UART5_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA20_FUNC_CTL_SPI2_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA20_FUNC_CTL_MCAN1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA20_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA20_FUNC_CTL_PWM1_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA20_FUNC_CTL_TRGM0_P_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA20_FUNC_CTL_QEI0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA20_FUNC_CTL_QEO0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA20_FUNC_CTL_SEI0_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA21_FUNC_CTL function mux definitions */
#define IOC_PA21_FUNC_CTL_GPIO_A_21            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA21_FUNC_CTL_GPTMR3_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA21_FUNC_CTL_UART5_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA21_FUNC_CTL_UART5_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA21_FUNC_CTL_SPI2_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA21_FUNC_CTL_MCAN1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA21_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA21_FUNC_CTL_PWM1_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA21_FUNC_CTL_TRGM0_P_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA21_FUNC_CTL_QEI0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA21_FUNC_CTL_QEO0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA21_FUNC_CTL_SEI0_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA22_FUNC_CTL function mux definitions */
#define IOC_PA22_FUNC_CTL_GPIO_A_22            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA22_FUNC_CTL_GPTMR2_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA22_FUNC_CTL_UART5_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA22_FUNC_CTL_I2C1_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA22_FUNC_CTL_SPI2_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA22_FUNC_CTL_PWM1_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA22_FUNC_CTL_TRGM0_P_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA22_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA22_FUNC_CTL_QEI0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA22_FUNC_CTL_QEO0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA22_FUNC_CTL_SEI0_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA23_FUNC_CTL function mux definitions */
#define IOC_PA23_FUNC_CTL_GPIO_A_23            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA23_FUNC_CTL_GPTMR2_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA23_FUNC_CTL_UART5_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA23_FUNC_CTL_I2C1_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA23_FUNC_CTL_SPI2_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA23_FUNC_CTL_PWM1_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA23_FUNC_CTL_TRGM0_P_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA23_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA23_FUNC_CTL_QEI0_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA23_FUNC_CTL_SEI0_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PA24_FUNC_CTL function mux definitions */
#define IOC_PA24_FUNC_CTL_GPIO_A_24            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA24_FUNC_CTL_GPTMR2_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA24_FUNC_CTL_UART6_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA24_FUNC_CTL_I2C2_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA24_FUNC_CTL_SPI1_CS_2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA24_FUNC_CTL_MCAN2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA24_FUNC_CTL_XPI0_CA_CS1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA24_FUNC_CTL_PWM0_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA24_FUNC_CTL_PWM1_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA24_FUNC_CTL_TRGM0_P_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA24_FUNC_CTL_QEI0_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)

/* IOC_PA25_FUNC_CTL function mux definitions */
#define IOC_PA25_FUNC_CTL_GPIO_A_25            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA25_FUNC_CTL_GPTMR2_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA25_FUNC_CTL_UART6_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA25_FUNC_CTL_I2C2_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA25_FUNC_CTL_SPI1_CS_1            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA25_FUNC_CTL_MCAN2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA25_FUNC_CTL_XPI0_CA_DQS          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA25_FUNC_CTL_PWM0_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA25_FUNC_CTL_PWM1_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA25_FUNC_CTL_TRGM0_P_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA25_FUNC_CTL_QEI0_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)

/* IOC_PA26_FUNC_CTL function mux definitions */
#define IOC_PA26_FUNC_CTL_GPIO_A_26            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA26_FUNC_CTL_GPTMR2_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA26_FUNC_CTL_UART6_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA26_FUNC_CTL_UART6_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA26_FUNC_CTL_SPI1_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA26_FUNC_CTL_MCAN2_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA26_FUNC_CTL_XPI0_CA_D_3          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA26_FUNC_CTL_PWM0_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA26_FUNC_CTL_PWM1_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA26_FUNC_CTL_TRGM0_P_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA26_FUNC_CTL_QEI0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA26_FUNC_CTL_QEO0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA26_FUNC_CTL_SEI0_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA26_FUNC_CTL_SYSCTL_CLK_OBS_0     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA27_FUNC_CTL function mux definitions */
#define IOC_PA27_FUNC_CTL_GPIO_A_27            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA27_FUNC_CTL_UART6_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA27_FUNC_CTL_SPI1_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA27_FUNC_CTL_XPI0_CA_SCLK         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA27_FUNC_CTL_PWM0_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA27_FUNC_CTL_PWM1_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA27_FUNC_CTL_TRGM0_P_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA27_FUNC_CTL_QEI0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA27_FUNC_CTL_QEO0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA27_FUNC_CTL_SEI0_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA27_FUNC_CTL_SYSCTL_CLK_OBS_1     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA28_FUNC_CTL function mux definitions */
#define IOC_PA28_FUNC_CTL_GPIO_A_28            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA28_FUNC_CTL_UART7_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA28_FUNC_CTL_I2C3_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA28_FUNC_CTL_SPI1_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA28_FUNC_CTL_XPI0_CA_D_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA28_FUNC_CTL_PWM0_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA28_FUNC_CTL_PWM1_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA28_FUNC_CTL_TRGM0_P_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA28_FUNC_CTL_RDC0_EXC_P           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA28_FUNC_CTL_QEI0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA28_FUNC_CTL_QEO0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PA28_FUNC_CTL_SEI0_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA28_FUNC_CTL_SYSCTL_CLK_OBS_2     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PA29_FUNC_CTL function mux definitions */
#define IOC_PA29_FUNC_CTL_GPIO_A_29            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA29_FUNC_CTL_GPTMR3_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA29_FUNC_CTL_UART7_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA29_FUNC_CTL_UART7_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PA29_FUNC_CTL_I2C3_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PA29_FUNC_CTL_SPI1_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA29_FUNC_CTL_MCAN3_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA29_FUNC_CTL_XPI0_CA_D_2          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA29_FUNC_CTL_PWM0_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA29_FUNC_CTL_PWM1_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA29_FUNC_CTL_TRGM0_P_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA29_FUNC_CTL_RDC0_EXC_N           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PA29_FUNC_CTL_QEI0_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA29_FUNC_CTL_SEI0_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PA29_FUNC_CTL_SYSCTL_CLK_OBS_3     IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)
#define IOC_PA29_FUNC_CTL_USB0_OC              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PA30_FUNC_CTL function mux definitions */
#define IOC_PA30_FUNC_CTL_GPIO_A_30            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA30_FUNC_CTL_UART7_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA30_FUNC_CTL_SPI1_DAT2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA30_FUNC_CTL_MCAN3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA30_FUNC_CTL_XPI0_CA_D_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA30_FUNC_CTL_PWM0_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA30_FUNC_CTL_PWM1_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA30_FUNC_CTL_TRGM0_P_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA30_FUNC_CTL_QEI0_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA30_FUNC_CTL_SOC_REF0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)
#define IOC_PA30_FUNC_CTL_USB0_PWR             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PA31_FUNC_CTL function mux definitions */
#define IOC_PA31_FUNC_CTL_GPIO_A_31            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PA31_FUNC_CTL_GPTMR2_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PA31_FUNC_CTL_UART7_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PA31_FUNC_CTL_SPI1_DAT3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PA31_FUNC_CTL_MCAN3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PA31_FUNC_CTL_XPI0_CA_CS0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)
#define IOC_PA31_FUNC_CTL_PWM0_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PA31_FUNC_CTL_PWM1_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PA31_FUNC_CTL_TRGM0_P_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PA31_FUNC_CTL_QEI0_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PA31_FUNC_CTL_USB0_ID              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PB00_FUNC_CTL function mux definitions */
#define IOC_PB00_FUNC_CTL_GPIO_B_00            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB00_FUNC_CTL_GPTMR1_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB00_FUNC_CTL_UART0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB00_FUNC_CTL_MCAN0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB00_FUNC_CTL_PWM0_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB00_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB00_FUNC_CTL_TRGM0_P_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB00_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)

/* IOC_PB01_FUNC_CTL function mux definitions */
#define IOC_PB01_FUNC_CTL_GPIO_B_01            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB01_FUNC_CTL_GPTMR1_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB01_FUNC_CTL_UART0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB01_FUNC_CTL_MCAN0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB01_FUNC_CTL_PWM0_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB01_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB01_FUNC_CTL_TRGM0_P_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB01_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)

/* IOC_PB02_FUNC_CTL function mux definitions */
#define IOC_PB02_FUNC_CTL_GPIO_B_02            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB02_FUNC_CTL_GPTMR1_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB02_FUNC_CTL_UART0_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB02_FUNC_CTL_UART0_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB02_FUNC_CTL_I2C0_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB02_FUNC_CTL_MCAN0_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB02_FUNC_CTL_PWM0_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB02_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB02_FUNC_CTL_TRGM0_P_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB02_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)

/* IOC_PB03_FUNC_CTL function mux definitions */
#define IOC_PB03_FUNC_CTL_GPIO_B_03            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB03_FUNC_CTL_GPTMR1_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB03_FUNC_CTL_UART0_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB03_FUNC_CTL_I2C0_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB03_FUNC_CTL_SPI2_CS_3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB03_FUNC_CTL_MCAN1_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB03_FUNC_CTL_PWM0_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB03_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB03_FUNC_CTL_TRGM0_P_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB03_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)

/* IOC_PB04_FUNC_CTL function mux definitions */
#define IOC_PB04_FUNC_CTL_GPIO_B_04            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB04_FUNC_CTL_UART1_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB04_FUNC_CTL_SPI3_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB04_FUNC_CTL_MCAN1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB04_FUNC_CTL_PWM0_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB04_FUNC_CTL_PWM1_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB04_FUNC_CTL_TRGM0_P_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB04_FUNC_CTL_QEI1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB04_FUNC_CTL_QEO1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB04_FUNC_CTL_SEI0_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB05_FUNC_CTL function mux definitions */
#define IOC_PB05_FUNC_CTL_GPIO_B_05            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB05_FUNC_CTL_GPTMR1_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB05_FUNC_CTL_UART1_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB05_FUNC_CTL_UART1_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB05_FUNC_CTL_SPI3_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB05_FUNC_CTL_MCAN1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB05_FUNC_CTL_PWM0_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB05_FUNC_CTL_PWM1_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB05_FUNC_CTL_TRGM0_P_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB05_FUNC_CTL_QEI1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB05_FUNC_CTL_QEO1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB05_FUNC_CTL_SEI0_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB06_FUNC_CTL function mux definitions */
#define IOC_PB06_FUNC_CTL_GPIO_B_06            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB06_FUNC_CTL_GPTMR0_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB06_FUNC_CTL_UART1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB06_FUNC_CTL_I2C1_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB06_FUNC_CTL_SPI3_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB06_FUNC_CTL_PWM0_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB06_FUNC_CTL_PWM1_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB06_FUNC_CTL_TRGM0_P_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB06_FUNC_CTL_RDC0_EXC_P           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PB06_FUNC_CTL_QEI1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB06_FUNC_CTL_QEO1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB06_FUNC_CTL_SEI0_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB07_FUNC_CTL function mux definitions */
#define IOC_PB07_FUNC_CTL_GPIO_B_07            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB07_FUNC_CTL_GPTMR0_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB07_FUNC_CTL_UART1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB07_FUNC_CTL_I2C1_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB07_FUNC_CTL_SPI3_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB07_FUNC_CTL_PWM0_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB07_FUNC_CTL_PWM1_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB07_FUNC_CTL_TRGM0_P_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB07_FUNC_CTL_RDC0_EXC_N           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PB07_FUNC_CTL_QEI1_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB07_FUNC_CTL_SEI0_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB08_FUNC_CTL function mux definitions */
#define IOC_PB08_FUNC_CTL_GPIO_B_08            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB08_FUNC_CTL_GPTMR0_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB08_FUNC_CTL_UART2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB08_FUNC_CTL_I2C2_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB08_FUNC_CTL_SPI2_CS_2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB08_FUNC_CTL_MCAN2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB08_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB08_FUNC_CTL_PWM1_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB08_FUNC_CTL_QEI1_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB08_FUNC_CTL_QEO1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB08_FUNC_CTL_SEI1_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PB08_FUNC_CTL_USB0_ID              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PB09_FUNC_CTL function mux definitions */
#define IOC_PB09_FUNC_CTL_GPIO_B_09            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB09_FUNC_CTL_GPTMR0_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB09_FUNC_CTL_UART2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB09_FUNC_CTL_I2C2_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB09_FUNC_CTL_SPI2_CS_1            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB09_FUNC_CTL_MCAN2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB09_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB09_FUNC_CTL_PWM1_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB09_FUNC_CTL_QEI1_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB09_FUNC_CTL_QEO1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB09_FUNC_CTL_SEI1_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PB09_FUNC_CTL_USB0_OC              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PB10_FUNC_CTL function mux definitions */
#define IOC_PB10_FUNC_CTL_GPIO_B_10            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB10_FUNC_CTL_GPTMR0_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB10_FUNC_CTL_UART2_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB10_FUNC_CTL_UART2_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB10_FUNC_CTL_SPI2_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB10_FUNC_CTL_MCAN2_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB10_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB10_FUNC_CTL_PWM1_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB10_FUNC_CTL_QEI0_H1              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB10_FUNC_CTL_QEO1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB10_FUNC_CTL_SEI1_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)
#define IOC_PB10_FUNC_CTL_USB0_PWR             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PB11_FUNC_CTL function mux definitions */
#define IOC_PB11_FUNC_CTL_GPIO_B_11            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB11_FUNC_CTL_UART2_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB11_FUNC_CTL_SPI2_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB11_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB11_FUNC_CTL_PWM1_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB11_FUNC_CTL_QEI0_F               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB11_FUNC_CTL_SEI1_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB12_FUNC_CTL function mux definitions */
#define IOC_PB12_FUNC_CTL_GPIO_B_12            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB12_FUNC_CTL_UART3_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB12_FUNC_CTL_I2C3_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB12_FUNC_CTL_SPI2_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB12_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB12_FUNC_CTL_PWM1_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB12_FUNC_CTL_TRGM0_P_00           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB12_FUNC_CTL_QEI0_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB12_FUNC_CTL_QEO1_A               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB12_FUNC_CTL_SEI0_DE              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB13_FUNC_CTL function mux definitions */
#define IOC_PB13_FUNC_CTL_GPIO_B_13            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB13_FUNC_CTL_GPTMR1_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB13_FUNC_CTL_UART3_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB13_FUNC_CTL_UART3_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PB13_FUNC_CTL_I2C3_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PB13_FUNC_CTL_SPI2_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB13_FUNC_CTL_MCAN3_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB13_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB13_FUNC_CTL_PWM1_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB13_FUNC_CTL_TRGM0_P_01           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB13_FUNC_CTL_QEI0_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB13_FUNC_CTL_QEO1_B               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB13_FUNC_CTL_SEI0_CK              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB14_FUNC_CTL function mux definitions */
#define IOC_PB14_FUNC_CTL_GPIO_B_14            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB14_FUNC_CTL_UART3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB14_FUNC_CTL_SPI2_DAT2            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB14_FUNC_CTL_MCAN3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB14_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB14_FUNC_CTL_PWM1_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB14_FUNC_CTL_TRGM0_P_02           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB14_FUNC_CTL_RDC0_EXC_P           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PB14_FUNC_CTL_QEI0_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB14_FUNC_CTL_QEO1_Z               IOC_PAD_FUNC_CTL_ALT_SELECT_SET(21)
#define IOC_PB14_FUNC_CTL_SEI0_TX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PB15_FUNC_CTL function mux definitions */
#define IOC_PB15_FUNC_CTL_GPIO_B_15            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PB15_FUNC_CTL_GPTMR0_COMP_3        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PB15_FUNC_CTL_UART3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PB15_FUNC_CTL_SPI2_DAT3            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PB15_FUNC_CTL_MCAN3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PB15_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PB15_FUNC_CTL_PWM1_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PB15_FUNC_CTL_TRGM0_P_03           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PB15_FUNC_CTL_RDC0_EXC_N           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PB15_FUNC_CTL_QEI0_H0              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(20)
#define IOC_PB15_FUNC_CTL_SEI0_RX              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(22)

/* IOC_PX00_FUNC_CTL function mux definitions */
#define IOC_PX00_FUNC_CTL_GPIO_X_00            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX00_FUNC_CTL_GPTMR2_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX00_FUNC_CTL_UART4_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX00_FUNC_CTL_MCAN0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX00_FUNC_CTL_XPI0_CA_D_2          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX01_FUNC_CTL function mux definitions */
#define IOC_PX01_FUNC_CTL_GPIO_X_01            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX01_FUNC_CTL_GPTMR2_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX01_FUNC_CTL_UART4_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX01_FUNC_CTL_MCAN0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX01_FUNC_CTL_XPI0_CA_D_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX02_FUNC_CTL function mux definitions */
#define IOC_PX02_FUNC_CTL_GPIO_X_02            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX02_FUNC_CTL_GPTMR2_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX02_FUNC_CTL_UART4_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX02_FUNC_CTL_UART4_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PX02_FUNC_CTL_I2C0_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PX02_FUNC_CTL_MCAN0_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX02_FUNC_CTL_XPI0_CA_CS0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX03_FUNC_CTL function mux definitions */
#define IOC_PX03_FUNC_CTL_GPIO_X_03            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX03_FUNC_CTL_GPTMR2_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX03_FUNC_CTL_UART4_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PX03_FUNC_CTL_I2C0_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PX03_FUNC_CTL_MCAN1_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX03_FUNC_CTL_XPI0_CA_DQS          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX04_FUNC_CTL function mux definitions */
#define IOC_PX04_FUNC_CTL_GPIO_X_04            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX04_FUNC_CTL_UART5_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PX04_FUNC_CTL_SPI1_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PX04_FUNC_CTL_MCAN1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX04_FUNC_CTL_XPI0_CA_CS1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX05_FUNC_CTL function mux definitions */
#define IOC_PX05_FUNC_CTL_GPIO_X_05            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX05_FUNC_CTL_GPTMR2_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX05_FUNC_CTL_UART5_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX05_FUNC_CTL_UART5_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PX05_FUNC_CTL_SPI1_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PX05_FUNC_CTL_MCAN1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PX05_FUNC_CTL_XPI0_CA_D_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX06_FUNC_CTL function mux definitions */
#define IOC_PX06_FUNC_CTL_GPIO_X_06            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX06_FUNC_CTL_GPTMR3_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX06_FUNC_CTL_UART5_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX06_FUNC_CTL_I2C1_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PX06_FUNC_CTL_SPI1_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PX06_FUNC_CTL_XPI0_CA_SCLK         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PX07_FUNC_CTL function mux definitions */
#define IOC_PX07_FUNC_CTL_GPIO_X_07            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PX07_FUNC_CTL_GPTMR3_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PX07_FUNC_CTL_UART5_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PX07_FUNC_CTL_I2C1_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PX07_FUNC_CTL_SPI1_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PX07_FUNC_CTL_XPI0_CA_D_3          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(14)

/* IOC_PY00_FUNC_CTL function mux definitions */
#define IOC_PY00_FUNC_CTL_GPIO_Y_00            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY00_FUNC_CTL_GPTMR3_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY00_FUNC_CTL_UART0_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY00_FUNC_CTL_MCAN2_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY00_FUNC_CTL_PWM0_P_0             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY00_FUNC_CTL_PWM1_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PY00_FUNC_CTL_PWM0_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY00_FUNC_CTL_USB0_ID              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PY01_FUNC_CTL function mux definitions */
#define IOC_PY01_FUNC_CTL_GPIO_Y_01            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY01_FUNC_CTL_GPTMR3_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY01_FUNC_CTL_UART0_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY01_FUNC_CTL_MCAN2_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY01_FUNC_CTL_PWM0_P_1             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY01_FUNC_CTL_PWM1_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PY01_FUNC_CTL_PWM0_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY01_FUNC_CTL_EWDG0_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)
#define IOC_PY01_FUNC_CTL_USB0_OC              IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PY02_FUNC_CTL function mux definitions */
#define IOC_PY02_FUNC_CTL_GPIO_Y_02            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY02_FUNC_CTL_GPTMR3_COMP_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY02_FUNC_CTL_UART0_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY02_FUNC_CTL_UART0_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PY02_FUNC_CTL_I2C2_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PY02_FUNC_CTL_MCAN2_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY02_FUNC_CTL_PWM0_P_2             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY02_FUNC_CTL_PWM1_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PY02_FUNC_CTL_ACMP_COMP_0          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY02_FUNC_CTL_PWM1_FAULT_0         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)
#define IOC_PY02_FUNC_CTL_EWDG1_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)
#define IOC_PY02_FUNC_CTL_USB0_PWR             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(25)

/* IOC_PY03_FUNC_CTL function mux definitions */
#define IOC_PY03_FUNC_CTL_GPIO_Y_03            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY03_FUNC_CTL_GPTMR3_CAPT_1        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY03_FUNC_CTL_UART0_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PY03_FUNC_CTL_I2C2_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PY03_FUNC_CTL_MCAN3_STBY           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY03_FUNC_CTL_PWM0_P_3             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY03_FUNC_CTL_PWM1_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(17)
#define IOC_PY03_FUNC_CTL_ACMP_COMP_1          IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY03_FUNC_CTL_PWM1_FAULT_1         IOC_PAD_FUNC_CTL_ALT_SELECT_SET(19)

/* IOC_PY04_FUNC_CTL function mux definitions */
#define IOC_PY04_FUNC_CTL_GPIO_Y_04            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY04_FUNC_CTL_UART1_CTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PY04_FUNC_CTL_SPI2_CS_0            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PY04_FUNC_CTL_MCAN3_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY04_FUNC_CTL_PWM0_P_4             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY04_FUNC_CTL_TRGM0_P_04           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)

/* IOC_PY05_FUNC_CTL function mux definitions */
#define IOC_PY05_FUNC_CTL_GPIO_Y_05            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY05_FUNC_CTL_GPTMR3_COMP_2        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY05_FUNC_CTL_UART1_DE             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY05_FUNC_CTL_UART1_RTS            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(3)
#define IOC_PY05_FUNC_CTL_SPI2_SCLK            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PY05_FUNC_CTL_MCAN3_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(7)
#define IOC_PY05_FUNC_CTL_PWM0_P_5             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY05_FUNC_CTL_TRGM0_P_05           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY05_FUNC_CTL_EWDG0_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PY06_FUNC_CTL function mux definitions */
#define IOC_PY06_FUNC_CTL_GPIO_Y_06            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY06_FUNC_CTL_GPTMR2_CAPT_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY06_FUNC_CTL_UART1_RXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY06_FUNC_CTL_I2C3_SDA             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PY06_FUNC_CTL_SPI2_MISO            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PY06_FUNC_CTL_PWM0_P_6             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY06_FUNC_CTL_TRGM0_P_06           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)
#define IOC_PY06_FUNC_CTL_EWDG1_RST            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(24)

/* IOC_PY07_FUNC_CTL function mux definitions */
#define IOC_PY07_FUNC_CTL_GPIO_Y_07            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(0)
#define IOC_PY07_FUNC_CTL_GPTMR2_COMP_0        IOC_PAD_FUNC_CTL_ALT_SELECT_SET(1)
#define IOC_PY07_FUNC_CTL_UART1_TXD            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(2)
#define IOC_PY07_FUNC_CTL_I2C3_SCL             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(4)
#define IOC_PY07_FUNC_CTL_SPI2_MOSI            IOC_PAD_FUNC_CTL_ALT_SELECT_SET(5)
#define IOC_PY07_FUNC_CTL_PWM0_P_7             IOC_PAD_FUNC_CTL_ALT_SELECT_SET(16)
#define IOC_PY07_FUNC_CTL_TRGM0_P_07           IOC_PAD_FUNC_CTL_ALT_SELECT_SET(18)


#endif /* HPM_IOMUX_H */
