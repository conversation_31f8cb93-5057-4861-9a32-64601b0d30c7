//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：Smi240.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "Smi240.h"

static uint8_t Smi240Crc3(uint32_t data, uint8_t init, uint8_t poly)
{
    uint8_t crc = init;
    uint8_t do_xor;
    int8_t i = 31;

    do {
          do_xor = crc & 0x04;
          crc <<= 1;
          crc |= 0x01 & (data >> i);
          if (do_xor)
              crc ^= poly;

          crc &= 0x07;
    } while (--i >= 0);


    return crc;
}

static bool smi240_sensor_data_is_valid(uint32_t data)
{
    if (Smi240Crc3(data, SMI240_CRC_INIT, SMI240_CRC_POLY))
        return false;

    return true;
}


uint32_t Smi240SpiTransfer(uint32_t request, uint32_t *response)
{
    uint32_t ret=0;
    uint8_t wbuff[4] = {0};
    uint8_t rbuff[4] = {0};

    wbuff[0] = request >> 24;
    wbuff[1] = request >> 16;
    wbuff[2] = request >> 8;
    wbuff[3] = request;

    ret = spi_transfer(BOARD_APP_SPI_BASE,
                &control_config,
                NULL, NULL,
                (uint8_t *)wbuff, ARRAY_SIZE(wbuff), (uint8_t *)rbuff, ARRAY_SIZE(rbuff));


    if (response != NULL)
    {
        *response = (rbuff[0] << 24) | (rbuff[1] << 16) | (rbuff[2] << 8) | rbuff[3];
    }

    return ret;
}


static int Smi240SetRegs(uint8_t reg_addr, uint16_t *reg_data,uint16_t len)
{
    int ret=0;
    int i;
    uint32_t data=0;

    for (i = 0; i < len; i++)
    {
        data=0;

        data = SMI240_BUS_ID << 30;
        data |= SMI240_WRITE_BIT << 21;
        data |= (reg_addr + i)<<22;
        data |= reg_data[i]<<3;
        data |= Smi240Crc3(data, SMI240_CRC_INIT, SMI240_CRC_POLY);

        ret = Smi240SpiTransfer(data, NULL);
    }

    return ret;
}

static int Smi240GetRegs(uint8_t reg_addr, uint16_t *reg_data,uint16_t len, uint8_t capture)
{
    int ret=0, i=0;
    uint8_t cap=0;
    uint32_t request=0, response=0;

    uint32_t Cmd[7]={0};

    for (i = 0; i < len; i++) 
    {
        request=0;

        cap = capture && (i == 0);
        request = SMI240_BUS_ID << 30;
        request |= SMI240_READ_BIT << 21;
        request |= cap << 20;
        request |= (reg_addr + i)<<22;
        request |= Smi240Crc3(request, SMI240_CRC_INIT, SMI240_CRC_POLY);

        ret = Smi240SpiTransfer(request, &response);

        if (i > 0) 
        {
            if (!smi240_sensor_data_is_valid(response))
                return -1;
            
            reg_data[i - 1] = (uint16_t)((response>>4)&(0xFFFF));
        }

        Cmd[i] = request;
    }



    ret = Smi240SpiTransfer(0x0, &response);
    if (!smi240_sensor_data_is_valid(response))
        return -1;

    reg_data[i - 1] = (uint16_t)((response>>4)&(0xFFFF));
    
    return ret;
}

//软件重启
static int Smi240SoftReset(void)
{
    int ret;
    uint16_t data = SMI240_SOFT_RESET_CMD;

    ret = Smi240SetRegs(SMI240_CMD_REG, &data, 1);
    board_delay_ms(120);
    return ret;
}

//SMI240自测
static int Smi240SelfTest(void)
{
	int ret;
	uint16_t response[7];
	uint16_t request = SMI240_BITE_CMD;

	ret = Smi240SetRegs(SMI240_BITE_CMD_REG, &request, 1);
	board_delay_ms(500);

	if (ret) 
        {
            return -1;
	}

	return Smi240GetRegs(SMI240_TEMP_CAP_REG, response, 7, 1);
}

//SMI240初始化配置
static int Smi240SoftConfig(void)
{
    int ret;
    uint16_t request = 0x1;
    uint16_t Reg_data[1] = {0};

    request &= ~(0x1<<1);//陀螺仪带宽为400Hz    0:400Hz,1:50hZ
    request &= ~(0x1<<2);//加速度计带宽为400Hz  0:400Hz,1:50hZ
    //request |= (0x1<<1);//陀螺仪带宽为50Hz    0:400Hz,1:50hZ
    //request |= (0x1<<2);//加速度计带宽为50Hz  0:400Hz,1:50hZ

    request |= (0x1<<3);//配置BITE是否自动执行
    request |= (0x2<<4);//配置在BITE失败时的自动重复次数为2

    ret = Smi240SetRegs(SMI240_SIGN_SFT_CFG_REG, Reg_data,1);
    ret |= Smi240SetRegs(SMI240_SOFT_CONFIG_REG, &request, 1);

    //uint16_t response;
    //Smi240GetRegs(SMI240_SIGN_SFT_CFG_REG, &response, 1, 0);

    board_delay_ms(580);

    return ret;
}


int Smi240Init(void)
{
    int ret;
    uint16_t response;

    ret = Smi240GetRegs(SMI240_CHIP_ID_REG, &response, 1, 0);
    if (ret) 
    {
        return ret;
    }
    if (response != SMI240_CHIP_ID) 
    {
        return -1;
    }

    ret = Smi240SoftReset();
    if (ret) 
    {
        return ret;
    }

    ret = Smi240SoftConfig();//寄存器初始化配置
    if (ret)
    {
        return ret;
    }

    return ret;
}

void ReadSmi240Data(float *ImuData,int16_t *data)
{
    int ret;

    ret = Smi240GetRegs(SMI240_TEMP_CAP_REG, data, 7, 1);

    //data[0] >>= SMI240_TEMPERATURE_SHIFT;
    //data[0] += SMI240_TEMPERATURE_BASE;

    ImuData[0] = (data[0]/256.0)+25.0;//Temperature

    ImuData[1] = data[1]/2000.0;//Accel X axis
    ImuData[2] = data[2]/2000.0;//Accel Y axis
    ImuData[3] = data[3]/2000.0;//Accel Z axis

    ImuData[4] = data[4]/100.0;//Gyro X axis
    ImuData[5] = data[5]/100.0;//Gyro Y axis
    ImuData[6] = data[6]/100.0;//Gyro Z axis


}



