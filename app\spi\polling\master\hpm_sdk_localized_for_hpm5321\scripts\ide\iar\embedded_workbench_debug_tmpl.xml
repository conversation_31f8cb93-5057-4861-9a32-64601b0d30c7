<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CSPYInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYDynDriver</name>
                    <state>IJETRISCV</state>
                </option>
                <option>
                    <name>CSPYRunToEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYRunoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSPYMacOverride</name>
                    {%- if target["iar_mac_file"] %}
                    <state>1</state>
                    {%- else %}
                    <state>0</state>
                    {%- endif %}
                </option>
                <option>
                    <name>CSPYMacFile</name>
                    <state>{{ target["iar_mac_file"] }}</state>
                </option>
                <option>
                    <name>CSPYMemOverride</name>
                    {%- if target["iar_mem_file"] %}
                    <state>1</state>
                    {%- else %}
                    <state>0</state>
                    {%- endif %}
                </option>
                <option>
                    <name>CSPYMemFile</name>
                    <state>{{ target["iar_mem_file"] }}</state>
                </option>
                <option>
                    <name>CSPYMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYDDFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OCDownloadSuppressDownload</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCDownloadVerifyAll</name>
                    <state>0</state>
                </option>
                <option>
                    <name>UseFlashLoader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OverrideDefFlashBoard</name>
                    {%- if target["iar_flashloader"] %}
                    <state>1</state>
                    {%- else %}
                    <state>0</state>
                    {%- endif %}
                </option>
                <option>
                    <name>FlashLoaders</name>
                    <state>{{ target["iar_flashloader"] }}</state>
                </option>
                <option>
                    <name>MassEraseBeforeFlashing</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCMulticoreNrOfCores</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCMulticoreWorkspace</name>
                    <state>{{ target["partner1_workspace"] }}</state>
                </option>
                <option>
                    <name>OCMulticoreSlaveProject</name>
                    <state>{{ target["partner1_project_name"] }}</state>
                </option>
                <option>
                    <name>OCMulticoreSlaveConfiguration</name>
                    <state>Debug</state>
                </option>
                <option>
                    <name>OCAttachSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCMulticoreNrOfCoresSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCMulticoreAMPConfigType</name>
                    {%- if target["partner1_workspace"] %}
                    <state>1</state>
                    {%- else %}
                    <state>0</state>
                    {%- endif %}
                </option>
                <option>
                    <name>OCMulticoreSessionFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OCOverrideSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOverrideSlavePath</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>RISCVGDBSERV</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CGDBMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TCPIP</name>
                    <state>localhost,3333</state>
                </option>
                <option>
                    <name>DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>CCJTagBreakpointRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCJTagDoUpdateBreakpoints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCJTagUpdateBreakpoints</name>
                    <state>_call_main</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IJETRISCV</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OCDriverInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCIarProbeScriptFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCProbeCfgOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCProbeConfig</name>
                    <state></state>
                </option>
                <option>
                    <name>IjetProbeConfigRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetSelectedCPUBehaviour</name>
                    <state></state>
                </option>
                <option>
                    <name>ICpuName</name>
                    <state></state>
                </option>
                <option>
                    <name>IjetResetList</name>
                    <version>0</version>
                    {%- if target["is_secondary_core"] == "1" %}
                    <state>0</state>
                    {%- else %}
                    <state>4</state>
                    {%- endif %}
                </option>
                <option>
                    <name>IjetHWResetDuration</name>
                    <state>300</state>
                </option>
                <option>
                    <name>IjetHWResetDelay</name>
                    <state></state>
                </option>
                <option>
                    <name>IjetPowerFromProbe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetPowerRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIjetUsbSerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIjetUsbSerialNoSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetDoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetLogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>IjetInterfaceRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetInterfaceCmdLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiTargetEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiTarget</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetScanChainNonRISCVDevices</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetIRLength</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiCPUEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiCPUNumber</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetJtagSpeedList</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetBreakpointRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetRestoreBreakpointsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetUpdateBreakpointsEdit</name>
                    <state>_call_main</state>
                </option>
                <option>
                    <name>RDICatchReset</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CatchDummy</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCJetEmuParams</name>
                    <state>1</state>
                </option>
                <option>
                    <name>FlashBoardPathSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchNmi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrMis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrFault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchIllegalInstr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadMis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadFault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStoreAddr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStoreAccess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchEnvironment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrPage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadPage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStorePage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchExternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchTimer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchSoftware</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeM</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeS</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeU</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDelayAfterOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IJETStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IjetSystemBusAccess</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCJetSigProbeOpt</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>SIMMandatory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>THIRDPARTY_ID</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OCDriverInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CThirdPartyDriverDll</name>
                    <state>###Uninitialized###</state>
                </option>
                <option>
                    <name>CThirdPartyLogFileCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CThirdPartyLogFileEditB</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>CThirdPartyDriverOpt</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>C-SPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>2</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CSPYInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYProcessor</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYDynDriver</name>
                    <state>IJETRISCV</state>
                </option>
                <option>
                    <name>CSPYRunToEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYRunoToName</name>
                    <state>main</state>
                </option>
                <option>
                    <name>CSPYMacOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMacFile</name>
                    <state>{{ target["iar_mac_file"] }}</state>
                </option>
                <option>
                    <name>CSPYMemOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYMemFile</name>
                    <state>{{ target["iar_mem_file"] }}</state>
                </option>
                <option>
                    <name>CSPYMandatory</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYDDFileSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesSuppressCheck3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesPath3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset1</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset2</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesOffset3</name>
                    <state></state>
                </option>
                <option>
                    <name>CSPYImagesUse1</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYImagesUse3</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CSPYExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>OCDownloadSuppressDownload</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCDownloadVerifyAll</name>
                    <state>0</state>
                </option>
                <option>
                    <name>UseFlashLoader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OverrideDefFlashBoard</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FlashLoaders</name>
                    <state>{{ target["iar_flashloader"] }}</state>
                </option>
                <option>
                    <name>MassEraseBeforeFlashing</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCMulticoreNrOfCores</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCMulticoreWorkspace</name>
                    <state></state>
                </option>
                <option>
                    <name>OCMulticoreSlaveProject</name>
                    <state></state>
                </option>
                <option>
                    <name>OCMulticoreSlaveConfiguration</name>
                    <state></state>
                </option>
                <option>
                    <name>OCAttachSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCMulticoreNrOfCoresSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCMulticoreAMPConfigType</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCMulticoreSessionFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OCOverrideSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOverrideSlavePath</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>RISCVGDBSERV</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CGDBMandatory</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TCPIP</name>
                    <state>localhost,3333</state>
                </option>
                <option>
                    <name>DoLogfile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>LogFile</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>CCJTagBreakpointRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCJTagDoUpdateBreakpoints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCJTagUpdateBreakpoints</name>
                    <state>_call_main</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IJETRISCV</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>3</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OCDriverInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCIarProbeScriptFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCProbeCfgOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCProbeConfig</name>
                    <state></state>
                </option>
                <option>
                    <name>IjetProbeConfigRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetSelectedCPUBehaviour</name>
                    <state></state>
                </option>
                <option>
                    <name>ICpuName</name>
                    <state></state>
                </option>
                <option>
                    <name>IjetResetList</name>
                    <version>0</version>
                    {%- if target["is_secondary_core"] == "1" %}
                    <state>0</state>
                    {%- else %}
                    <state>4</state>
                    {%- endif %}
                </option>
                <option>
                    <name>IjetHWResetDuration</name>
                    <state>300</state>
                </option>
                <option>
                    <name>IjetPowerFromProbe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetPowerRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIjetUsbSerialNo</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIjetUsbSerialNoSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetDoLogfile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IjetLogFile</name>
                    <state>$PROJ_DIR$\cspycomm_release.log</state>
                </option>
                <option>
                    <name>IjetInterfaceRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetInterfaceCmdLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiTargetEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiTarget</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetScanChainNonRISCVDevices</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetIRLength</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiCPUEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetMultiCPUNumber</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetJtagSpeedList</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetBreakpointRadio</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetRestoreBreakpointsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IjetUpdateBreakpointsEdit</name>
                    <state>_call_main</state>
                </option>
                <option>
                    <name>RDICatchReset</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CatchDummy</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OCJetEmuParams</name>
                    <state>1</state>
                </option>
                <option>
                    <name>FlashBoardPathSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchNmi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrMis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrFault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchIllegalInstr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadMis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadFault</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStoreAddr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStoreAccess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchEnvironment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchInstrPage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchLoadPage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchStorePage</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchExternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchTimer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchSoftware</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeM</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeS</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RDICatchModeU</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDelayAfterOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IJETStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IjetSystemBusAccess</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OCJetSigProbeOpt</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>SIMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>SIMMandatory</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>THIRDPARTY_ID</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OCDriverInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CThirdPartyDriverDll</name>
                    <state>Browse to your third-party driver</state>
                </option>
                <option>
                    <name>CThirdPartyLogFileCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CThirdPartyLogFileEditB</name>
                    <state>$PROJ_DIR$\cspycomm.log</state>
                </option>
                <option>
                    <name>CThirdPartyDriverOpt</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <debuggerPlugins>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\OpenRTOS\OpenRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$TOOLKIT_DIR$\plugins\rtos\SafeRTOS\SafeRTOSPlugin.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\Orti\Orti.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\TargetAccessServer\TargetAccessServer.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
            <plugin>
                <file>$EW_DIR$\common\plugins\uCProbe\uCProbePlugin.ENU.ewplugin</file>
                <loadFlag>0</loadFlag>
            </plugin>
        </debuggerPlugins>
    </configuration>
</project>
