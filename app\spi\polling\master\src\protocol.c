//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：protocol.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.24
//---------------------------------------------------------

#include "uart_dma.h"
#include "uart_Irq.h"
#include "spi.h"
#include "protocol.h"
#include "arithmetic.h"

Smi240_Data stSmi240Data;

uint8_t  Check_8bit(uint8_t* srcbuf,uint32_t srcqty)
{
    uint32_t tmpvlu = 0;
    for(uint32_t i = 0; i < srcqty; i++)
    {
        tmpvlu += *srcbuf++;
    }

    return (uint8_t)tmpvlu;
}


uint16_t  Check_16bit(uint8_t* srcbuf,uint32_t srcqty)
{
    uint32_t tmpvlu = 0;
    for(uint32_t i = 0; i <= srcqty; i++)
    {
        tmpvlu += *srcbuf++;
    }

    return (uint16_t)tmpvlu;
}

//异或校验
uint8_t xor_check(uint8_t *buf, uint16_t len)
{
    uint16_t i = 0;
    uint8_t x = 0;

    for(; i < len; i++)
    {
        x = x ^ (*(buf + i));
    }

    return x;
}

void GetSmi240Data(float *ImuData)
{
    stSmi240Data.temp_UNO = ImuData[0];//Temperature

    stSmi240Data.accel_x = ImuData[1];//Accel X axis
    stSmi240Data.accel_y = ImuData[2];//Accel Y axis
    stSmi240Data.accel_z = ImuData[3];//Accel Z axis

    stSmi240Data.gyro_x = ImuData[4];//Gyro X axis
    stSmi240Data.gyro_y = ImuData[5];//Gyro Y axis
    stSmi240Data.gyro_z = ImuData[6];//Gyro Z axis

}

 //Smi240原始数据进入算法接口
void Smi240DataToAlgorithm(p_Compen lp_Compen)
{
    combineData.time = TimeStamp;//更新时间戳到算法

    combineData.temp_UNO = stSmi240Data.temp_UNO;
    
    combineData.accel_x = lp_Compen->Acc[0];
    combineData.accel_y = lp_Compen->Acc[1];
    combineData.accel_z = lp_Compen->Acc[2];

    combineData.gyro_x = lp_Compen->Gyro[0];
    combineData.gyro_y = lp_Compen->Gyro[1];
    combineData.gyro_z = lp_Compen->Gyro[2];
}

//原始数据输出
void Smi240UartSend(float *txbuf)
{
    Imubag_Send Smi240DataBao;
    uint8_t SendDatabuf[sizeof(Imubag_Send)]={0};
    memset(&Smi240DataBao, 0, sizeof(Imubag_Send));

    //包头
    Smi240DataBao.head.header[0] = 0x4A;
    Smi240DataBao.head.header[1] = 0x02;
    Smi240DataBao.head.cmd[0] = 0x0A;
    Smi240DataBao.head.cmd[1] = 0x01;

    Smi240DataBao.info.ttemp = TimeStamp;//系统惯导时间

    Smi240DataBao.info.temp_UNO    = txbuf[0];
    Smi240DataBao.info.accel_x = txbuf[1];
    Smi240DataBao.info.accel_y = txbuf[2];
    Smi240DataBao.info.accel_z = txbuf[3];
    Smi240DataBao.info.gyro_x  = txbuf[4];
    Smi240DataBao.info.gyro_y  = txbuf[5];
    Smi240DataBao.info.gyro_z  = txbuf[6];

    memcpy(SendDatabuf,&Smi240DataBao, sizeof(Imubag_Send));

    //包尾
    Smi240DataBao.ender.check = Check_8bit(SendDatabuf+4,32);

    //UartDMASend((char*)&Smi240DataBao, sizeof(Imubag_Send));
    if(g_StartUpdateFirm ==0)
    {
        UartIrqSendMsg((char*)&Smi240DataBao, sizeof(Imubag_Send));
    }
}

//原始数据输出
void Smi240Spi2Send(float *txbuf)
{
    Imubag_Send Smi240DataBao;
    uint8_t SendDatabuf[sizeof(Imubag_Send)]={0};
    memset(&Smi240DataBao, 0, sizeof(Imubag_Send));

    //包头
    Smi240DataBao.head.header[0] = 0x4A;
    Smi240DataBao.head.header[1] = 0x02;
    Smi240DataBao.head.cmd[0] = 0x0A;
    Smi240DataBao.head.cmd[1] = 0x01;

    Smi240DataBao.info.ttemp = TimeStamp;//系统惯导时间

    Smi240DataBao.info.temp_UNO    = txbuf[0];
    Smi240DataBao.info.accel_x = txbuf[1];
    Smi240DataBao.info.accel_y = txbuf[2];
    Smi240DataBao.info.accel_z = txbuf[3];
    Smi240DataBao.info.gyro_x  = txbuf[4];
    Smi240DataBao.info.gyro_y  = txbuf[5];
    Smi240DataBao.info.gyro_z  = txbuf[6];

    memcpy(SendDatabuf,&Smi240DataBao, sizeof(Imubag_Send));

    //包尾
    Smi240DataBao.ender.check = Check_8bit(SendDatabuf+4,32);

    SpiSlaveSend((char*)&Smi240DataBao, sizeof(Imubag_Send));
}

//组合惯导数据输出65字节
void CombinationUartSend(float *txbuf)
{
    DATA_STREAM Smi240DataBao;
    uint8_t SendDatabuf[sizeof(DATA_STREAM)]={0};
    memset(&Smi240DataBao, 0, sizeof(DATA_STREAM));

    //包头
    Smi240DataBao.header[0] = 0xBD;
    Smi240DataBao.header[1] = 0xDB;
    Smi240DataBao.header[2] = 0x0B;

    Smi240DataBao.accelX = txbuf[1]*(32768.0/12.0);
    Smi240DataBao.accelY = txbuf[2]*(32768.0/12.0);
    Smi240DataBao.accelZ = txbuf[3]*(32768.0/12.0);
    Smi240DataBao.gyroX  = txbuf[4]*(32768.0/300.0);
    Smi240DataBao.gyroY  = txbuf[5]*(32768.0/300.0);
    Smi240DataBao.gyroZ  = txbuf[6]*(2147483648.0/600.0);

    //memcpy(SendDatabuf,&Smi240DataBao, sizeof(DATA_STREAM));

    Smi240DataBao.xor_verify1 = xor_check(Smi240DataBao.header, sizeof(DATA_STREAM) - 6);
    Smi240DataBao.xor_verify2 = xor_check(Smi240DataBao.header, sizeof(DATA_STREAM) - 1);

    //UartDMASend((char*)&Smi240DataBao, sizeof(DATA_STREAM));
    if(g_StartUpdateFirm ==0)
    {
        UartIrqSendMsg((char*)&Smi240DataBao, sizeof(DATA_STREAM));
    }

}

//组合惯导数据输出65字节
void CombinationSpi2Send(float *txbuf)
{
    DATA_STREAM Smi240DataBao;
    uint8_t SendDatabuf[sizeof(DATA_STREAM)]={0};
    memset(&Smi240DataBao, 0, sizeof(DATA_STREAM));

    //包头
    Smi240DataBao.header[0] = 0xBD;
    Smi240DataBao.header[1] = 0xDB;
    Smi240DataBao.header[2] = 0x0B;


    Smi240DataBao.accelX = txbuf[1]*(32768.0/12.0);
    Smi240DataBao.accelY = txbuf[2]*(32768.0/12.0);
    Smi240DataBao.accelZ = txbuf[3]*(32768.0/12.0);
    Smi240DataBao.gyroX  = txbuf[4]*(32768.0/300.0);
    Smi240DataBao.gyroY  = txbuf[5]*(32768.0/300.0);
    Smi240DataBao.gyroZ  = txbuf[6]*(2147483648.0/600.0);

    //memcpy(SendDatabuf,&Smi240DataBao, sizeof(DATA_STREAM));

    Smi240DataBao.xor_verify1 = xor_check(Smi240DataBao.header, sizeof(DATA_STREAM) - 6);
    Smi240DataBao.xor_verify2 = xor_check(Smi240DataBao.header, sizeof(DATA_STREAM) - 1);

    SpiSlaveSend((char*)&Smi240DataBao, sizeof(DATA_STREAM));

}

//组合惯导数据输出22字节
void CombinationUartSend22B(float *txbuf)
{
    DATA_STREAM_22B Smi240DataBao;
    uint8_t SendDatabuf[sizeof(DATA_STREAM_22B)]={0};
    memset(&Smi240DataBao, 0, sizeof(DATA_STREAM_22B));

    //包头
    Smi240DataBao.header[0] = 0xCD;
    Smi240DataBao.header[1] = 0xDC;
    Smi240DataBao.header[2] = 0x0B;

    Smi240DataBao.accelX = txbuf[1]*(32768.0/12.0);
    Smi240DataBao.accelY = txbuf[2]*(32768.0/12.0);
    Smi240DataBao.accelZ = txbuf[3]*(32768.0/12.0);
    Smi240DataBao.gyroX  = txbuf[4]*(32768.0/300.0);
    Smi240DataBao.gyroY  = txbuf[5]*(32768.0/300.0);
    Smi240DataBao.gyroZ  = txbuf[6]*(32768.0/300.0);

    //memcpy(SendDatabuf,&Smi240DataBao, sizeof(DATA_STREAM_22B));

    Smi240DataBao.CheckXor = xor_check(Smi240DataBao.header, sizeof(DATA_STREAM_22B) - 1);

    //UartDMASend((char*)&Smi240DataBao, sizeof(DATA_STREAM_22B));
    if(g_StartUpdateFirm ==0)
    {
          UartIrqSendMsg((char*)&Smi240DataBao, sizeof(DATA_STREAM_22B));
    }

}


//纯惯导数据输出67字节
void PureUartSend(p_InavOutDataDef lp_InavOutData)
{
    PureImubagData Smi240DataBao;
    uint8_t SendDatabuf[sizeof(PureImubagData)]={0};
    memset(&Smi240DataBao, 0, sizeof(PureImubagData));

    //包头
    Smi240DataBao.header[0] = 0x3A;
    Smi240DataBao.header[1] = 0x01;
    Smi240DataBao.header[2] = 0x00;
    Smi240DataBao.header[3] = 0x09;
    Smi240DataBao.header[4] = 0x00;
    Smi240DataBao.len = sizeof(PureImubagData_info);

    Smi240DataBao.info.ttemp = TimeStamp;//系统惯导时间

    Smi240DataBao.info.accel_x = lp_InavOutData->ACCEL_X;
    Smi240DataBao.info.accel_y = lp_InavOutData->ACCEL_Y;
    Smi240DataBao.info.accel_z = lp_InavOutData->ACCEL_Z;
    Smi240DataBao.info.gyro_x  = lp_InavOutData->GYRO_X;
    Smi240DataBao.info.gyro_y  = lp_InavOutData->GYRO_Y;
    Smi240DataBao.info.gyro_z  = lp_InavOutData->GYRO_Z;

    Smi240DataBao.info.Quart_W = lp_InavOutData->Quart_W;
    Smi240DataBao.info.Quart_X = lp_InavOutData->Quart_X;
    Smi240DataBao.info.Quart_Y = lp_InavOutData->Quart_Y;
    Smi240DataBao.info.Quart_Z = lp_InavOutData->Quart_Z;

    memcpy(SendDatabuf,&Smi240DataBao, sizeof(PureImubagData));

        //包尾
    Smi240DataBao.CheckSum = Check_16bit(SendDatabuf+1,62);
    Smi240DataBao.ender[0] = 0x0D;
    Smi240DataBao.ender[1] = 0x0A;

    //UartDMASend((char*)&Smi240DataBao, sizeof(PureImubagData));
    if(g_StartUpdateFirm ==0)
    {
        UartIrqSendMsg((char*)&Smi240DataBao, sizeof(PureImubagData));
    }
}


//纯惯导数据输出67字节
void PureSpi2Send(float *txbuf)//目前输出原始IMU数据*********
{
    PureImubagData Smi240DataBao;
    uint8_t SendDatabuf[sizeof(PureImubagData)]={0};
    memset(&Smi240DataBao, 0, sizeof(PureImubagData));

    //包头
    Smi240DataBao.header[0] = 0x3A;
    Smi240DataBao.header[1] = 0x01;
    Smi240DataBao.header[2] = 0x00;
    Smi240DataBao.header[3] = 0x09;
    Smi240DataBao.header[4] = 0x00;
    Smi240DataBao.len = sizeof(PureImubagData_info);

    Smi240DataBao.info.ttemp = TimeStamp;//系统惯导时间

    Smi240DataBao.info.accel_x = txbuf[1];
    Smi240DataBao.info.accel_y = txbuf[2];
    Smi240DataBao.info.accel_z = txbuf[3];
    Smi240DataBao.info.gyro_x  = txbuf[4];
    Smi240DataBao.info.gyro_y  = txbuf[5];
    Smi240DataBao.info.gyro_z  = txbuf[6];

    memcpy(SendDatabuf,&Smi240DataBao, sizeof(PureImubagData));

        //包尾
    Smi240DataBao.CheckSum = Check_16bit(SendDatabuf+1,62);
    Smi240DataBao.ender[0] = 0x0D;
    Smi240DataBao.ender[1] = 0x0A;

    SpiSlaveSend((char*)&Smi240DataBao, sizeof(PureImubagData));
}

//纯惯导数据输出36字节
void PureUartSend36B(p_InavOutDataDef lp_InavOutData)
{
    PureImubagData_36B Smi240DataBao;
    memset(&Smi240DataBao, 0, sizeof(PureImubagData_36B));

    //包头
    Smi240DataBao.header[0] = 0x3B;
    Smi240DataBao.header[1] = 0x02;
    Smi240DataBao.header[2] = 0x0B;

    Smi240DataBao.ttemp = TimeStamp;//系统惯导时间

    Smi240DataBao.accelX = lp_InavOutData->ACCEL_X *(2047.9375f);
    Smi240DataBao.accelY = lp_InavOutData->ACCEL_Y *(2047.9375f);
    Smi240DataBao.accelZ = lp_InavOutData->ACCEL_Z *(2047.9375f);
    Smi240DataBao.gyroX  = lp_InavOutData->GYRO_X * (109.2233f);
    Smi240DataBao.gyroY  = lp_InavOutData->GYRO_Y * (109.2233f);
    Smi240DataBao.gyroZ  = lp_InavOutData->GYRO_Z * (109.2233f);

    Smi240DataBao.Quart_W = lp_InavOutData->Quart_W;
    Smi240DataBao.Quart_X = lp_InavOutData->Quart_X;
    Smi240DataBao.Quart_Y = lp_InavOutData->Quart_Y;
    Smi240DataBao.Quart_Z = lp_InavOutData->Quart_Z;

    //校验
    Smi240DataBao.CheckXor = xor_check(Smi240DataBao.header, sizeof(PureImubagData_36B) - 1);

    //UartDMASend((char*)&Smi240DataBao, sizeof(PureImubagData_36B));
    if(g_StartUpdateFirm ==0)
    {
        UartIrqSendMsg((char*)&Smi240DataBao, sizeof(PureImubagData_36B));
    }
}






