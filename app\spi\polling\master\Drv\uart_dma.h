//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.23
//---------------------------------------------------------

#ifndef _UART_DMA_H
#define _UART_DMA_H

#include "protocol.h"


//RS232
#define TEST_UART                      HPM_UART6
#define TEST_UART_CLK_NAME             clock_uart6
#define TEST_UART_TX_DMA_REQ           HPM_DMA_SRC_UART6_TX
#define TEST_UART_RX_DMA_REQ           HPM_DMA_SRC_UART6_RX

#define TEST_UART_DMA_CONTROLLER       HPM_HDMA
#define TEST_UART_DMAMUX_CONTROLLER    HPM_DMAMUX
#define TEST_UART_TX_DMA_CHN           (0U)
#define TEST_UART_RX_DMA_CHN           (1U)
#define TEST_UART_TX_DMAMUX_CHN        DMA_SOC_CHN_TO_DMAMUX_CHN(TEST_UART_DMA_CONTROLLER, TEST_UART_TX_DMA_CHN)
#define TEST_UART_RX_DMAMUX_CHN        DMA_SOC_CHN_TO_DMAMUX_CHN(TEST_UART_DMA_CONTROLLER, TEST_UART_RX_DMA_CHN)
#define TEST_UART_DMA_IRQ              IRQn_HDMA


#define TEST_BUFFER_SIZE   (100U)


/**********************************声明函数******************************************/
void UartDMASend(char *txbuf, int size);

void UartDMARecStart(int BagSize);

void UartDMAInit(void);

#endif