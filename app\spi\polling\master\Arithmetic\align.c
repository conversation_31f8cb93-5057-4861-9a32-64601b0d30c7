//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// align.c
// 
// 
//
// V1.0
//     LiJunWei
// 2025.05.12
//---------------------------------------------------------
#include "align.h"
#include <math.h>
#define GainHeadingfromAlignment

void ComputeCen(LATI r_Lati, LOGI r_Logi, MATR Cen[9])
{
    register DPARA SINLATI = sin(r_<PERSON>ti);
    register DPARA COSLATI = cos(r_Lati);
    register DPARA SINLOGI = sin(r_Logi);
    register DPARA COSLOGI = cos(r_Logi);

    Cen[0] = -1 * SINLATI * COSLOGI;
    Cen[1] = -1 * SINLATI * SINLOGI;
    Cen[2] = COSLATI;
    Cen[3] = COSLATI * COSLOGI;
    Cen[4] = COSLATI * SINLOGI;
    Cen[5] = SINLATI;
    Cen[6] = -1 * SINLOGI;
    Cen[7] = COSLOGI;
    Cen[8] = 0;
}

void ComputeCie(TIME AlignTime, MATR Cie[9])
{
    Cie[0] = cos(WIE * AlignTime);
    Cie[1] = sin(WIE * AlignTime);
    Cie[2] = 0.0;

    Cie[3] = -1 * sin(WIE * AlignTime);
    Cie[4] = cos(WIE * AlignTime);
    Cie[5] = 0.0;

    Cie[6] = 0.0;
    Cie[7] = 0.0;
    Cie[8] = 1.0;
}

void ComputeCib0i(VEL Vi[3], VEL Vib0[3], VEL Vi_T1[3], VEL Vib0_T1[3], MATR Cib0i[9])
{
    //IPARA i;
    VEC Vector_1[3];
    VEC Vector_2[3];

    VEC Vector_3[3];
    VEC Vector_4[3];

    MATR Matr_1[9];
    MATR Matr_2[9];

    MATR InvMatr_1[9]; //Matr_1�������

    Vec_Cross(Vi_T1, Vi, Vector_1);
    Vec_Cross(Vib0_T1, Vib0, Vector_3);

    Vec_Cross(Vector_1, Vi_T1, Vector_2);
    Vec_Cross(Vector_3, Vib0_T1, Vector_4);

    Matr_1[0] = Vi_T1[0];
    Matr_1[1] = Vi_T1[1];
    Matr_1[2] = Vi_T1[2];

    Matr_1[3] = Vector_1[0];
    Matr_1[4] = Vector_1[1];
    Matr_1[5] = Vector_1[2];

    Matr_1[6] = Vector_2[0];
    Matr_1[7] = Vector_2[1];
    Matr_1[8] = Vector_2[2];

    Matr_2[0] = Vib0_T1[0];
    Matr_2[1] = Vib0_T1[1];
    Matr_2[2] = Vib0_T1[2];

    Matr_2[3] = Vector_3[0];
    Matr_2[4] = Vector_3[1];
    Matr_2[5] = Vector_3[2];

    Matr_2[6] = Vector_4[0];
    Matr_2[7] = Vector_4[1];
    Matr_2[8] = Vector_4[2];

    //�����Cib0i
    Mat_Inv(Matr_1, InvMatr_1, 3);
    Mat_Mul(InvMatr_1, Matr_2, Cib0i, 3, 3, 3);
}

void InertialSysAlign_Init(p_InitBind const lp_InitBind, p_InertialSysAlign lp_InertialSysAlign)
{
    //�ߵ�λ�ó�ʼ��
    lp_InertialSysAlign->r_Lati = lp_InitBind->r_InitLati;  //γ��,��λ��rad
    lp_InertialSysAlign->r_Logi = lp_InitBind->r_InitLogi;  //���ȣ���λ��rad
    lp_InertialSysAlign->Height = lp_InitBind->InitHeight;  //�߶ȣ���λ��m
    //Qbib0�ĳ�ʼ����Cbib0�Լ�Cib0b������Ԫ�������ã������ʼ��
    lp_InertialSysAlign->Qbib0[0] = 1.0;
    lp_InertialSysAlign->Qbib0[1] = 0.0;
    lp_InertialSysAlign->Qbib0[2] = 0.0;
    lp_InertialSysAlign->Qbib0[3] = 0.0;

    //�����׼�м�ʱ���t1��ʱ��
    lp_InertialSysAlign->T1 = lp_InertialSysAlign->AlignEndTime / 2.0;
    //T1ʱ���ٶ�ʸ����¼��־ΪNO
    lp_InertialSysAlign->isT1Record = NO;
    //��������׼������Cenȡ���ھ�γ�ȣ������׼ʱ��ı䣬ֱ�ӳ�ʼ��
    //����Cen�������Ҿ���
    ComputeCen(lp_InertialSysAlign->r_Lati, lp_InertialSysAlign->r_Logi, lp_InertialSysAlign->Cen);
}

void InertialSysAlignCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3], DPARA const LastAcc[3], p_InertialSysAlign lp_InertialSysAlign) 
{
    register IPARA i;
    //BOOL isAlignNormal;
    //�������ݡ��ӱ���ֵ��д���ת��
    for (i = 0; i < 3; i++)
    {
        lp_InertialSysAlign->r_Wibb[0][i] = LastGyro[i];
        lp_InertialSysAlign->r_Wibb[1][i] = Gyro[i];
        lp_InertialSysAlign->Fibb[i] = 0.5 * (Acc[i] + LastAcc[i]);
    }
    //��׼����
    ComputeCie(lp_InertialSysAlign->AlignTime, lp_InertialSysAlign->Cie);

    ComputeDelSenbb(lp_InertialSysAlign->r_Wibb, lp_InertialSysAlign->r_DelSenbb_1, lp_InertialSysAlign->r_DelSenbb_2, lp_InertialSysAlign->r_DelSenbb);

    ComputeQ(lp_InertialSysAlign->r_DelSenbb, lp_InertialSysAlign->Qbib0);//����navi.c��computeQ��������Qbib0

    QToCnb(lp_InertialSysAlign->Qbib0, lp_InertialSysAlign->Cib0b);//����navi.c��QToCnb����Cib0b

    Mat_Tr(lp_InertialSysAlign->Cib0b, lp_InertialSysAlign->Cbib0, 3, 3);//����Cbib0

    ComputeG(lp_InertialSysAlign->r_Lati, lp_InertialSysAlign->Height, &lp_InertialSysAlign->Gn);//����navi.c��ComputeG��������ֵ

    ComputeVi(lp_InertialSysAlign->Gn, lp_InertialSysAlign->Cie, lp_InertialSysAlign->Cen, lp_InertialSysAlign->Vi);

    ComputeVib0(lp_InertialSysAlign->Fibb, lp_InertialSysAlign->Cbib0, lp_InertialSysAlign->Vib0);

    lp_InertialSysAlign->AlignTime += TIME_NAVI;
    lp_InertialSysAlign->AlignCount++;
    //��¼t1ʱ�̵�Vi��Vib0
    if ((lp_InertialSysAlign->AlignTime >= lp_InertialSysAlign->T1) && (lp_InertialSysAlign->isT1Record == NO))
    {
        for (i = 0; i < 3; i++)
        {
            lp_InertialSysAlign->Vi_T1[i] = lp_InertialSysAlign->Vi[i];
            lp_InertialSysAlign->Vib0_T1[i] = lp_InertialSysAlign->Vib0[i];
        }
        lp_InertialSysAlign->isT1Record = YES;
    }
    //return isAlignNormal;
}

void FinishInertialSysAlign(p_InertialSysAlign lp_InertialSysAlign)
{
    MATR Cbi[9];
    MATR Cbe[9];
    MATR Cbn[9];

    ComputeCib0i(lp_InertialSysAlign->Vi, lp_InertialSysAlign->Vib0, lp_InertialSysAlign->Vi_T1, lp_InertialSysAlign->Vib0_T1, lp_InertialSysAlign->Cib0i);
    //������������˻����Cbn
    Mat_Mul(lp_InertialSysAlign->Cib0i, lp_InertialSysAlign->Cbib0, Cbi, 3, 3, 3);
    Mat_Mul(lp_InertialSysAlign->Cie, Cbi, Cbe, 3, 3, 3);
    Mat_Mul(lp_InertialSysAlign->Cen, Cbe, Cbn, 3, 3, 3);
    //ת�û��Cnb
    Mat_Tr(Cbn, lp_InertialSysAlign->AlignCnb, 3, 3);
    CnbToAtti(lp_InertialSysAlign->AlignCnb, lp_InertialSysAlign->r_AlignAtti);  //����navi.c��CnbToAtti����
    AttiToCnb(lp_InertialSysAlign->r_AlignAtti, lp_InertialSysAlign->AlignCnb);  //������̬�ع�һ����������Cnb������navi.c��AttiToCnb����
    CnbToQ(lp_InertialSysAlign->AlignCnb, lp_InertialSysAlign->AlignQ);    //����navi.c��CnbToQ����
    lp_InertialSysAlign->isAlign_Finish = YES;
}

void ComputeVi(ACCELER Gn, MATR Cie[9], MATR Cen[9], VEL Vi[3])
{
    IPARA i;
    MATR Cei[9];
    MATR Cne[9];
    ACCELER Fn[3];
    ACCELER Fe[3];
    ACCELER Fi[3];

    Fn[0] = 0.0;
    Fn[1] = Gn;
    Fn[2] = 0.0;    //���춫����

    Mat_Tr(Cie, Cei, 3, 3);  //ת��
    Mat_Tr(Cen, Cne, 3, 3);  //ת��
    Mat_Mul(Cne, Fn, Fe, 3, 3, 1);
    Mat_Mul(Cei, Fe, Fi, 3, 3, 1);

    for (i = 0; i < 3; i++)
    {
        Vi[i] += Fi[i] * TIME_NAVI;
    }
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�ComputeVib0                                                                                     */
/*������������������iϵ���������ۻ���Vib0                                                                   */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*���������                                                                                                */
/*�����������                                                                                              */
/*����������                                                                                                */
/*��ע1��                                                                                                   */
/*����ֵ����                                                                                                */
/************************************************************************************************************/
void ComputeVib0(ACCELER Fibb[3], MATR Cbib0[9], VEL Vib0[3])
{
    IPARA i;

    ACCELER Fib0[3];

    Mat_Mul(Cbib0, Fibb, Fib0, 3, 3, 1);

    for (i = 0; i < 3; i++)
    {
        Vib0[i] += Fib0[i] * TIME_NAVI;
    }
}
