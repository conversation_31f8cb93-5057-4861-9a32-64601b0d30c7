Output/Debug/Obj/spi_polling_master_example\ -\ hpm5321/soc/HPM5300/HPM5361/boot/hpm_bootheader.c.o: \
 C:\Users\<USER>\Desktop\code\7.23\Smi240\Smi240\app\spi\polling\master\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\boot\hpm_bootheader.c \
 C:\Users\<USER>\Desktop\code\7.23\Smi240\Smi240\app\spi\polling\master\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\boot\hpm_bootheader.h \
 ../../hpm_sdk_localized_for_hpm5321/drivers/inc/hpm_common.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/assert.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_ConfDefaults.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/__SEGGER_RTL_RISCV_Conf.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdbool.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdint.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/string.h \
 D:/Program\ Files/SEGGER/SEGGER\ Embedded\ Studio\ 8.16b/include/stdlib.h
