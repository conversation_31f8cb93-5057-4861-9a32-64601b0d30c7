//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：Smi240.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.19
//---------------------------------------------------------
#ifndef _SMI240_H
#define _SMI240_H

#include "spi.h"

#define SMI240_BUS_ID	0x00

#define SMI240_READ_BIT  0x00
#define SMI240_WRITE_BIT 0x01

#define SMI240_CHIP_ID 0x0024
#define SMI240_CRC_INIT 0x05
#define SMI240_CRC_POLY 0x0B

//寄存器地址
#define SMI240_CHIP_ID_REG	0x00
#define SMI240_SOFT_CONFIG_REG	0x0A
#define SMI240_SIGN_SFT_CFG_REG 0x0B

#define SMI240_TEMP_CUR_REG	0x10
#define SMI240_ACCEL_X_CUR_REG	0x11
#define SMI240_ACCEL_Y_CUR_REG	0x12
#define SMI240_ACCEL_Z_CUR_REG	0x13
#define SMI240_GYRO_X_CUR_REG	0x14
#define SMI240_GYRO_Y_CUR_REG	0x15
#define SMI240_GYRO_Z_CUR_REG	0x16

#define SMI240_TEMP_CAP_REG    0x17
#define SMI240_ACCEL_X_CAP_REG 0x18
#define SMI240_ACCEL_Y_CAP_REG 0x19
#define SMI240_ACCEL_Z_CAP_REG 0x1A
#define SMI240_GYRO_X_CAP_REG  0x1B
#define SMI240_GYRO_Y_CAP_REG  0x1C
#define SMI240_GYRO_Z_CAP_REG  0x1D

#define SMI240_CMD_REG         0x2F
#define SMI240_BITE_CMD_REG    0x36

#define SMI240_SOFT_RESET_CMD 0xB6
#define SMI240_BITE_CMD	      0xB17E

#define SMI240_TEMPERATURE_BASE	 25
#define SMI240_TEMPERATURE_SHIFT 8

int Smi240Init(void);
void ReadSmi240Data(float *ImuData,int16_t *data);

#endif /* _SPI_H */