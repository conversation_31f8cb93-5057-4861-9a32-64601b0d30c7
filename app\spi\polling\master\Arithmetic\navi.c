//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// File name: navi.c
// File identifier:
// File summary:
//
// Current version: V0.1
// Author: LiJunWei
// Completion time: 2025.05.12
//---------------------------------------------------------

#include "navi.h"
#include "ZUPT.h"
#include <math.h>
#include <string.h>
void SysVarDefaultSet(p_SysVar lp_SysVar)
{
    // Set default values for system variables
    lp_SysVar->WorkPhase = PHASE_STANDBY;   // Default system working phase, see CONST.h
    lp_SysVar->Time = 0.0;                  // Default system start time, see CONST.h
    // Initialize system status variables
    g_SysVar.isGNSSValid = NO;
    g_SysVar.isGNSS_Update = NO;
    g_SysVar.isPPSStart = NO;
    g_SysVar.isRapidAlign = NO;
    g_SysVar.isFineAlign = NO;
    g_SysVar.isDampedOK = NO;
    g_InitBind.isBind = NO;
    g_InitBind.isHeadBind = NO;
    g_SysVar.isGNSSValid = NO;
}

void Sys_Init(void)
{
    // First, clear all global variables
    memset(&g_SysVar, 0, sizeof(SysVar));         // Clear all members of g_SysVar structure
    memset(&g_SelfTest, 0, sizeof(SelfTest));
    memset(&g_InitBind, 0, sizeof(InitBind));     // Clear all members of g_InitBind structure
    memset(&g_Align, 0, sizeof(Align));           // Clear all members of g_Align structure
    memset(&g_Navi, 0, sizeof(Navi));             // Clear all members of g_Navi structure
    memset(&g_Kalman, 0, sizeof(Kalman));         // Clear all members of g_Kalman structure
    memset(&g_Compen, 0, sizeof(Compen));         // Clear all members of g_Compen structure
    memset(&g_InertialSysAlign, 0, sizeof(InertialSysAlign)); // Clear all members of g_InertialSysAlign structure
    SysVarDefaultSet(&g_SysVar);                  // Set default system variables

    // Initialize alignment mode to default: rapid alignment is off
    g_SysVar.isRapidAlign = NO;
    g_SysVar.isFineAlign = NO;
}

void Bind_Init(p_InitBind lp_InitBind)
{
    lp_InitBind->r_InitLati = 22.5445741 * D2R;
    lp_InitBind->r_InitLogi = 114.0545429 * D2R;
    lp_InitBind->InitHeight = 0.0;
}

void Navi_Init(p_InitBind const lp_InitBind, p_InertialSysAlign const lp_InertialSysAlign, p_Navi lp_Navi)
{
    register IPARA ii;

    memset(lp_Navi, 0, sizeof(Navi));   // Clear all members of g_Navi structure

    // Initialize latitude, longitude, and height
    lp_Navi -> r_Lati = lp_InitBind -> r_InitLati;  // Latitude, unit: rad
    lp_Navi -> r_Logi = lp_InitBind -> r_InitLogi;  // Longitude, unit: rad
    lp_Navi -> Height = lp_InitBind -> InitHeight;  // Height

    // Set default damping height
    lp_Navi-> DampHeight = lp_InitBind -> InitHeight;

    // Initialize velocity
    for (ii = 0; ii < 3; ii++)
    {
        lp_Navi -> Vn[ii] = lp_InitBind -> InitVn[ii];   
    }

    // Initialize attitude matrix
    for (ii = 0; ii < 9; ii++)
    {
        lp_Navi -> Cnb[ii] = lp_InertialSysAlign -> AlignCnb[ii];
    }

    // Initialize attitude angles
    CnbToAtti(lp_Navi -> Cnb, lp_Navi -> r_Atti);
    // Initialize quaternion
    for (ii = 0; ii < 4; ii++)
    {
        lp_Navi -> Q[ii] = lp_InertialSysAlign -> AlignQ[ii];
    }

#ifndef GainHeadingfromAlignment
    {
        lp_Navi->r_Atti[0] = lp_InitBind->r_InitHead;
        AttiToCnb(lp_Navi->r_Atti, lp_Navi->Cnb);
        CnbToQ(lp_Navi->Cnb, lp_Navi->Q);
        lp_Navi->isHeadBind = YES;
    }
#endif
}

void NaviCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3], DPARA const LastAcc[3], p_Navi lp_Navi)
{
    register IPARA i;
    // Convert sensor data to navigation algorithm input format
    for (i = 0; i < 3; i++)
    {
        lp_Navi->r_Wibb[0][i] = LastGyro[i];
        lp_Navi->r_Wibb[1][i] = Gyro[i];
        lp_Navi->Fibb[0][i] = LastAcc[i];
        lp_Navi->Fibb[1][i] = Acc[i];
    }

    //printf("d_Logi0:%lf\n",lp_Navi ->d_Logi);
    // Navigation computation
    ComputeWien(lp_Navi->r_Lati, lp_Navi->r_Wien);// Compute angular velocity in navigation frame
    //printf("d_Atti2:%lf\n",g_Navi.d_Atti[0]);

    ComputeRmRn(lp_Navi->r_Lati, lp_Navi->Height, &lp_Navi->Rm, &lp_Navi->Rn, &lp_Navi->invRm, &lp_Navi->invRn);// Compute radius of curvature in the meridian and prime vertical

    ComputeWenn(lp_Navi->r_Lati, lp_Navi->Vn, lp_Navi->invRm, lp_Navi->invRn, lp_Navi->r_Wenn);// Compute specific force in the navigation frame

    ComputeWnbb(lp_Navi->r_Wibb, lp_Navi->r_Wien, lp_Navi->r_Wenn, lp_Navi->Cnb, lp_Navi->r_Wnbb);// Compute angular velocity in the body frame

    ComputeDelSenbb(lp_Navi->r_Wnbb, lp_Navi->r_DelSenbb_1, lp_Navi->r_DelSenbb_2, lp_Navi->r_DelSenbb);

    // ZUPT angle increment constraint function call
    // Call condition: When ZUPT flag is valid
    ZUPTAngleConstraint(lp_Navi->r_DelSenbb);

    ComputeQ(lp_Navi->r_DelSenbb, lp_Navi->Q);// Update quaternion based on angle increment

    QToCnb(lp_Navi->Q, lp_Navi->Cnb);// Convert quaternion to direction cosine matrix

    CnbToAtti(lp_Navi->Cnb, lp_Navi->r_Atti);// Convert direction cosine matrix to Euler angles

    lp_Navi->Navi_Count++;// Increment navigation computation count
    //printf("d_Logi7:%lf\n",lp_Navi ->d_Logi);
}

void ComputeDelSenbb(ANGRATE r_Wnbb[2][3], DELANG r_DelSenbb_1[3], DELANG r_DelSenbb_2[3], DELANG r_DelSenbb[3])
{
    register IPARA i;
    DELANG r_DelSenbb_Compen[3] = { 0.0 };

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_1[i] = r_Wnbb[0][i] * TIME_NAVI;
    }
    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_2[i] = r_Wnbb[1][i] * TIME_NAVI;
    }
    Vec_Cross(r_DelSenbb_1, r_DelSenbb_2, r_DelSenbb_Compen);

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb[i] = r_DelSenbb_1[i];// + 1.0 / 12.0 * r_DelSenbb_Compen[i];
    }
}

void ComputeQ(DELANG r_DelSenbb[3], QUAT Q[4])
{
    register IPARA i;
    register DPARA C;
    register DPARA S;
    register DPARA InvQSquRoot = 0.0;

    QUAT Qc[4];
    QUAT LastQ[4];

    DELANG r_Delta;

    r_Delta = sqrt(r_DelSenbb[0] * r_DelSenbb[0] + r_DelSenbb[1] * r_DelSenbb[1] + r_DelSenbb[2] * r_DelSenbb[2]);

    C = cos(0.5 * r_Delta);

    S = (fabs(r_Delta) >= MIN_DATA) ? sin(0.5 * r_Delta) / r_Delta : 0.5;// Use small angle approximation

    Qc[0] = C;
    Qc[1] = S * r_DelSenbb[0];
    Qc[2] = S * r_DelSenbb[1];
    Qc[3] = S * r_DelSenbb[2];
    // Update quaternion
    for (i = 0; i < 4; i++)
    {
        LastQ[i] = Q[i];
    }
    Qua_Mul(LastQ, Qc, Q);
    // Normalize quaternion
    InvQSquRoot = 1.0 / sqrt(Q[0] * Q[0] + Q[1] * Q[1] + Q[2] * Q[2] + Q[3] * Q[3]);
    Q[0] = Q[0] * InvQSquRoot;
    Q[1] = Q[1] * InvQSquRoot;
    Q[2] = Q[2] * InvQSquRoot;
    Q[3] = Q[3] * InvQSquRoot;
}

void ComputeG(LATI r_Lati, HEIGHT Height, ACCELER* lp_Gn)
{
    *lp_Gn = G0 * (1 + 0.0052884 * sin(r_Lati) * sin(r_Lati) - 0.0000059 * sin(2 * r_Lati) * sin(2 * r_Lati)) * (1 - 2 * Height * INVRE);
}

void ComputeRmRn(LATI r_Lati, HEIGHT Height, LEN* lp_Rm, LEN* lp_Rn, DPARA* lp_invRm, DPARA* lp_invRn)
{
    DPARA SinLati = sin(r_Lati);
    DPARA SinLatiSqure = SinLati * SinLati;
    *lp_Rm = RE - (2 * RE * F) + (3 * RE * F * SinLatiSqure) + Height;
    *lp_Rn = RE + F * RE * SinLatiSqure + Height;
    *lp_invRm = 1 / *lp_Rm;
    *lp_invRn = 1 / *lp_Rn;
}

void ComputeWien(LATI r_Lati, ANGRATE r_Wien[3])
{

    r_Wien[0] = WIE * cos(r_Lati);
    r_Wien[1] = WIE * sin(r_Lati);
    r_Wien[2] = 0.0;
}

void ComputeWenn(LATI r_Lati, VEL const Vn[3], DPARA invRm, DPARA invRn, ANGRATE r_Wenn[3])
{
    r_Wenn[0] = Vn[2] * invRn;
    r_Wenn[1] = r_Wenn[0] * tan(r_Lati);
    r_Wenn[2] = -Vn[0] * invRm;
}

void ComputeWnbb(ANGRATE r_Wibb[2][3], ANGRATE const r_Wien[3], ANGRATE const r_Wenn[3], MATR Cnb[9], ANGRATE r_Wnbb[2][3])
{
    register IPARA i = 0, j = 0;

    ANGRATE r_Winn[3], r_Winb[3];

    for (i = 0; i < 3; i++)
    {
        r_Winn[i] = r_Wien[i] + r_Wenn[i];
    }

    Mat_Mul(Cnb, r_Winn, r_Winb, 3, 3, 1);
    for (j = 0; j < 2; j++)
    {
        for (i = 0; i < 3; i++)
        {
            r_Wnbb[j][i] = r_Wibb[j][i] - r_Winb[i];
        }
    }
}

void QToCnb(QUAT const Q[4], MATR Cnb[9])
{

    Cnb[0] = (Q[0] * Q[0]) + (Q[1] * Q[1]) - (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[1] = 2 * (Q[1] * Q[2] + Q[0] * Q[3]);
    Cnb[2] = 2 * (Q[1] * Q[3] - Q[0] * Q[2]);

    Cnb[3] = 2 * (Q[1] * Q[2] - Q[0] * Q[3]);
    Cnb[4] = (Q[0] * Q[0]) - (Q[1] * Q[1]) + (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[5] = 2 * (Q[2] * Q[3] + Q[0] * Q[1]);

    Cnb[6] = 2 * (Q[1] * Q[3] + Q[0] * Q[2]);
    Cnb[7] = 2 * (Q[2] * Q[3] - Q[0] * Q[1]);
    Cnb[8] = (Q[0] * Q[0]) - (Q[1] * Q[1]) - (Q[2] * Q[2]) + (Q[3] * Q[3]);

}

void CnbToAtti(MATR const Cnb[9], ATTI r_Atti[3])
{
    // Convert direction cosine matrix to Euler angles
    // Roll angle
    if (fabs(Cnb[0]) <= MIN_DATA)
    {
        r_Atti[0] = (Cnb[2] > 0) ? -PIDIV2 : PIDIV2;
    }
    else
    {
        r_Atti[0] = -1 * atan(Cnb[2] / Cnb[0]);

        if (Cnb[0] < 0.0)
        {
            r_Atti[0] = (Cnb[2] > 0) ? r_Atti[0] - PI : r_Atti[0] + PI;
        }
    }
    // Pitch angle
    r_Atti[1] = asin(Cnb[1]);

    // Yaw angle
    if (fabs(Cnb[4]) <= MIN_DATA)
    {
        r_Atti[2] = (Cnb[7] > 0) ? -PIDIV2 : PIDIV2;
    }
    else
    {
        r_Atti[2] = -1 * atan(Cnb[7] / Cnb[4]);
        if (Cnb[4] < 0)
        {
            r_Atti[2] = (Cnb[7] > 0) ? r_Atti[2] - PI : r_Atti[2] + PI;
        }
    }
}

void AttiToCnb(ATTI const r_Atti[3], MATR Cnb[9])
{
    register DPARA SINFAI = sin(r_Atti[0]);
    register DPARA COSFAI = cos(r_Atti[0]);
    register DPARA SINSETA = sin(r_Atti[1]);
    register DPARA COSSETA = cos(r_Atti[1]);
    register DPARA SINGAMA = sin(r_Atti[2]);
    register DPARA COSGAMA = cos(r_Atti[2]);

    Cnb[0] = COSSETA * COSFAI;
    Cnb[1] = SINSETA;
    Cnb[2] = -SINFAI * COSSETA;
    Cnb[3] = SINGAMA * SINFAI - SINSETA * COSGAMA * COSFAI;
    Cnb[4] = COSGAMA * COSSETA;
    Cnb[5] = SINSETA * SINFAI * COSGAMA + SINGAMA * COSFAI;
    Cnb[6] = SINSETA * SINGAMA * COSFAI + COSGAMA * SINFAI;
    Cnb[7] = -SINGAMA * COSSETA;
    Cnb[8] = COSGAMA * COSFAI - SINGAMA * SINSETA * SINFAI;
}

void CnbToQ(MATR Cnb[9], QUAT Q[4])
{
    IPARA i;
    // If elements of Cnb are close to zero, set them to zero to avoid numerical instability
    for (i = 0; i < 9; i++)
    {
        if (fabs(Cnb[i]) <= MIN_DATA)
        {
            Cnb[i] = 0.0;
        }
    }
    Q[0] = 0.5 * sqrt(1 + Cnb[0] + Cnb[4] + Cnb[8]);

    Q[1] = (Cnb[5] >= Cnb[7]) ? (0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8]));

    Q[2] = (Cnb[6] >= Cnb[2]) ? (0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8]));

    Q[3] = (Cnb[1] >= Cnb[3]) ? (0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8]));
}
