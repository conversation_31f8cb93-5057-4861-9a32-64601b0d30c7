***********************************************************************************************
***                                                                                         ***
***                                    LINK INFORMATION                                     ***
***                                                                                         ***
***********************************************************************************************

Linker version:

  SEGGER RISC-V Linker 4.38.14 compiled Sep 10 2024 17:37:42
  Copyright (c) 2017-2024 SEGGER Microcontroller GmbH    www.segger.com


***********************************************************************************************
***                                                                                         ***
***                                     MODULE SUMMARY                                      ***
***                                                                                         ***
***********************************************************************************************

Memory use by input file:

  Object File                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  align.o                                             2 310          40                        
  AnnTempCompen.o                                    47 674                                    
  arithmetic.o                                        4 282          32                     312
  board.c.o                                           2 356         956                       4
  compen.o                                           20 590          24                        
  FirmwareUpdateFile.o                                  186                                    
  flash.o                                               444          31                     256
  hpm_bootheader.c.o                                                144                        
  hpm_clock_drv.c.o                                   1 760         140                       4
  hpm_debug_console.c.o                                 252                       4           8
  hpm_gptmr_drv.c.o                                     420                                    
  hpm_l1c_drv.c.o                                       124                                    
  hpm_pcfg_drv.c.o                                       72                                    
  hpm_pllctlv2_drv.c.o                                  700          24                        
  hpm_spi_drv.c.o                                     2 476                                    
  hpm_sysctl_drv.c.o                                    750                                    
  hpm_uart_drv.c.o                                    1 380          24                        
  main.o                                              1 210          17                  37 227
  matvecmath.o                                        2 536           8                        
  navi.o                                              6 626         152                        
  pinmux.c.o                                            330                                    
  protocol.o                                          6 664          32                      28
  reset.c.o                                             198                                    
  SetParaBao.o                                       20 426         332           5       5 957
  Smi980.o                                              494          44                        
  spi.o                                                 790       1 051                      24
  startup.s.o                                           108         292                        
  system.c.o                                            130                                    
  Timer.o                                               536                                    
  trap.c.o                                              380          64                        
  uart_dma.o                                            414                       1           1
  Uart_Irq.o                                          1 856          26           8       9 226
  ZUPT.o                                              2 286         808                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (33 objects)                             130 760       4 241          18      53 047
  ---------------------------------------------  ----------  ----------  ----------  ----------
  heapops_basic_rv32imac_balanced.a                      22                                   4
  libc_rv32imac_balanced.a                           14 114       2 456                        
  mbops_timeops_rv32imac_balanced.a                     230         549          20           4
  SEGGER_RV32_crtinit_rv32imac_balanced.a                68                                    
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (4 archives)                              14 434       3 005          20           8
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            145 194       9 190          38      85 823
  =============================================  ==========  ==========  ==========  ==========

Memory use by archive member:

  Archive member                                    RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
                                                      3 220         248                        
  fileops.o (libc_rv32imac_balanced.a)                  172                                    
  floatasmops_rv.o (libc_rv32imac_balanced.a)         3 938         256                        
  floatops.o (libc_rv32imac_balanced.a)               3 528         712                        
  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
                                                         22                                   4
  intasmops_rv.o (libc_rv32imac_balanced.a)              38                                    
  intops.o (libc_rv32imac_balanced.a)                 2 134       1 024                        
  mbops.o (mbops_timeops_rv32imac_balanced.a)           230         549          20           4
  prinops.o (libc_rv32imac_balanced.a)                  500         192                        
  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
                                                         68                                    
  strasmops_rv.o (libc_rv32imac_balanced.a)             342                                    
  strops.o (libc_rv32imac_balanced.a)                   152                                    
  utilops.o (libc_rv32imac_balanced.a)                   90          24                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (13 members from 4 archives)              14 434       3 005          20           8
  Objects (33 files)                                130 760       4 241          18      53 047
  Linker created (shared data, fills, blocks):                    1 944                  32 768
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            145 194       9 190          38      85 823
  =============================================  ==========  ==========  ==========  ==========

Memory use by linker:

  Description                                       RX Code     RO Data     RW Data     ZI Data
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Initialization table                                            1 820                        
  Memory for block 'heap'                                                                16 384
  Memory for block 'stack'                                                               16 384
  Merged section data (32-bit)                                       28                        
  Merged section data (64-bit)                                       96                        
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Subtotal (linker created):                                      1 944                  32 768
  ---------------------------------------------  ----------  ----------  ----------  ----------
  Objects (33 files)                                130 760       4 241          18      53 047
  Archives (4 files)                                 14 434       3 005          20           8
  =============================================  ==========  ==========  ==========  ==========
  Total:                                            145 194       9 190          38      85 823
  =============================================  ==========  ==========  ==========  ==========


***********************************************************************************************
***                                                                                         ***
***                                     SECTION DETAIL                                      ***
***                                                                                         ***
***********************************************************************************************

Sections by address:

  Range              Symbol or [section] Name         Size  Al  Init  Ac  Object File
  -----------------  -------------------------  ----------  --  ----  --  -----------
  00000000-00000123  __vector_table                    292  512
                                                                Init  RO  startup.s.o
  00000124-000001e1  tick_ms_isr                       190   2  Init  RX  Timer.o
  000001e2-000002bb  dma_isr                           218   2  Init  RX  uart_dma.o
  000002bc-000004a1  uart_isr                          486   2  Init  RX  Uart_Irq.o
  000004a2-0000055f  board_timer_isr                   190   2  Init  RX  board.c.o
  00000560-00000565  nmi_handler                         6   4  Init  RX  startup.s.o
  00000566-00000567  ( ALIGN .=.+2 )                     2   -  ----  -   -
  00000568-000006a1  irq_handler_trap                  314   4  Init  RX  trap.c.o
  000006a2-000006a3  ( ALIGN .=.+2 )                     2   -  ----  -   -
  000006a4-0007ffff  ( UNUSED .=.+522588 )         522 588   -  ----  -   -
  00080000-00080013  __SEGGER_RTL_global_locale
                                                        20   4  Init  RW  mbops.o (mbops_timeops_rv32imac_balanced.a)
  00080014-00080017  uiOffsetAddr.1                      4   4  Zero  ZI  SetParaBao.o
  00080018-00083b2f  g_Kalman                       15 128   8  Zero  ZI  main.o
  00083b30-00086307  g_Compen                       10 200   8  Zero  ZI  main.o
  00086308-00086e17  g_Navi                          2 832   8  Zero  ZI  main.o
  00086e18-00087597  g_CmdFullTempCompenData         1 920   8  Zero  ZI  main.o
  00087598-00087c9f  g_GyroANNCompen                 1 800   8  Zero  ZI  main.o
  00087ca0-000883a7  g_AccANNCompen                  1 800   8  Zero  ZI  main.o
  000883a8-00088977  g_ZUPT                          1 488   8  Zero  ZI  main.o
  00088978-00088cb7  g_InertialSysAlign                832   8  Zero  ZI  main.o
  00088cb8-00088e77  g_SysVar                          448   8  Zero  ZI  main.o
  00088e78-00088fd7  g_CmdNormalTempCompenData         352   8  Zero  ZI  main.o
  00088fd8-0008908f  g_Align                           184   8  Zero  ZI  main.o
  00089090-0008910f  g_SelfTest                        128   8  Zero  ZI  main.o
  00089110-0008915f  g_InitBind                         80   8  Zero  ZI  main.o
  00089160-00089177  r_LastGyro                         24   8  Zero  ZI  arithmetic.o
  00089178-0008918f  r_Gyro                             24   8  Zero  ZI  arithmetic.o
  00089190-000891a7  gyro_sum                           24   8  Zero  ZI  arithmetic.o
  000891a8-000891bf  estimated_gyro_bias                24   8  Zero  ZI  arithmetic.o
  000891c0-000891d7  estimated_acc_bias                 24   8  Zero  ZI  arithmetic.o
  000891d8-000891ef  acc_sum                            24   8  Zero  ZI  arithmetic.o
  000891f0-00089207  LastAcc                            24   8  Zero  ZI  arithmetic.o
  00089208-0008921f  Acc                                24   8  Zero  ZI  arithmetic.o
  00089220-0008a959  stSetPara                       5 946   4  Zero  ZI  SetParaBao.o
  0008a95a-0008a95b  tx_counter                          2   2  Zero  ZI  Uart_Irq.o
  0008a95c-0008ad5b  gframeParsebuf                  1 024   4  Zero  ZI  Uart_Irq.o
  0008ad5c-0008ae5b  s_xpi_nor_config                  256   4  Zero  ZI  flash.o
  0008ae5c-0008aeab  InavOutData                        80   4  Zero  ZI  arithmetic.o
  0008aeac-0008aecb  combineData                        32   4  Zero  ZI  arithmetic.o
  0008aecc-0008aee7  stSmi240Data                       28   4  Zero  ZI  protocol.o
  0008aee8-0008af03  ImuData                            28   4  Zero  ZI  main.o
  0008af04-0008af0f  control_config                     12   4  Zero  ZI  spi.o
  0008af10-0008af1b  control_Slave_config               12   4  Zero  ZI  spi.o
  0008af1c-0008af1f  timer_cb                            4   4  Zero  ZI  board.c.o
  0008af20-0008af23  hpm_core_clock                      4   4  Zero  ZI  hpm_clock_drv.c.o
  0008af24-0008af27  grxst                               4   4  Zero  ZI  Uart_Irq.o
  0008af28-0008af2b  grxlen                              4   4  Zero  ZI  Uart_Irq.o
  0008af2c-0008af2f  g_console_uart                      4   4  Zero  ZI  hpm_debug_console.c.o
  0008af30-0008af33  flag.2                              4   4  Zero  ZI  SetParaBao.o
  0008af34-0008af37  bias_sample_count                   4   4  Zero  ZI  arithmetic.o
  0008af38-0008af3b  bias_estimate_done                  4   4  Zero  ZI  arithmetic.o
  0008af3c-0008af3f  __SEGGER_RTL_stdout_file            4   4  Zero  ZI  hpm_debug_console.c.o
  0008af40-0008af43  TimeStamp                           4   4  Zero  ZI  main.o
  0008af44-0008af47  __SEGGER_RTL_locale_ptr             4   4  Zero  ZI  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0008af48-0008af4b  __SEGGER_RTL_heap_globals           4   4  Zero  ZI  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0008af4c-0008af4d  tCnt.0                              2   2  Zero  ZI  main.o
  0008af4e-0008af4e  uart_rx_dma_done                    1   1  Zero  ZI  uart_dma.o
  0008af4f-0008af4f  g_ucSystemResetFlag                 1   1  Zero  ZI  SetParaBao.o
  0008af50-0008af50  g_UpdateSuccessful                  1   1  Zero  ZI  SetParaBao.o
  0008af51-0008af51  g_StartUpdateFirm                   1   1  Zero  ZI  SetParaBao.o
  0008af52-0008af52  fpga_syn                            1   1  Zero  ZI  main.o
  0008af53-0008af53  uart_tx_dma_done                    1   1  Init  RW  uart_dma.o
  0008af54-0008af57  uiLastBaoInDex.3                    4   4  Init  RW  SetParaBao.o
  0008af58-0008af5b  stdout                              4   4  Init  RW  hpm_debug_console.c.o
  0008af5c-0008af5f  nbr_data_to_send                    4   4  Init  RW  Uart_Irq.o
  0008af60-0008af63  gbtxcompleted                       4   4  Init  RW  Uart_Irq.o
  0008af64-0008af64  g_UpdateBackFlag                    1   1  Init  RW  SetParaBao.o
  0008af65-0008af67  ( UNUSED .=.+3 )                    3   -  ----  -   -
  0008af68-0008cf67  grxbuffer                       8 192   4  None  ZI  Uart_Irq.o
  0008cf68-00090f67  [.bss.block.heap]              16 384   8  None  ZI  [ Linker created ]
  00090f68-0009bfff  ( UNUSED .=.+45208 )           45 208   -  ----  -   -
  0009c000-0009ffff  [.bss.block.stack]             16 384  16  None  ZI  [ Linker created ]
  8000d000-8000d00f  option                             16   4  Cnst  RO  board.c.o
  8000d010-8000dfff  ( UNUSED .=.+4080 )             4 080   -  ----  -   -
  8000e000-8000e00f  header                             16   4  Cnst  RO  hpm_bootheader.c.o
  8000e010-8000e08f  fw_info                           128   4  Cnst  RO  hpm_bootheader.c.o
  8000e090-8000ffff  ( UNUSED .=.+8048 )             8 048   -  ----  -   -
  80010000-80010065  _start                            102   2  Code  RX  startup.s.o
  80010066-80010067  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80010068-8001008f  [.rodata]                          40   8  Cnst  RO  align.o
  80010090-800100af  [.rodata]                          32   8  Cnst  RO  arithmetic.o
  800100b0-80010147  [.rodata]                         152   8  Cnst  RO  navi.o
  80010148-8001046f  [.rodata]                         808   8  Cnst  RO  ZUPT.o
  80010470-80010487  [.rodata]                          24   8  Cnst  RO  compen.o
  80010488-8001048f  [.rodata]                           8   8  Cnst  RO  matvecmath.o
  80010490-800104af  [.rodata]                          32   8  Cnst  RO  protocol.o
  800104b0-800104c7  [.rodata]                          24   8  Cnst  RO  hpm_pllctlv2_drv.c.o
  800104c8-80010527  __SEGGER_RTL_float64_ATan          96   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010528-8001055f  __SEGGER_RTL_float64_Tan           56   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010560-800105cf  __SEGGER_RTL_float64_ASinACos
                                                       112   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  800105d0-8001060f  __SEGGER_RTL_float64_SinCos
                                                        64   8  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010610-800106af  __SEGGER_RTL_ipow10               160   8  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800106b0-8001070f  [.srodata.merged.cst8]             96   8  Cnst  RO  [ Linker created ]
  80010710-8001085b  [.rodata.SetParaTemperCompen]
                                                       332   4  Cnst  RO  SetParaBao.o
  8001085c-80010887  [.rodata]                          44   4  Cnst  RO  Smi980.o
  80010888-80010c33  [.rodata]                         940   4  Cnst  RO  board.c.o
  80010c34-80010c47  [.rodata.uart_init]                20   4  Cnst  RO  hpm_uart_drv.c.o
  80010c48-80010c4b  [.rodata]                           4   4  Cnst  RO  hpm_uart_drv.c.o
  80010c4c-80010c8b  [.rodata.exception_handler]
                                                        64   4  Cnst  RO  trap.c.o
  80010c8c-80010c93  s_wdgs                              8   4  Cnst  RO  hpm_clock_drv.c.o
  80010c94-80010cc3  [.rodata.clock_get_frequency]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  80010cc4-80010ce3  [.rodata.get_frequency_for_source]
                                                        32   4  Cnst  RO  hpm_clock_drv.c.o
  80010ce4-80010d13  [.rodata.clock_set_source_divider]
                                                        48   4  Cnst  RO  hpm_clock_drv.c.o
  80010d14-80010e13  __SEGGER_RTL_fdiv_reciprocal_table
                                                       256   4  Cnst  RO  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80010e14-80010f93  __SEGGER_RTL_aSqrtData            384   4  Cnst  RO  floatops.o (libc_rv32imac_balanced.a)
  80010f94-80011393  __SEGGER_RTL_Moeller_inverse_lut
                                                     1 024   4  Cnst  RO  intops.o (libc_rv32imac_balanced.a)
  80011394-800113ab  __SEGGER_RTL_aPower2f              24   4  Cnst  RO  utilops.o (libc_rv32imac_balanced.a)
  800113ac-800113bb  __SEGGER_RTL_hex_lc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800113bc-800113cb  __SEGGER_RTL_hex_uc                16   4  Cnst  RO  prinops.o (libc_rv32imac_balanced.a)
  800113cc-800113fb  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long.str1.4]
                                                        48   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  800113fc-800114c3  [.rodata.libc.__SEGGER_RTL_vfprintf_short_float_long]
                                                       200   4  Cnst  RO  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  800114c4-800114cf  __SEGGER_RTL_c_locale              12   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800114d0-80011527  __SEGGER_RTL_c_locale_data
                                                        88   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011528-80011547  __SEGGER_RTL_codeset_ascii
                                                        32   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011548-800115c7  __SEGGER_RTL_ascii_ctype_map
                                                       128   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800115c8-800115e3  [.srodata.merged.cst4]             28   4  Cnst  RO  [ Linker created ]
  800115e4-800115fd  [.rodata]                          26   4  Cnst  RO  Uart_Irq.o
  800115fe-8001173f  ComputeCen                        322   2  Code  RX  align.o
  80011740-80011741  s_adc_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  80011742-80011815  InertialSysAlign_Init             212   2  Code  RX  align.o
  80011816-80011817  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80011818-80011819  s_dac_clk_mux_node                  2   4  Cnst  RO  hpm_clock_drv.c.o
  8001181a-80011a47  InertialSysAlignCompute           558   2  Code  RX  align.o
  80011a48-80011a51  [.rodata.libc.__SEGGER_RTL_find_locale.str1.4]
                                                        10   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011a52-80011b43  ComputeVi                         242   2  Code  RX  align.o
  80011b44-80011b45  __SEGGER_RTL_data_utf8_period
                                                         2   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011b46-80011bdf  ComputeVib0                       154   2  Code  RX  align.o
  80011be0-80011c19  __SEGGER_RTL_c_locale_day_names
                                                        58   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80011c1a-80012493  NavDataOutputSet                2 170   2  Code  RX  arithmetic.o
  80012494-800124a4  [.rodata]                          17   4  Cnst  RO  main.o
  800124a5-800124a5  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800124a6-8001268d  AlgorithmAct                      488   2  Code  RX  arithmetic.o
  8001268e-8001268f  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012690-80012690  __SEGGER_RTL_data_empty_string
                                                         1   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012691-80012691  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012692-800126d5  INS600mAlgorithmEntry              68   2  Code  RX  arithmetic.o
  800126d6-800126d7  ( UNUSED .=.+2 )                    2   -  ----  -   -
  800126d8-80012708  __SEGGER_RTL_c_locale_abbrev_month_names
                                                        49   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012709-80012709  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8001270a-80012793  SysVarDefaultSet                  138   2  Code  RX  navi.o
  80012794-800127b0  __SEGGER_RTL_c_locale_abbrev_day_names
                                                        29   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800127b1-800127b1  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800127b2-8001285d  Sys_Init                          172   2  Code  RX  navi.o
  8001285e-8001285f  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80012860-8001286c  __SEGGER_RTL_ascii_ctype_mask
                                                        13   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001286d-8001286d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  8001286e-80012997  Navi_Init                         298   2  Code  RX  navi.o
  80012998-800129a0  __SEGGER_RTL_c_locale_time_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800129a1-800129a1  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800129a2-80012a73  ComputeDelSenbb                   210   2  Code  RX  navi.o
  80012a74-80012a7c  __SEGGER_RTL_c_locale_date_format
                                                         9   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80012a7d-80012a7d  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012a7e-80012d93  ComputeQ                          790   2  Code  RX  navi.o
  80012d94-80012db2  [.rodata]                          31   4  Cnst  RO  flash.o
  80012db3-80012db3  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80012db4-800131ce  [.rodata]                       1 051   4  Cnst  RO  spi.o
  800131cf-800131cf  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800131d0-800131de  __SEGGER_RTL_c_locale_date_time_format
                                                        15   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800131df-800131df  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800131e0-800131e6  __SEGGER_RTL_c_locale_am_pm_indicator
                                                         7   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  800131e7-800131e7  ( ALIGN .=.+1 )                     1   -  ----  -   -
  800131e8-8001323e  __SEGGER_RTL_c_locale_month_names
                                                        87   4  Cnst  RO  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8001323f-8001323f  ( ALIGN .=.+1 )                     1   -  ----  -   -
  80013240-80013321  ComputeWnbb                       226   2  Code  RX  navi.o
  80013322-800137ab  QToCnb                          1 162   2  Code  RX  navi.o
  800137ac-80013a31  AttiToCnb                         646   2  Code  RX  navi.o
  80013a32-80013bef  ZUPTInit                          446   2  Code  RX  ZUPT.o
  80013bf0-800142b9  ZUPTDetection                   1 738   2  Code  RX  ZUPT.o
  800142ba-800142f3  rom_xpi_nor_erase_sector           58   2  Code  RX  flash.o
  800142f4-80014335  rom_xpi_nor_program                66   2  Code  RX  flash.o
  80014336-8001437b  norflash_init                      70   2  Code  RX  flash.o
  8001437c-800143b1  norflash_read                      54   2  Code  RX  flash.o
  800143b2-800143e5  norflash_write                     52   2  Code  RX  flash.o
  800143e6-80014411  norflash_erase_sector              44   2  Code  RX  flash.o
  80014412-8001446f  spi_transfer_mode_print            94   2  Code  RX  spi.o
  80014470-8001456f  SpiInitMaster                     256   2  Code  RX  spi.o
  80014570-8001463f  SpiInitSlave                      208   2  Code  RX  spi.o
  80014640-80014719  Timer_Init                        218   2  Code  RX  Timer.o
  8001471a-8001472b  uart_read_byte                     18   2  Code  RX  Uart_Irq.o
  8001472c-8001474d  uart_check_status                  34   2  Code  RX  Uart_Irq.o
  8001474e-80014797  UartIrqSendMsg                     74   2  Code  RX  Uart_Irq.o
  80014798-80014b05  analysisRxdata                    878   2  Code  RX  Uart_Irq.o
  80014b06-80016987  GyroANNCompen_X_Init            7 810   2  Code  RX  AnnTempCompen.o
  80016988-80018809  GyroANNCompen_Y_Init            7 810   2  Code  RX  AnnTempCompen.o
  8001880a-8001a68b  GyroANNCompen_Z_Init            7 810   2  Code  RX  AnnTempCompen.o
  8001a68c-8001c50d  AccANNCompen_X_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001c50e-8001e38f  AccANNCompen_Y_Init             7 810   2  Code  RX  AnnTempCompen.o
  8001e390-80020211  AccANNCompen_Z_Init             7 810   2  Code  RX  AnnTempCompen.o
  80020212-800204eb  ANN_Predict                       730   2  Code  RX  AnnTempCompen.o
  800204ec-80022685  Gyro_Compen_Para_Init           8 602   2  Code  RX  compen.o
  80022686-8002483f  Acc_Compen_Para_Init            8 634   2  Code  RX  compen.o
  80024840-800249eb  GyroCompenCompute                 428   2  Code  RX  compen.o
  800249ec-80024ba5  AccCompenCompute                  442   2  Code  RX  compen.o
  80024ba6-80024c63  GetTempRangeNum                   190   2  Code  RX  compen.o
  80024c64-80024fb1  ComputeAccTempDiff                846   2  Code  RX  compen.o
  80024fb2-8002513f  RTCompenPara                      398   2  Code  RX  compen.o
  80025140-80025211  LinerCompen_60_ANN_Order          210   2  Code  RX  compen.o
  80025212-80025223  ppor_sw_reset                      18   2  Code  RX  FirmwareUpdateFile.o
  80025224-80025237  Drv_SystemReset                    20   2  Code  RX  FirmwareUpdateFile.o
  80025238-8002553d  UserTask                          774   2  Code  RX  main.o
  8002553e-800255c5  main                              136   2  Code  RX  main.o
  800255c6-8002563f  MultiDim_Vec_Dot                  122   2  Code  RX  matvecmath.o
  80025640-80025695  Mat_Tr                             86   2  Code  RX  matvecmath.o
  80025696-80025a87  Mat_Inv                         1 010   2  Code  RX  matvecmath.o
  80025a88-80025d6d  Qua_Mul                           742   2  Code  RX  matvecmath.o
  80025d6e-80025da7  Check_8bit                         58   2  Code  RX  protocol.o
  80025da8-80025de1  Check_16bit                        58   2  Code  RX  protocol.o
  80025de2-80025e2b  xor_check                          74   2  Code  RX  protocol.o
  80025e2c-80025eff  Smi240UartSend                    212   2  Code  RX  protocol.o
  80025f00-800261ad  CombinationSpi2Send               686   2  Code  RX  protocol.o
  800261ae-80026435  CombinationUartSend22B            648   2  Code  RX  protocol.o
  80026436-800266fb  PureSpi2Send                      710   2  Code  RX  protocol.o
  800266fc-80026b7d  PureUartSend36B                 1 154   2  Code  RX  protocol.o
  80026b7e-80026c63  SetParaBaud                       230   2  Code  RX  SetParaBao.o
  80026c64-80026d3d  SetParaFrequency                  218   2  Code  RX  SetParaBao.o
  80026d3e-80026e4b  SetParaGnss                       270   2  Code  RX  SetParaBao.o
  80026e4c-80026f59  SetParaAngle                      270   2  Code  RX  SetParaBao.o
  80026f5a-80027067  SetParaVector                     270   2  Code  RX  SetParaBao.o
  80027068-80027175  SetParaDeviation                  270   2  Code  RX  SetParaBao.o
  80027176-800272eb  SetParaGnssInitValue              374   2  Code  RX  SetParaBao.o
  800272ec-800273c5  SetParaTime                       218   2  Code  RX  SetParaBao.o
  800273c6-800275cf  SaveParaToFlash                   522   2  Code  RX  SetParaBao.o
  800275d0-800277e9  RestoreFactory                    538   2  Code  RX  SetParaBao.o
  800277ea-80027b9f  SetParaAll                        950   2  Code  RX  SetParaBao.o
  80027ba0-80027cdd  ReadPara_0                        318   2  Code  RX  SetParaBao.o
  80027cde-80027dd3  ReadPara_2                        246   2  Code  RX  SetParaBao.o
  80027dd4-80027fa9  ReadPara_3                        470   2  Code  RX  SetParaBao.o
  80027faa-80028097  SetParaCalibration                238   2  Code  RX  SetParaBao.o
  80028098-80028169  SetParaKalmanQ                    210   2  Code  RX  SetParaBao.o
  8002816a-8002823b  SetParaKalmanR                    210   2  Code  RX  SetParaBao.o
  8002823c-8002830d  SetParaFilter                     210   2  Code  RX  SetParaBao.o
  8002830e-8002844f  SetParaUpdateStart                322   2  Code  RX  SetParaBao.o
  80028450-80028579  SetParaUpdateSend                 298   2  Code  RX  SetParaBao.o
  8002857a-80028673  SetParaUpdateEnd                  250   2  Code  RX  SetParaBao.o
  80028674-80028759  TemperCompenGyroNormal            230   2  Code  RX  SetParaBao.o
  8002875a-8002883f  TemperCompenAccNormal             230   2  Code  RX  SetParaBao.o
  80028840-80028925  TemperCompenGyroAll_0             230   2  Code  RX  SetParaBao.o
  80028926-80028a0b  TemperCompenAccAll_0              230   2  Code  RX  SetParaBao.o
  80028a0c-80028af1  TemperCompenGyroAll_1             230   2  Code  RX  SetParaBao.o
  80028af2-80028bd7  TemperCompenAccAll_1              230   2  Code  RX  SetParaBao.o
  80028bd8-80028cbd  TemperCompenGyroAll_2             230   2  Code  RX  SetParaBao.o
  80028cbe-80028da3  TemperCompenAccAll_2              230   2  Code  RX  SetParaBao.o
  80028da4-80028e8d  TemperCompenGyroAll_3             234   2  Code  RX  SetParaBao.o
  80028e8e-80028f77  TemperCompenAccAll_3              234   2  Code  RX  SetParaBao.o
  80028f78-80029061  TemperCompenGyroNerve_X0          234   2  Code  RX  SetParaBao.o
  80029062-8002914b  TemperCompenGyroNerve_Y0          234   2  Code  RX  SetParaBao.o
  8002914c-80029235  TemperCompenGyroNerve_Z0          234   2  Code  RX  SetParaBao.o
  80029236-8002931f  TemperCompenAccNerve_X0           234   2  Code  RX  SetParaBao.o
  80029320-80029409  TemperCompenAccNerve_Y0           234   2  Code  RX  SetParaBao.o
  8002940a-800294f3  TemperCompenAccNerve_Z0           234   2  Code  RX  SetParaBao.o
  800294f4-800295dd  TemperCompenGyroNerve_X1          234   2  Code  RX  SetParaBao.o
  800295de-800296c7  TemperCompenGyroNerve_Y1          234   2  Code  RX  SetParaBao.o
  800296c8-800297b1  TemperCompenGyroNerve_Z1          234   2  Code  RX  SetParaBao.o
  800297b2-8002989b  TemperCompenAccNerve_Y1           234   2  Code  RX  SetParaBao.o
  8002989c-80029985  TemperCompenAccNerve_Z1           234   2  Code  RX  SetParaBao.o
  80029986-80029a6f  TemperCompenGyroNerve_X2          234   2  Code  RX  SetParaBao.o
  80029a70-80029b59  TemperCompenGyroNerve_Y2          234   2  Code  RX  SetParaBao.o
  80029b5a-80029c43  TemperCompenGyroNerve_Z2          234   2  Code  RX  SetParaBao.o
  80029c44-80029d2d  TemperCompenAccNerve_X2           234   2  Code  RX  SetParaBao.o
  80029d2e-80029e17  TemperCompenAccNerve_Y2           234   2  Code  RX  SetParaBao.o
  80029e18-80029f01  TemperCompenAccNerve_Z2           234   2  Code  RX  SetParaBao.o
  80029f02-8002a273  UartDmaRecSetPara                 882   2  Code  RX  SetParaBao.o
  8002a274-8002a29d  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  board.c.o
  8002a29e-8002a2c3  sysctl_resource_target_get_mode
                                                        38   2  Code  RX  board.c.o
  8002a2c4-8002a2ed  sysctl_clock_set_preset            42   2  Code  RX  board.c.o
  8002a2ee-8002a313  pllctlv2_xtal_set_rampup_time
                                                        38   2  Code  RX  board.c.o
  8002a314-8002a355  board_print_banner                 66   2  Code  RX  board.c.o
  8002a356-8002a3df  board_print_clock_freq            138   2  Code  RX  board.c.o
  8002a3e0-8002a4b9  board_init_usb_dp_dm_pins         218   2  Code  RX  board.c.o
  8002a4ba-8002a4d1  board_init_uart                    24   2  Code  RX  board.c.o
  8002a4d2-8002a55b  init_uart_pins                    138   2  Code  RX  pinmux.c.o
  8002a55c-8002a5cd  gptmr_channel_get_default_config
                                                       114   2  Code  RX  hpm_gptmr_drv.c.o
  8002a5ce-8002a6ff  gptmr_channel_config              306   2  Code  RX  hpm_gptmr_drv.c.o
  8002a700-8002a771  pllctlv2_set_postdiv              114   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002a772-8002a82b  pllctlv2_get_pll_postdiv_freq_in_hz
                                                       186   2  Code  RX  hpm_pllctlv2_drv.c.o
  8002a82c-8002a849  spi_get_data_length_in_bytes
                                                        30   2  Code  RX  hpm_spi_drv.c.o
  8002a84a-8002a883  spi_get_rx_fifo_valid_data_size
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002a884-8002a8c5  spi_write_command                  66   2  Code  RX  hpm_spi_drv.c.o
  8002a8c6-8002a90b  spi_read_command                   70   2  Code  RX  hpm_spi_drv.c.o
  8002a90c-8002a9d1  spi_write_data                    198   2  Code  RX  hpm_spi_drv.c.o
  8002a9d2-8002aabd  spi_read_data                     236   2  Code  RX  hpm_spi_drv.c.o
  8002aabe-8002ac0f  spi_write_read_data               338   2  Code  RX  hpm_spi_drv.c.o
  8002ac10-8002ac4d  spi_no_data                        62   2  Code  RX  hpm_spi_drv.c.o
  8002ac4e-8002ac67  spi_master_get_default_timing_config
                                                        26   2  Code  RX  hpm_spi_drv.c.o
  8002ac68-8002acb9  spi_master_get_default_control_config
                                                        82   2  Code  RX  hpm_spi_drv.c.o
  8002acba-8002acf3  spi_slave_get_default_control_config
                                                        58   2  Code  RX  hpm_spi_drv.c.o
  8002acf4-8002ad91  spi_master_timing_init            158   2  Code  RX  hpm_spi_drv.c.o
  8002ad92-8002af53  uart_calculate_baudrate           450   2  Code  RX  hpm_uart_drv.c.o
  8002af54-8002b107  uart_init                         436   2  Code  RX  hpm_uart_drv.c.o
  8002b108-8002b155  uart_send_byte                     78   2  Code  RX  hpm_uart_drv.c.o
  8002b156-8002b1ff  _clean_up                         170   2  Code  RX  reset.c.o
  8002b200-8002b211  syscall_handler                    18   2  Code  RX  trap.c.o
  8002b212-8002b28b  hpm_csr_get_core_cycle            122   2  Code  RX  hpm_clock_drv.c.o
  8002b28c-8002b339  get_frequency_for_source          174   2  Code  RX  hpm_clock_drv.c.o
  8002b33a-8002b3a5  get_frequency_for_ip_in_common_group
                                                       108   2  Code  RX  hpm_clock_drv.c.o
  8002b3a6-8002b43d  get_frequency_for_adc             152   2  Code  RX  hpm_clock_drv.c.o
  8002b43e-8002b46f  get_frequency_for_ewdg             50   2  Code  RX  hpm_clock_drv.c.o
  8002b470-8002b4b3  get_frequency_for_cpu              68   2  Code  RX  hpm_clock_drv.c.o
  8002b4b4-8002b5ad  clock_set_source_divider          250   2  Code  RX  hpm_clock_drv.c.o
  8002b5ae-8002b5e7  clock_add_to_group                 58   2  Code  RX  hpm_clock_drv.c.o
  8002b5e8-8002b621  clock_remove_from_group            58   2  Code  RX  hpm_clock_drv.c.o
  8002b622-8002b657  l1c_dc_enable                      54   2  Code  RX  hpm_l1c_drv.c.o
  8002b658-8002b685  l1c_ic_enable                      46   2  Code  RX  hpm_l1c_drv.c.o
  8002b686-8002b6af  sysctl_resource_target_is_busy
                                                        42   2  Code  RX  hpm_sysctl_drv.c.o
  8002b6b0-8002b6d1  sysctl_cpu_clock_any_is_busy
                                                        34   2  Code  RX  hpm_sysctl_drv.c.o
  8002b6d2-8002b6ff  sysctl_clock_target_is_busy
                                                        46   2  Code  RX  hpm_sysctl_drv.c.o
  8002b700-8002b787  sysctl_config_clock               136   2  Code  RX  hpm_sysctl_drv.c.o
  8002b788-8002b7dd  system_init                        86   2  Code  RX  system.c.o
  8002b7de-8002b823  fwrite                             70   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002b824-8002b847  fputc                              36   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  8002b848-8002b84f  __subsf3                            8   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002b850-8002b859  __subdf3                           10   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002b85a-8002ba07  __addsf3                          430   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002ba08-8002ba41  __ltsf2                            58   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002ba42-8002ba87  __ltdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002ba88-8002babd  __lesf2                            54   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002babe-8002bb03  __ledf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bb04-8002bb35  __gtsf2                            50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bb36-8002bb7b  __gtdf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bb7c-8002bbb9  __gesf2                            62   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bbba-8002bbff  __gedf2                            70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bc00-8002bc49  __fixsfsi                          74   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bc4a-8002bc7b  __fixunssfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bc7c-8002bcad  __fixunsdfsi                       50   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bcae-8002bd13  __floatsisf                       102   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bd14-8002bd61  __floatsidf                        78   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bd62-8002bdb7  __floatunsisf                      86   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bdb8-8002be61  __floatundisf                     170   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002be62-8002bea7  __extendsfdf2                      70   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bea8-8002bf2d  __truncdfsf2                      134   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8002bf2e-8002bfa7  __SEGGER_RTL_ldouble_to_double
                                                       122   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002bfa8-8002c005  __SEGGER_RTL_float64_PolyEvalP
                                                        94   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c006-8002c10f  __SEGGER_RTL_float64_sin_inline
                                                       266   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c110-8002c21d  __SEGGER_RTL_float64_cos_inline
                                                       270   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c21e-8002c22f  __SEGGER_RTL_float32_isnan
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c230-8002c23d  __SEGGER_RTL_float32_isinf
                                                        14   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c23e-8002c24f  __SEGGER_RTL_float32_isnormal
                                                        18   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c250-8002c2a5  ldexp.localalias                   86   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c2a6-8002c30f  floorf                            106   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c310-8002c4b7  atan                              424   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c4b8-8002c799  sqrt                              738   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c79a-8002c79f  asin                                6   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8002c7a0-8002c7c5  __ashldi3                          38   2  Code  RX  intasmops_rv.o (libc_rv32imac_balanced.a)
  8002c7c6-8002cbdd  __udivdi3                       1 048   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002cbde-8002d011  __umoddi3                       1 076   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d012-8002d01b  abs                                10   2  Code  RX  intops.o (libc_rv32imac_balanced.a)
  8002d01c-8002d0a1  memcpy                            134   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  8002d0a2-8002d0fb  __SEGGER_RTL_pow10f                90   2  Code  RX  utilops.o (libc_rv32imac_balanced.a)
  8002d0fc-8002d11d  __SEGGER_RTL_prin_flush            34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d11e-8002d137  __SEGGER_RTL_pre_padding           26   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d138-8002d159  __SEGGER_RTL_init_prin_l           34   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d15a-8002d17f  vfprintf                           38   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d180-8002d1a7  printf                             40   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  8002d1a8-8002d1bb  __SEGGER_init_heap                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  8002d1bc-8002d1d1  __SEGGER_RTL_init_heap             22   2  Code  RX  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  8002d1d2-8002d1df  __SEGGER_RTL_ascii_toupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002d1e0-8002d1ed  __SEGGER_RTL_ascii_towupper
                                                        14   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002d1ee-8002d217  __SEGGER_RTL_ascii_mbtowc          42   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  8002d218-8002d34b  ComputeCie                        308   2  Code  RX  align.o
  8002d34c-8002d483  ComputeCib0i                      312   2  Code  RX  align.o
  8002d484-8002d54d  FinishInertialSysAlign            202   2  Code  RX  align.o
  8002d54e-8002d701  ApplyBiasCorrectionToCombineData
                                                       436   2  Code  RX  arithmetic.o
  8002d702-8002d879  IMUdataPredo                      376   2  Code  RX  arithmetic.o
  8002d87a-8002d8cd  IMUdataPredp_algParmCache          84   2  Code  RX  arithmetic.o
  8002d8ce-8002db51  InitialBiasEstimate               644   2  Code  RX  arithmetic.o
  8002db52-8002db61  AlgorithmDo                        16   2  Code  RX  arithmetic.o
  8002db62-8002db91  Bind_Init                          48   2  Code  RX  navi.o
  8002db92-8002dd31  NaviCompute                       416   2  Code  RX  navi.o
  8002dd32-8002de91  ComputeG                          352   2  Code  RX  navi.o
  8002de92-8002df89  ComputeRmRn                       248   2  Code  RX  navi.o
  8002df8a-8002dffd  ComputeWien                       116   2  Code  RX  navi.o
  8002dffe-8002e0ad  ComputeWenn                       176   2  Code  RX  navi.o
  8002e0ae-8002e319  CnbToAtti                         620   2  Code  RX  navi.o
  8002e31a-8002e709  CnbToQ                          1 008   2  Code  RX  navi.o
  8002e70a-8002e715  GetZUPTFlag                        12   2  Code  RX  ZUPT.o
  8002e716-8002e76f  ZUPTAngleConstraint                90   2  Code  RX  ZUPT.o
  8002e770-8002e7ab  rom_xpi_nor_read                   60   2  Code  RX  flash.o
  8002e7ac-8002e7d3  rom_xpi_nor_auto_config            40   2  Code  RX  flash.o
  8002e7d4-8002e813  SpiSlaveSend                       64   2  Code  RX  spi.o
  8002e814-8002e8bb  Smi980SpiTransfer                 168   2  Code  RX  spi.o
  8002e8bc-8002e8d7  gptmr_enable_irq                   28   2  Code  RX  Timer.o
  8002e8d8-8002e8fb  gptmr_check_status                 36   2  Code  RX  Timer.o
  8002e8fc-8002e90f  gptmr_clear_status                 20   2  Code  RX  Timer.o
  8002e910-8002e93b  gptmr_start_counter                44   2  Code  RX  Timer.o
  8002e93c-8002e9ff  dma_check_transfer_status         196   2  Code  RX  uart_dma.o
  8002ea00-8002ea17  uart_write_byte                    24   2  Code  RX  Uart_Irq.o
  8002ea18-8002ea33  uart_disable_irq                   28   2  Code  RX  Uart_Irq.o
  8002ea34-8002ea4b  uart_enable_irq                    24   2  Code  RX  Uart_Irq.o
  8002ea4c-8002ea63  uart_get_irq_id                    24   2  Code  RX  Uart_Irq.o
  8002ea64-8002eb6d  UartIrqInit                       266   2  Code  RX  Uart_Irq.o
  8002eb6e-8002ebc1  ANNCompen_Init                     84   2  Code  RX  AnnTempCompen.o
  8002ebc2-8002ef09  ComputeGyroTempDiff               840   2  Code  RX  compen.o
  8002ef0a-8002ef41  Drv_FlashErase                     56   2  Code  RX  FirmwareUpdateFile.o
  8002ef42-8002ef81  Drv_FlashWrite                     64   2  Code  RX  FirmwareUpdateFile.o
  8002ef82-8002ef9d  Drv_FlashRead                      28   2  Code  RX  FirmwareUpdateFile.o
  8002ef9e-8002f073  TaskMange                         214   2  Code  RX  main.o
  8002f074-8002f0c9  Spi2Task                           86   2  Code  RX  main.o
  8002f0ca-8002f1e1  Vec_Cross                         280   2  Code  RX  matvecmath.o
  8002f1e2-8002f2bd  Mat_Mul                           220   2  Code  RX  matvecmath.o
  8002f2be-8002f309  Relu                               76   2  Code  RX  matvecmath.o
  8002f30a-8002f375  GetSmi240Data                     108   2  Code  RX  protocol.o
  8002f376-8002f67d  Smi240DataToAlgorithm             776   2  Code  RX  protocol.o
  8002f67e-8002f749  Smi240Spi2Send                    204   2  Code  RX  protocol.o
  8002f74a-8002fa05  CombinationUartSend               700   2  Code  RX  protocol.o
  8002fa06-8002ff01  PureUartSend                    1 276   2  Code  RX  protocol.o
  8002ff02-8002ff39  crc_verify_8bit                    56   2  Code  RX  SetParaBao.o
  8002ff3a-8002ffc5  SendPara_SetHead                  140   2  Code  RX  SetParaBao.o
  8002ffc6-80030027  SendPara_SetEnd                    98   2  Code  RX  SetParaBao.o
  80030028-800300b3  UpdateStart_SetHead               140   2  Code  RX  SetParaBao.o
  800300b4-80030115  UpdateStart_SetEnd                 98   2  Code  RX  SetParaBao.o
  80030116-800301a1  UpdateSend_SetHead                140   2  Code  RX  SetParaBao.o
  800301a2-80030203  UpdateSend_SetEnd                  98   2  Code  RX  SetParaBao.o
  80030204-8003028f  UpdateEnd_SetHead                 140   2  Code  RX  SetParaBao.o
  80030290-800302f1  UpdateEnd_SetEnd                   98   2  Code  RX  SetParaBao.o
  800302f2-8003037d  UpdateStop_SetHead                140   2  Code  RX  SetParaBao.o
  8003037e-800303df  UpdateStop_SetEnd                  98   2  Code  RX  SetParaBao.o
  800303e0-8003046b  ReadPara0_SetHead                 140   2  Code  RX  SetParaBao.o
  8003046c-800304cd  ReadPara0_SetEnd                   98   2  Code  RX  SetParaBao.o
  800304ce-80030559  ReadPara1_SetHead                 140   2  Code  RX  SetParaBao.o
  8003055a-800305bb  ReadPara1_SetEnd                   98   2  Code  RX  SetParaBao.o
  800305bc-80030647  ReadPara2_SetHead                 140   2  Code  RX  SetParaBao.o
  80030648-800306a9  ReadPara2_SetEnd                   98   2  Code  RX  SetParaBao.o
  800306aa-80030735  ReadPara3_SetHead                 140   2  Code  RX  SetParaBao.o
  80030736-80030799  ReadPara3_SetEnd                  100   2  Code  RX  SetParaBao.o
  8003079a-80030825  ReadPara4_SetHead                 140   2  Code  RX  SetParaBao.o
  80030826-80030889  ReadPara4_SetEnd                  100   2  Code  RX  SetParaBao.o
  8003088a-80030961  SetParaCoord                      216   2  Code  RX  SetParaBao.o
  80030962-80030a89  ReadParaFromFlash                 296   2  Code  RX  SetParaBao.o
  80030a8a-80030b81  ReadPara_1                        248   2  Code  RX  SetParaBao.o
  80030b82-80030cb5  ReadPara_4                        308   2  Code  RX  SetParaBao.o
  80030cb6-80030da1  ReadPara                          236   2  Code  RX  SetParaBao.o
  80030da2-80030e6d  SetParaGpsType                    204   2  Code  RX  SetParaBao.o
  80030e6e-80030f39  SetParaDataOutType                204   2  Code  RX  SetParaBao.o
  80030f3a-80031005  SetParaDebugMode                  204   2  Code  RX  SetParaBao.o
  80031006-800310d1  SetParaGyroType                   204   2  Code  RX  SetParaBao.o
  800310d2-8003122d  SetParaFactorGyro                 348   2  Code  RX  SetParaBao.o
  8003122e-80031389  SetParaFactorAcc                  348   2  Code  RX  SetParaBao.o
  8003138a-80031505  ParaUpdateHandle                  380   2  Code  RX  SetParaBao.o
  80031506-800315cd  SetParaUpdateStop                 200   2  Code  RX  SetParaBao.o
  800315ce-800316b3  TemperCompenAccNerve_X1           230   2  Code  RX  SetParaBao.o
  800316b4-800317d5  SetParaTemperCompen               290   2  Code  RX  SetParaBao.o
  800317d6-80031845  Smi980_Init                       112   2  Code  RX  Smi980.o
  80031846-800319c3  Smi980_ReadData                   382   2  Code  RX  Smi980.o
  800319c4-800319df  sysctl_resource_any_is_busy
                                                        28   2  Code  RX  board.c.o
  800319e0-80031a1f  sysctl_resource_target_set_mode
                                                        64   2  Code  RX  board.c.o
  80031a20-80031a43  gptmr_check_status                 36   2  Code  RX  board.c.o
  80031a44-80031a57  gptmr_clear_status                 20   2  Code  RX  board.c.o
  80031a58-80031a77  usb_phy_disable_dp_dm_pulldown
                                                        32   2  Code  RX  board.c.o
  80031a78-80031a93  pllctlv2_xtal_is_stable            28   2  Code  RX  board.c.o
  80031a94-80031aaf  pllctlv2_xtal_is_enabled           28   2  Code  RX  board.c.o
  80031ab0-80031b0d  board_init_console                 94   2  Code  RX  board.c.o
  80031b0e-80031b2d  board_init                         32   2  Code  RX  board.c.o
  80031b2e-80031e89  board_init_clock                  860   2  Code  RX  board.c.o
  80031e8a-80031e9d  board_delay_ms                     20   2  Code  RX  board.c.o
  80031e9e-80031ed5  board_init_spi_clock               56   2  Code  RX  board.c.o
  80031ed6-80031ee7  board_init_spi_pins                18   2  Code  RX  board.c.o
  80031ee8-80031eeb  board_init_pmp                      4   2  Code  RX  board.c.o
  80031eec-80031fdb  board_init_uart_clock             240   2  Code  RX  board.c.o
  80031fdc-80032027  init_py_pins_as_pgpio              76   2  Code  RX  pinmux.c.o
  80032028-8003209b  init_spi_pins                     116   2  Code  RX  pinmux.c.o
  8003209c-80032101  console_init                      102   2  Code  RX  hpm_debug_console.c.o
  80032102-8003217f  __SEGGER_RTL_X_file_write         126   2  Code  RX  hpm_debug_console.c.o
  80032180-8003218b  __SEGGER_RTL_X_file_stat           12   2  Code  RX  hpm_debug_console.c.o
  8003218c-80032197  __SEGGER_RTL_X_file_bufsize
                                                        12   2  Code  RX  hpm_debug_console.c.o
  80032198-800321df  pcfg_dcdc_set_voltage              72   2  Code  RX  hpm_pcfg_drv.c.o
  800321e0-80032297  pllctlv2_init_pll_with_freq
                                                       184   2  Code  RX  hpm_pllctlv2_drv.c.o
  80032298-8003236f  pllctlv2_get_pll_freq_in_hz
                                                       216   2  Code  RX  hpm_pllctlv2_drv.c.o
  80032370-8003238f  spi_get_data_length_in_bits
                                                        32   2  Code  RX  hpm_spi_drv.c.o
  80032390-800323cf  spi_wait_for_idle_status           64   2  Code  RX  hpm_spi_drv.c.o
  800323d0-80032403  spi_write_address                  52   2  Code  RX  hpm_spi_drv.c.o
  80032404-80032447  spi_master_get_default_format_config
                                                        68   2  Code  RX  hpm_spi_drv.c.o
  80032448-80032483  spi_slave_get_default_format_config
                                                        60   2  Code  RX  hpm_spi_drv.c.o
  80032484-80032503  spi_format_init                   128   2  Code  RX  hpm_spi_drv.c.o
  80032504-8003261f  spi_control_init                  284   2  Code  RX  hpm_spi_drv.c.o
  80032620-800327b5  spi_transfer                      406   2  Code  RX  hpm_spi_drv.c.o
  800327b6-800327f1  uart_modem_config                  60   2  Code  RX  hpm_uart_drv.c.o
  800327f2-8003280d  uart_disable_irq                   28   2  Code  RX  hpm_uart_drv.c.o
  8003280e-80032825  uart_enable_irq                    24   2  Code  RX  hpm_uart_drv.c.o
  80032826-800328b9  uart_default_config               148   2  Code  RX  hpm_uart_drv.c.o
  800328ba-800328f9  uart_flush                         64   2  Code  RX  hpm_uart_drv.c.o
  800328fa-80032955  uart_init_rxline_idle_detection
                                                        92   2  Code  RX  hpm_uart_drv.c.o
  80032956-8003296d  reset_handler                      24   2  Code  RX  reset.c.o
  8003296e-80032971  _init                               4   2  Code  RX  reset.c.o
  80032972-80032975  mchtmr_isr                          4   2  Code  RX  trap.c.o
  80032976-80032979  swi_isr                             4   2  Code  RX  trap.c.o
  8003297a-800329a1  exception_handler                  40   2  Code  RX  trap.c.o
  800329a2-80032a3f  clock_get_frequency               158   2  Code  RX  hpm_clock_drv.c.o
  80032a40-80032ad1  get_frequency_for_dac             146   2  Code  RX  hpm_clock_drv.c.o
  80032ad2-80032af9  get_frequency_for_pewdg            40   2  Code  RX  hpm_clock_drv.c.o
  80032afa-80032b25  get_frequency_for_ahb              44   2  Code  RX  hpm_clock_drv.c.o
  80032b26-80032b53  clock_check_in_group               46   2  Code  RX  hpm_clock_drv.c.o
  80032b54-80032b7b  clock_connect_group_to_cpu
                                                        40   2  Code  RX  hpm_clock_drv.c.o
  80032b7c-80032c53  clock_cpu_delay_ms                216   2  Code  RX  hpm_clock_drv.c.o
  80032c54-80032c71  clock_update_core_clock            30   2  Code  RX  hpm_clock_drv.c.o
  80032c72-80032c89  l1c_dc_invalidate_all              24   2  Code  RX  hpm_l1c_drv.c.o
  80032c8a-80032d4d  sysctl_enable_group_resource
                                                       196   2  Code  RX  hpm_sysctl_drv.c.o
  80032d4e-80032dc1  sysctl_check_group_resource_enable
                                                       116   2  Code  RX  hpm_sysctl_drv.c.o
  80032dc2-80032e75  sysctl_config_cpu0_domain_clock
                                                       180   2  Code  RX  hpm_sysctl_drv.c.o
  80032e76-80032ea1  enable_plic_feature                44   2  Code  RX  system.c.o
  80032ea2-80032ead  putchar                            12   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80032eae-80032ee3  puts                               54   2  Code  RX  fileops.o (libc_rv32imac_balanced.a)
  80032ee4-800331bb  __adddf3                          728   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800331bc-8003326b  __mulsf3                          176   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003326c-8003337b  __muldf3                          272   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003337c-8003347b  __divsf3                          256   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003347c-8003363b  __divdf3                          448   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  8003363c-80033667  __eqsf2                            44   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033668-800336b7  __fixdfsi                          80   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  800336b8-80033717  __fixunssfdi                       96   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033718-8003375f  __floatunsidf                      72   2  Code  RX  floatasmops_rv.o (libc_rv32imac_balanced.a)
  80033760-80033783  __SEGGER_RTL_SquareHi_U64          36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033784-800337dd  __SEGGER_RTL_float64_PolyEvalQ
                                                        90   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  800337de-80033801  __trunctfsf2                       36   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033802-80033805  __SEGGER_RTL_float32_signbit
                                                         4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033806-80033849  ldexpf.localalias                  68   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003384a-80033875  frexpf                             44   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033876-80033979  fmodf                             260   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003397a-8003397d  sin                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  8003397e-80033981  cos                                 4   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033982-80033afd  tan                               380   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033afe-80033cb5  __SEGGER_RTL_float64_asinacos_fpu
                                                       440   2  Code  RX  floatops.o (libc_rv32imac_balanced.a)
  80033cb6-80033d1d  memset                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  80033d1e-80033d85  strlen                            104   2  Code  RX  strasmops_rv.o (libc_rv32imac_balanced.a)
  80033d86-80033e1d  strnlen                           152   2  Code  RX  strops.o (libc_rv32imac_balanced.a)
  80033e1e-80033e29  __SEGGER_RTL_stream_write          12   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80033e2a-80033ec5  __SEGGER_RTL_putc                 156   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80033ec6-80033eef  __SEGGER_RTL_print_padding
                                                        42   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80033ef0-80033f65  vfprintf_l                        118   2  Code  RX  prinops.o (libc_rv32imac_balanced.a)
  80033f66-80034bf9  __SEGGER_RTL_vfprintf_short_float_long
                                                     3 220   2  Code  RX  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  80034bfa-80034c25  __SEGGER_RTL_ascii_isctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c26-80034c35  __SEGGER_RTL_ascii_tolower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c36-80034c61  __SEGGER_RTL_ascii_iswctype
                                                        44   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c62-80034c71  __SEGGER_RTL_ascii_towlower
                                                        16   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c72-80034c85  __SEGGER_RTL_ascii_wctomb          20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c86-80034c99  __SEGGER_RTL_current_locale
                                                        20   2  Code  RX  mbops.o (mbops_timeops_rv32imac_balanced.a)
  80034c9a-80034c9b  ( UNUSED .=.+2 )                    2   -  ----  -   -
  80034c9c-80034cef  __SEGGER_init_table__              84   4  Cnst  RO  [ Linker created ]
  80034cf0-800353b7  __SEGGER_init_data__            1 736   4  Cnst  RO  [ Linker created ]
  800353b8-800353cb  __SEGGER_init_zero                 20   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  800353cc-800353e7  __SEGGER_init_copy                 28   2  Code  RX  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)


***********************************************************************************************
***                                                                                         ***
***                                       SYMBOL LIST                                       ***
***                                                                                         ***
***********************************************************************************************

RAM function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  board_timer_isr            0x000004A2          54      2  Init  Gb  board.c.o
  default_isr_19             0x0000041A         136      2  Init  Gb  Uart_Irq.o
  default_isr_34             0x00000232         138      2  Init  Gb  uart_dma.o
  default_isr_5              0x0000015A         136      2  Init  Gb  Timer.o
  default_isr_8              0x000004D8         136      2  Init  Gb  board.c.o
  dma_isr                    0x000001E2          80      2  Init  Gb  uart_dma.o
  irq_handler_trap           0x00000568         312      4  Init  Gb  trap.c.o
  tick_ms_isr                0x00000124          54      2  Init  Gb  Timer.o
  uart_isr                   0x000002BC         350      2  Init  Gb  Uart_Irq.o

RAM function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x00000124  tick_ms_isr                        54      2  Init  Gb  Timer.o
  0x0000015A  default_isr_5                     136      2  Init  Gb  Timer.o
  0x000001E2  dma_isr                            80      2  Init  Gb  uart_dma.o
  0x00000232  default_isr_34                    138      2  Init  Gb  uart_dma.o
  0x000002BC  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  0x0000041A  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  0x000004A2  board_timer_isr                    54      2  Init  Gb  board.c.o
  0x000004D8  default_isr_8                     136      2  Init  Gb  board.c.o
  0x00000568  irq_handler_trap                  312      4  Init  Gb  trap.c.o

RAM function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  uart_isr                          350      2  Init  Gb  Uart_Irq.o
  irq_handler_trap                  312      4  Init  Gb  trap.c.o
  default_isr_34                    138      2  Init  Gb  uart_dma.o
  default_isr_19                    136      2  Init  Gb  Uart_Irq.o
  default_isr_5                     136      2  Init  Gb  Timer.o
  default_isr_8                     136      2  Init  Gb  board.c.o
  dma_isr                            80      2  Init  Gb  uart_dma.o
  board_timer_isr                    54      2  Init  Gb  board.c.o
  tick_ms_isr                        54      2  Init  Gb  Timer.o

Function symbols by name:

  Symbol name                   Address        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ----------  -----  ----  --  -----------
  ANNCompen_Init             0x8002EB6E         108      2  Code  Gb  AnnTempCompen.o
  ANN_Predict                0x80020212         818      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_X_Init        0x8001A68C       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init        0x8001C50E       8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init        0x8001E390       8 134      2  Code  Gb  AnnTempCompen.o
  AccCompenCompute           0x800249EC         490      2  Code  Gb  compen.o
  Acc_Compen_Para_Init       0x80022686       9 246      2  Code  Gb  compen.o
  AlgorithmAct               0x800124A6         634      2  Code  Gb  arithmetic.o
  AlgorithmDo                0x8002DB52          20      2  Code  Gb  arithmetic.o
  ApplyBiasCorrectionToCombineData
                             0x8002D54E         472      2  Code  Gb  arithmetic.o
  AttiToCnb                  0x800137AC         750      2  Code  Gb  navi.o
  Bind_Init                  0x8002DB62          56      2  Code  Gb  navi.o
  Check_16bit                0x80025DA8          58      2  Code  Gb  protocol.o
  Check_8bit                 0x80025D6E          58      2  Code  Gb  protocol.o
  CnbToAtti                  0x8002E0AE         728      2  Code  Gb  navi.o
  CnbToQ                     0x8002E31A       1 224      2  Code  Gb  navi.o
  CombinationSpi2Send        0x80025F00         806      2  Code  Gb  protocol.o
  CombinationUartSend        0x8002F74A         816      2  Code  Gb  protocol.o
  CombinationUartSend22B     0x800261AE         758      2  Code  Gb  protocol.o
  ComputeAccTempDiff         0x80024C64         886      2  Code  Gb  compen.o
  ComputeCen                 0x800115FE         354      2  Code  Gb  align.o
  ComputeCib0i               0x8002D34C         336      2  Code  Gb  align.o
  ComputeCie                 0x8002D218         340      2  Code  Gb  align.o
  ComputeDelSenbb            0x800129A2         230      2  Code  Gb  navi.o
  ComputeG                   0x8002DD32         444      2  Code  Gb  navi.o
  ComputeGyroTempDiff        0x8002EBC2         880      2  Code  Gb  compen.o
  ComputeQ                   0x80012A7E         926      2  Code  Gb  navi.o
  ComputeRmRn                0x8002DE92         312      2  Code  Gb  navi.o
  ComputeVi                  0x80011A52         266      2  Code  Gb  align.o
  ComputeVib0                0x80011B46         166      2  Code  Gb  align.o
  ComputeWenn                0x8002DFFE         192      2  Code  Gb  navi.o
  ComputeWien                0x8002DF8A         140      2  Code  Gb  navi.o
  ComputeWnbb                0x80013240         238      2  Code  Gb  navi.o
  Drv_FlashErase             0x8002EF0A          60      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashRead              0x8002EF82          32      2  Code  Gb  FirmwareUpdateFile.o
  Drv_FlashWrite             0x8002EF42          68      2  Code  Gb  FirmwareUpdateFile.o
  Drv_SystemReset            0x80025224          26      2  Code  Gb  FirmwareUpdateFile.o
  FinishInertialSysAlign     0x8002D484         236      2  Code  Gb  align.o
  GetSmi240Data              0x8002F30A         108      2  Code  Gb  protocol.o
  GetTempRangeNum            0x80024BA6         194      2  Code  Gb  compen.o
  GetZUPTFlag                0x8002E70A          16      2  Code  Gb  ZUPT.o
  GyroANNCompen_X_Init       0x80014B06       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init       0x80016988       8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init       0x8001880A       8 134      2  Code  Gb  AnnTempCompen.o
  GyroCompenCompute          0x80024840         474      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init      0x800204EC       9 214      2  Code  Gb  compen.o
  IMUdataPredo               0x8002D702         468      2  Code  Gb  arithmetic.o
  IMUdataPredp_algParmCache  0x8002D87A         100      2  Code  Gb  arithmetic.o
  INS600mAlgorithmEntry      0x80012692          86      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute    0x8001181A         610      2  Code  Gb  align.o
  InertialSysAlign_Init      0x80011742         222      2  Code  Gb  align.o
  InitialBiasEstimate        0x8002D8CE         772      2  Code  Gb  arithmetic.o
  LinerCompen_60_ANN_Order   0x80025140         222      2  Code  Gb  compen.o
  Mat_Inv                    0x80025696       1 050      2  Code  Gb  matvecmath.o
  Mat_Mul                    0x8002F1E2         228      2  Code  Gb  matvecmath.o
  Mat_Tr                     0x80025640          86      2  Code  Gb  matvecmath.o
  MultiDim_Vec_Dot           0x800255C6         130      2  Code  Gb  matvecmath.o
  NavDataOutputSet           0x80011C1A       2 318      2  Code  Gb  arithmetic.o
  NaviCompute                0x8002DB92         460      2  Code  Gb  navi.o
  Navi_Init                  0x8001286E         314      2  Code  Gb  navi.o
  ParaUpdateHandle           0x8003138A         400      2  Code  Gb  SetParaBao.o
  PureSpi2Send               0x80026436         730      2  Code  Gb  protocol.o
  PureUartSend               0x8002FA06       1 296      2  Code  Gb  protocol.o
  PureUartSend36B            0x800266FC       1 238      2  Code  Gb  protocol.o
  QToCnb                     0x80013322       1 342      2  Code  Gb  navi.o
  Qua_Mul                    0x80025A88         854      2  Code  Gb  matvecmath.o
  RTCompenPara               0x80024FB2         422      2  Code  Gb  compen.o
  ReadPara                   0x80030CB6         268      2  Code  Gb  SetParaBao.o
  ReadPara0_SetEnd           0x8003046C         112      2  Code  Gb  SetParaBao.o
  ReadPara0_SetHead          0x800303E0         140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd           0x8003055A         112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead          0x800304CE         140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd           0x80030648         112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead          0x800305BC         140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd           0x80030736         112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead          0x800306AA         140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd           0x80030826         112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead          0x8003079A         140      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash          0x80030962         356      2  Code  Gb  SetParaBao.o
  ReadPara_0                 0x80027BA0         366      2  Code  Gb  SetParaBao.o
  ReadPara_1                 0x80030A8A         308      2  Code  Gb  SetParaBao.o
  ReadPara_2                 0x80027CDE         298      2  Code  Gb  SetParaBao.o
  ReadPara_3                 0x80027DD4         534      2  Code  Gb  SetParaBao.o
  ReadPara_4                 0x80030B82         368      2  Code  Gb  SetParaBao.o
  Relu                       0x8002F2BE          80      2  Code  Gb  matvecmath.o
  RestoreFactory             0x800275D0         610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash            0x800273C6         582      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd            0x8002FFC6         112      2  Code  Gb  SetParaBao.o
  SendPara_SetHead           0x8002FF3A         140      2  Code  Gb  SetParaBao.o
  SetParaAll                 0x800277EA       1 098      2  Code  Gb  SetParaBao.o
  SetParaAngle               0x80026E4C         314      2  Code  Gb  SetParaBao.o
  SetParaBaud                0x80026B7E         274      2  Code  Gb  SetParaBao.o
  SetParaCalibration         0x80027FAA         278      2  Code  Gb  SetParaBao.o
  SetParaCoord               0x8003088A         256      2  Code  Gb  SetParaBao.o
  SetParaDataOutType         0x80030E6E         240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode           0x80030F3A         240      2  Code  Gb  SetParaBao.o
  SetParaDeviation           0x80027068         314      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc           0x8003122E         392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro          0x800310D2         392      2  Code  Gb  SetParaBao.o
  SetParaFilter              0x8002823C         250      2  Code  Gb  SetParaBao.o
  SetParaFrequency           0x80026C64         254      2  Code  Gb  SetParaBao.o
  SetParaGnss                0x80026D3E         314      2  Code  Gb  SetParaBao.o
  SetParaGnssInitValue       0x80027176         434      2  Code  Gb  SetParaBao.o
  SetParaGpsType             0x80030DA2         240      2  Code  Gb  SetParaBao.o
  SetParaGyroType            0x80031006         240      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ             0x80028098         250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR             0x8002816A         250      2  Code  Gb  SetParaBao.o
  SetParaTemperCompen        0x800316B4         412      2  Code  Gb  SetParaBao.o
  SetParaTime                0x800272EC         254      2  Code  Gb  SetParaBao.o
  SetParaUpdateEnd           0x8002857A         290      2  Code  Gb  SetParaBao.o
  SetParaUpdateSend          0x80028450         334      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart         0x8002830E         358      2  Code  Gb  SetParaBao.o
  SetParaUpdateStop          0x80031506         232      2  Code  Gb  SetParaBao.o
  SetParaVector              0x80026F5A         314      2  Code  Gb  SetParaBao.o
  Smi240DataToAlgorithm      0x8002F376         800      2  Code  Gb  protocol.o
  Smi240Spi2Send             0x8002F67E         216      2  Code  Gb  protocol.o
  Smi240UartSend             0x80025E2C         226      2  Code  Gb  protocol.o
  Smi980SpiTransfer          0x8002E814         180      2  Code  Gb  spi.o
  Smi980_Init                0x800317D6         152      2  Code  Gb  Smi980.o
  Smi980_ReadData            0x80031846         448      2  Code  Gb  Smi980.o
  Spi2Task                   0x8002F074         100      2  Code  Lc  main.o
  SpiInitMaster              0x80014470         314      2  Code  Gb  spi.o
  SpiInitSlave               0x80014570         250      2  Code  Gb  spi.o
  SpiSlaveSend               0x8002E7D4          72      2  Code  Gb  spi.o
  SysVarDefaultSet           0x8001270A         174      2  Code  Gb  navi.o
  Sys_Init                   0x800127B2         242      2  Code  Gb  navi.o
  TaskMange                  0x8002EF9E         244      2  Code  Lc  main.o
  TemperCompenAccAll_0       0x80028926         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1       0x80028AF2         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2       0x80028CBE         278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_3       0x80028E8E         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0    0x80029236         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X1    0x800315CE         280      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2    0x80029C44         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0    0x80029320         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1    0x800297B2         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2    0x80029D2E         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0    0x8002940A         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1    0x8002989C         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2    0x80029E18         282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal      0x8002875A         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0      0x80028840         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1      0x80028A0C         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2      0x80028BD8         278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3      0x80028DA4         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0   0x80028F78         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1   0x800294F4         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2   0x80029986         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0   0x80029062         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1   0x800295DE         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2   0x80029A70         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0   0x8002914C         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1   0x800296C8         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2   0x80029B5A         282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal     0x80028674         278      2  Code  Gb  SetParaBao.o
  Timer_Init                 0x80014640         238      2  Code  Gb  Timer.o
  UartDmaRecSetPara          0x80029F02         998      2  Code  Gb  SetParaBao.o
  UartIrqInit                0x8002EA64         296      2  Code  Gb  Uart_Irq.o
  UartIrqSendMsg             0x8001474E          82      2  Code  Gb  Uart_Irq.o
  UpdateEnd_SetEnd           0x80030290         112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead          0x80030204         140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd          0x800301A2         112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead         0x80030116         140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd         0x800300B4         112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead        0x80030028         140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd          0x8003037E         112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead         0x800302F2         140      2  Code  Gb  SetParaBao.o
  UserTask                   0x80025238         862      2  Code  Lc  main.o
  Vec_Cross                  0x8002F0CA         316      2  Code  Gb  matvecmath.o
  ZUPTAngleConstraint        0x8002E716         104      2  Code  Gb  ZUPT.o
  ZUPTDetection              0x80013BF0       2 014      2  Code  Gb  ZUPT.o
  ZUPTInit                   0x80013A32         446      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_SquareHi_U64  0x80033760          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                             0x8003218C          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat   0x80032180          12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_write  0x80032102         140      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_ascii_isctype
                             0x80034BFA          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                             0x80034C36          44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_mbtowc  0x8002D1EE          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_tolower
                             0x80034C26          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                             0x8002D1D2          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                             0x80034C62          16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                             0x8002D1E0          14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_wctomb  0x80034C72          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                             0x80034C86          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                             0x8002C230          14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnan
                             0x8002C21E          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                             0x8002C23E          18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                             0x80033802           4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalP
                             0x8002BFA8         110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_PolyEvalQ
                             0x80033784         104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_asinacos_fpu
                             0x80033AFE         528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_cos_inline
                             0x8002C110         354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_sin_inline
                             0x8002C006         338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_init_heap     0x8002D1BC          22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_init_prin_l   0x8002D138          38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ldouble_to_double
                             0x8002BF2E         122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pow10f        0x8002D0A2          98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_pre_padding   0x8002D11E          30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_prin_flush    0x8002D0FC          34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_print_padding
                             0x80033EC6          48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_putc          0x80033E2A         160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write  0x80033E1E          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf      0x80033F66       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                             0x80033F66       3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy         0x800353CC          28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_done         0x80010040                  2  Code  Gb  startup.s.o
  __SEGGER_init_heap         0x8002D1A8          26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __SEGGER_init_zero         0x800353B8          20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  __adddf3                   0x80032EE4         728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __addsf3                   0x8002B85A         430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ashldi3                  0x8002C7A0          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  __divdf3                   0x8003347C         448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __divsf3                   0x8003337C         260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __eqsf2                    0x8003363C          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __extendsfdf2              0x8002BE62          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixdfsi                  0x80033668          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixsfsi                  0x8002BC00          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunsdfsi               0x8002BC7C          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfdi               0x800336B8          96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi               0x8002BC4A          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                0x8002BD14          78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsisf                0x8002BCAE         102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatundisf              0x8002BDB8         170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsidf              0x80033718          72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatunsisf              0x8002BD62          86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                    0x8002BBBA          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gesf2                    0x8002BB7C          62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                    0x8002BB36          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                    0x8002BB04          50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                    0x8002BABE          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __lesf2                    0x8002BA88          54      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                    0x8002BA42          70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltsf2                    0x8002BA08          58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __muldf3                   0x8003326C         272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __mulsf3                   0x800331BC         176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                    0x8003363C          44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subdf3                   0x8002B850          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                   0x8002B848          14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __truncdfsf2               0x8002BEA8         134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2               0x800337DE          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __udivdi3                  0x8002C7C6       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  __umoddi3                  0x8002CBDE       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  _clean_up                  0x8002B156         170      2  Code  Wk  reset.c.o
  _init                      0x8003296E           4      2  Code  Wk  reset.c.o
  _start                     0x80010000         142      2  Code  Gb  startup.s.o
  abs                        0x8002D012          10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  analysisRxdata             0x80014798         882      2  Code  Gb  Uart_Irq.o
  asin                       0x8002C79A          10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  atan                       0x8002C310         510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  board_delay_ms             0x80031E8A          24      2  Code  Gb  board.c.o
  board_init                 0x80031B0E          68      2  Code  Gb  board.c.o
  board_init_clock           0x80031B2E       1 112      2  Code  Gb  board.c.o
  board_init_console         0x80031AB0         116      2  Code  Gb  board.c.o
  board_init_pmp             0x80031EE8           4      2  Code  Gb  board.c.o
  board_init_spi_clock       0x80031E9E          64      2  Code  Gb  board.c.o
  board_init_spi_pins        0x80031ED6          24      2  Code  Gb  board.c.o
  board_init_uart            0x8002A4BA          34      2  Code  Gb  board.c.o
  board_init_uart_clock      0x80031EEC         288      2  Code  Gb  board.c.o
  board_init_usb_dp_dm_pins  0x8002A3E0         282      2  Code  Gb  board.c.o
  board_print_banner         0x8002A314          94      2  Code  Gb  board.c.o
  board_print_clock_freq     0x8002A356         222      2  Code  Gb  board.c.o
  clock_add_to_group         0x8002B5AE          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_check_in_group       0x80032B26          52      2  Code  Gb  hpm_clock_drv.c.o
  clock_connect_group_to_cpu
                             0x80032B54          40      2  Code  Gb  hpm_clock_drv.c.o
  clock_cpu_delay_ms         0x80032B7C         224      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency        0x800329A2         200      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group    0x8002B5E8          62      2  Code  Gb  hpm_clock_drv.c.o
  clock_set_source_divider   0x8002B4B4         270      2  Code  Gb  hpm_clock_drv.c.o
  clock_update_core_clock    0x80032C54          36      2  Code  Gb  hpm_clock_drv.c.o
  console_init               0x8003209C         112      2  Code  Gb  hpm_debug_console.c.o
  cos                        0x8003397E           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  crc_verify_8bit            0x8002FF02          56      2  Code  Lc  SetParaBao.o
  dma_check_transfer_status  0x8002E93C         196      2  Code  Lc  uart_dma.o
  enable_plic_feature        0x80032E76          44      2  Code  Gb  system.c.o
  exception_handler          0x8003297A          44      2  Code  Wk  trap.c.o
  exit                       0x8001005A           2      2  Code  Gb  startup.s.o
  floorf                     0x8002C2A6         106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fmodf                      0x80033876         260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fputc                      0x8002B824          42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  frexpf                     0x8003384A          44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  fwrite                     0x8002B7DE          78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  get_frequency_for_adc      0x8002B3A6         162      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ahb      0x80032AFA          48      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_cpu      0x8002B470          74      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_dac      0x80032A40         156      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ewdg     0x8002B43E          58      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_ip_in_common_group
                             0x8002B33A         114      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_pewdg    0x80032AD2          40      2  Code  Lc  hpm_clock_drv.c.o
  get_frequency_for_source   0x8002B28C         206      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_channel_config       0x8002A5CE         306      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_channel_get_default_config
                             0x8002A55C         114      2  Code  Gb  hpm_gptmr_drv.c.o
  gptmr_check_status         0x8002E8D8          36      2  Code  Lc  Timer.o
  gptmr_check_status         0x80031A20          36      2  Code  Lc  board.c.o
  gptmr_clear_status         0x8002E8FC          20      2  Code  Lc  Timer.o
  gptmr_clear_status         0x80031A44          20      2  Code  Lc  board.c.o
  gptmr_enable_irq           0x8002E8BC          28      2  Code  Lc  Timer.o
  gptmr_start_counter        0x8002E910          44      2  Code  Lc  Timer.o
  hpm_csr_get_core_cycle     0x8002B212         122      2  Code  Lc  hpm_clock_drv.c.o
  init_py_pins_as_pgpio      0x80031FDC          76      2  Code  Gb  pinmux.c.o
  init_spi_pins              0x80032028         116      2  Code  Gb  pinmux.c.o
  init_uart_pins             0x8002A4D2         138      2  Code  Gb  pinmux.c.o
  l1c_dc_enable              0x8002B622          54      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_dc_invalidate_all      0x80032C72          24      2  Code  Gb  hpm_l1c_drv.c.o
  l1c_ic_enable              0x8002B658          46      2  Code  Gb  hpm_l1c_drv.c.o
  ldexp                      0x8002C250          86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias           0x8002C250          86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ldexpf                     0x80033806          68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias          0x80033806          68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  main                       0x8002553E         222      2  Code  Gb  main.o
  mchtmr_isr                 0x80032972           4      2  Code  Wk  trap.c.o
  memcpy                     0x8002D01C         134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  memset                     0x80033CB6         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_erase_sector      0x800143E6          50      2  Code  Gb  flash.o
  norflash_init              0x80014336          78      2  Code  Gb  flash.o
  norflash_read              0x8001437C          58      2  Code  Gb  flash.o
  norflash_write             0x800143B2          58      2  Code  Gb  flash.o
  pcfg_dcdc_set_voltage      0x80032198          72      2  Code  Gb  hpm_pcfg_drv.c.o
  pllctlv2_get_pll_freq_in_hz
                             0x80032298         248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                             0x8002A772         222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_init_pll_with_freq
                             0x800321E0         184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_set_postdiv       0x8002A700         114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  pllctlv2_xtal_is_enabled   0x80031A94          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable    0x80031A78          28      2  Code  Lc  board.c.o
  pllctlv2_xtal_set_rampup_time
                             0x8002A2EE          38      2  Code  Lc  board.c.o
  ppor_sw_reset              0x80025212          18      2  Code  Lc  FirmwareUpdateFile.o
  printf                     0x8002D180          46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  putchar                    0x80032EA2          16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  puts                       0x80032EAE          68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  reset_handler              0x80032956          32      2  Code  Wk  reset.c.o
  rom_xpi_nor_auto_config    0x8002E7AC          40      2  Code  Lc  flash.o
  rom_xpi_nor_erase_sector   0x800142BA          58      2  Code  Lc  flash.o
  rom_xpi_nor_program        0x800142F4          66      2  Code  Lc  flash.o
  rom_xpi_nor_read           0x8002E770          60      2  Code  Lc  flash.o
  sin                        0x8003397A           8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  spi_control_init           0x80032504         284      2  Code  Gb  hpm_spi_drv.c.o
  spi_format_init            0x80032484         128      2  Code  Gb  hpm_spi_drv.c.o
  spi_get_data_length_in_bits
                             0x80032370          32      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_data_length_in_bytes
                             0x8002A82C          34      2  Code  Lc  hpm_spi_drv.c.o
  spi_get_rx_fifo_valid_data_size
                             0x8002A84A          58      2  Code  Lc  hpm_spi_drv.c.o
  spi_master_get_default_control_config
                             0x8002AC68          82      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_format_config
                             0x80032404          68      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_get_default_timing_config
                             0x8002AC4E          26      2  Code  Gb  hpm_spi_drv.c.o
  spi_master_timing_init     0x8002ACF4         158      2  Code  Gb  hpm_spi_drv.c.o
  spi_no_data                0x8002AC10          62      2  Code  Lc  hpm_spi_drv.c.o
  spi_read_command           0x8002A8C6          70      2  Code  Gb  hpm_spi_drv.c.o
  spi_read_data              0x8002A9D2         242      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                             0x8002ACBA          58      2  Code  Gb  hpm_spi_drv.c.o
  spi_slave_get_default_format_config
                             0x80032448          60      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer               0x80032620         460      2  Code  Gb  hpm_spi_drv.c.o
  spi_transfer_mode_print    0x80014412         102      2  Code  Gb  spi.o
  spi_wait_for_idle_status   0x80032390          64      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_address          0x800323D0          52      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_command          0x8002A884          66      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_data             0x8002A90C         198      2  Code  Gb  hpm_spi_drv.c.o
  spi_write_read_data        0x8002AABE         338      2  Code  Gb  hpm_spi_drv.c.o
  sqrt                       0x8002C4B8         778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  start                      0x80010054                  2  Code  Gb  startup.s.o
  strlen                     0x80033D1E         104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strnlen                    0x80033D86         152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  swi_isr                    0x80032976           4      2  Code  Wk  trap.c.o
  syscall_handler            0x8002B200          18      2  Code  Wk  trap.c.o
  sysctl_check_group_resource_enable
                             0x80032D4E         116      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_clock_set_preset    0x8002A2C4          42      2  Code  Lc  board.c.o
  sysctl_clock_target_is_busy
                             0x8002B6D2          46      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_config_clock        0x8002B700         142      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_config_cpu0_domain_clock
                             0x80032DC2         188      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_cpu_clock_any_is_busy
                             0x8002B6B0          34      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_enable_group_resource
                             0x80032C8A         200      2  Code  Gb  hpm_sysctl_drv.c.o
  sysctl_resource_any_is_busy
                             0x800319C4          28      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                             0x8002A29E          38      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002A274          42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                             0x8002B686          42      2  Code  Lc  hpm_sysctl_drv.c.o
  sysctl_resource_target_set_mode
                             0x800319E0          64      2  Code  Lc  board.c.o
  system_init                0x8002B788          90      2  Code  Wk  system.c.o
  tan                        0x80033982         488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate    0x8002AD92         526      2  Code  Lc  hpm_uart_drv.c.o
  uart_check_status          0x8001472C          34      2  Code  Lc  Uart_Irq.o
  uart_default_config        0x80032826         148      2  Code  Gb  hpm_uart_drv.c.o
  uart_disable_irq           0x8002EA18          28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq           0x800327F2          28      2  Code  Lc  hpm_uart_drv.c.o
  uart_enable_irq            0x8002EA34          24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq            0x8003280E          24      2  Code  Lc  hpm_uart_drv.c.o
  uart_flush                 0x800328BA          64      2  Code  Gb  hpm_uart_drv.c.o
  uart_get_irq_id            0x8002EA4C          24      2  Code  Lc  Uart_Irq.o
  uart_init                  0x8002AF54         454      2  Code  Gb  hpm_uart_drv.c.o
  uart_init_rxline_idle_detection
                             0x800328FA         104      2  Code  Gb  hpm_uart_drv.c.o
  uart_modem_config          0x800327B6          60      2  Code  Lc  hpm_uart_drv.c.o
  uart_read_byte             0x8001471A          18      2  Code  Lc  Uart_Irq.o
  uart_send_byte             0x8002B108          78      2  Code  Gb  hpm_uart_drv.c.o
  uart_write_byte            0x8002EA00          24      2  Code  Lc  Uart_Irq.o
  usb_phy_disable_dp_dm_pulldown
                             0x80031A58          32      2  Code  Lc  board.c.o
  vfprintf                   0x8002D15A          46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  vfprintf_l                 0x80033EF0         132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  xor_check                  0x80025DE2          74      2  Code  Gb  protocol.o

Function symbols by address:

     Address  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  -------------------------  ----------  -----  ----  --  -----------
  0x80010000  _start                            142      2  Code  Gb  startup.s.o
  0x80010040  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  0x80010054  start                                      2  Code  Gb  startup.s.o
  0x8001005A  exit                                2      2  Code  Gb  startup.s.o
  0x800115FE  ComputeCen                        354      2  Code  Gb  align.o
  0x80011742  InertialSysAlign_Init             222      2  Code  Gb  align.o
  0x8001181A  InertialSysAlignCompute           610      2  Code  Gb  align.o
  0x80011A52  ComputeVi                         266      2  Code  Gb  align.o
  0x80011B46  ComputeVib0                       166      2  Code  Gb  align.o
  0x80011C1A  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  0x800124A6  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  0x80012692  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  0x8001270A  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  0x800127B2  Sys_Init                          242      2  Code  Gb  navi.o
  0x8001286E  Navi_Init                         314      2  Code  Gb  navi.o
  0x800129A2  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  0x80012A7E  ComputeQ                          926      2  Code  Gb  navi.o
  0x80013240  ComputeWnbb                       238      2  Code  Gb  navi.o
  0x80013322  QToCnb                          1 342      2  Code  Gb  navi.o
  0x800137AC  AttiToCnb                         750      2  Code  Gb  navi.o
  0x80013A32  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  0x80013BF0  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  0x800142BA  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  0x800142F4  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  0x80014336  norflash_init                      78      2  Code  Gb  flash.o
  0x8001437C  norflash_read                      58      2  Code  Gb  flash.o
  0x800143B2  norflash_write                     58      2  Code  Gb  flash.o
  0x800143E6  norflash_erase_sector              50      2  Code  Gb  flash.o
  0x80014412  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  0x80014470  SpiInitMaster                     314      2  Code  Gb  spi.o
  0x80014570  SpiInitSlave                      250      2  Code  Gb  spi.o
  0x80014640  Timer_Init                        238      2  Code  Gb  Timer.o
  0x8001471A  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  0x8001472C  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  0x8001474E  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  0x80014798  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  0x80014B06  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x80016988  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x8001880A  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  0x8001A68C  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001C50E  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x8001E390  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  0x80020212  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  0x800204EC  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  0x80022686  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  0x80024840  GyroCompenCompute                 474      2  Code  Gb  compen.o
  0x800249EC  AccCompenCompute                  490      2  Code  Gb  compen.o
  0x80024BA6  GetTempRangeNum                   194      2  Code  Gb  compen.o
  0x80024C64  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  0x80024FB2  RTCompenPara                      422      2  Code  Gb  compen.o
  0x80025140  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  0x80025212  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  0x80025224  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  0x80025238  UserTask                          862      2  Code  Lc  main.o
  0x8002553E  main                              222      2  Code  Gb  main.o
  0x800255C6  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  0x80025640  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  0x80025696  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  0x80025A88  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  0x80025D6E  Check_8bit                         58      2  Code  Gb  protocol.o
  0x80025DA8  Check_16bit                        58      2  Code  Gb  protocol.o
  0x80025DE2  xor_check                          74      2  Code  Gb  protocol.o
  0x80025E2C  Smi240UartSend                    226      2  Code  Gb  protocol.o
  0x80025F00  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  0x800261AE  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  0x80026436  PureSpi2Send                      730      2  Code  Gb  protocol.o
  0x800266FC  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  0x80026B7E  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  0x80026C64  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  0x80026D3E  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  0x80026E4C  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  0x80026F5A  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  0x80027068  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  0x80027176  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  0x800272EC  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  0x800273C6  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  0x800275D0  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  0x800277EA  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  0x80027BA0  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  0x80027CDE  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  0x80027DD4  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  0x80027FAA  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  0x80028098  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  0x8002816A  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  0x8002823C  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  0x8002830E  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  0x80028450  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  0x8002857A  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  0x80028674  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  0x8002875A  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  0x80028840  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  0x80028926  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  0x80028A0C  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  0x80028AF2  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  0x80028BD8  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  0x80028CBE  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  0x80028DA4  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  0x80028E8E  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  0x80028F78  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  0x80029062  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  0x8002914C  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  0x80029236  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  0x80029320  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  0x8002940A  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  0x800294F4  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  0x800295DE  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  0x800296C8  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  0x800297B2  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  0x8002989C  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  0x80029986  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  0x80029A70  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  0x80029B5A  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  0x80029C44  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  0x80029D2E  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  0x80029E18  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  0x80029F02  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  0x8002A274  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  board.c.o
  0x8002A29E  sysctl_resource_target_get_mode
                                                 38      2  Code  Lc  board.c.o
  0x8002A2C4  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  0x8002A2EE  pllctlv2_xtal_set_rampup_time
                                                 38      2  Code  Lc  board.c.o
  0x8002A314  board_print_banner                 94      2  Code  Gb  board.c.o
  0x8002A356  board_print_clock_freq            222      2  Code  Gb  board.c.o
  0x8002A3E0  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  0x8002A4BA  board_init_uart                    34      2  Code  Gb  board.c.o
  0x8002A4D2  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  0x8002A55C  gptmr_channel_get_default_config
                                                114      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002A5CE  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  0x8002A700  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002A772  pllctlv2_get_pll_postdiv_freq_in_hz
                                                222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x8002A82C  spi_get_data_length_in_bytes
                                                 34      2  Code  Lc  hpm_spi_drv.c.o
  0x8002A84A  spi_get_rx_fifo_valid_data_size
                                                 58      2  Code  Lc  hpm_spi_drv.c.o
  0x8002A884  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  0x8002A8C6  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  0x8002A90C  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  0x8002A9D2  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  0x8002AABE  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  0x8002AC10  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  0x8002AC4E  spi_master_get_default_timing_config
                                                 26      2  Code  Gb  hpm_spi_drv.c.o
  0x8002AC68  spi_master_get_default_control_config
                                                 82      2  Code  Gb  hpm_spi_drv.c.o
  0x8002ACBA  spi_slave_get_default_control_config
                                                 58      2  Code  Gb  hpm_spi_drv.c.o
  0x8002ACF4  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  0x8002AD92  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  0x8002AF54  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  0x8002B108  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  0x8002B156  _clean_up                         170      2  Code  Wk  reset.c.o
  0x8002B200  syscall_handler                    18      2  Code  Wk  trap.c.o
  0x8002B212  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  0x8002B28C  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  0x8002B33A  get_frequency_for_ip_in_common_group
                                                114      2  Code  Lc  hpm_clock_drv.c.o
  0x8002B3A6  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  0x8002B43E  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  0x8002B470  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  0x8002B4B4  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  0x8002B5AE  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002B5E8  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  0x8002B622  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002B658  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  0x8002B686  sysctl_resource_target_is_busy
                                                 42      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002B6B0  sysctl_cpu_clock_any_is_busy
                                                 34      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002B6D2  sysctl_clock_target_is_busy
                                                 46      2  Code  Lc  hpm_sysctl_drv.c.o
  0x8002B700  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  0x8002B788  system_init                        90      2  Code  Wk  system.c.o
  0x8002B7DE  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002B824  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x8002B848  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002B850  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002B85A  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BA08  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BA42  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BA88  __lesf2                            54      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BABE  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BB04  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BB36  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BB7C  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BBBA  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BC00  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BC4A  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BC7C  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BCAE  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BD14  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BD62  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BDB8  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BE62  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BEA8  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002BF2E  __SEGGER_RTL_ldouble_to_double
                                                122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002BFA8  __SEGGER_RTL_float64_PolyEvalP
                                                110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C006  __SEGGER_RTL_float64_sin_inline
                                                338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C110  __SEGGER_RTL_float64_cos_inline
                                                354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C21E  __SEGGER_RTL_float32_isnan
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C230  __SEGGER_RTL_float32_isinf
                                                 14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C23E  __SEGGER_RTL_float32_isnormal
                                                 18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C250  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x8002C250  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C2A6  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C310  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C4B8  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C79A  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8002C7A0  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002C7C6  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002CBDE  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D012  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  0x8002D01C  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x8002D0A2  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  0x8002D0FC  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002D11E  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002D138  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002D15A  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x8002D180  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  0x8002D1A8  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x8002D1BC  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x8002D1D2  __SEGGER_RTL_ascii_toupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002D1E0  __SEGGER_RTL_ascii_towupper
                                                 14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002D1EE  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x8002D218  ComputeCie                        340      2  Code  Gb  align.o
  0x8002D34C  ComputeCib0i                      336      2  Code  Gb  align.o
  0x8002D484  FinishInertialSysAlign            236      2  Code  Gb  align.o
  0x8002D54E  ApplyBiasCorrectionToCombineData
                                                472      2  Code  Gb  arithmetic.o
  0x8002D702  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  0x8002D87A  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  0x8002D8CE  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  0x8002DB52  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  0x8002DB62  Bind_Init                          56      2  Code  Gb  navi.o
  0x8002DB92  NaviCompute                       460      2  Code  Gb  navi.o
  0x8002DD32  ComputeG                          444      2  Code  Gb  navi.o
  0x8002DE92  ComputeRmRn                       312      2  Code  Gb  navi.o
  0x8002DF8A  ComputeWien                       140      2  Code  Gb  navi.o
  0x8002DFFE  ComputeWenn                       192      2  Code  Gb  navi.o
  0x8002E0AE  CnbToAtti                         728      2  Code  Gb  navi.o
  0x8002E31A  CnbToQ                          1 224      2  Code  Gb  navi.o
  0x8002E70A  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  0x8002E716  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  0x8002E770  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  0x8002E7AC  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  0x8002E7D4  SpiSlaveSend                       72      2  Code  Gb  spi.o
  0x8002E814  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  0x8002E8BC  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  0x8002E8D8  gptmr_check_status                 36      2  Code  Lc  Timer.o
  0x8002E8FC  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  0x8002E910  gptmr_start_counter                44      2  Code  Lc  Timer.o
  0x8002E93C  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  0x8002EA00  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  0x8002EA18  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  0x8002EA34  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  0x8002EA4C  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  0x8002EA64  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  0x8002EB6E  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  0x8002EBC2  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  0x8002EF0A  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  0x8002EF42  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  0x8002EF82  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  0x8002EF9E  TaskMange                         244      2  Code  Lc  main.o
  0x8002F074  Spi2Task                          100      2  Code  Lc  main.o
  0x8002F0CA  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  0x8002F1E2  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  0x8002F2BE  Relu                               80      2  Code  Gb  matvecmath.o
  0x8002F30A  GetSmi240Data                     108      2  Code  Gb  protocol.o
  0x8002F376  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  0x8002F67E  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  0x8002F74A  CombinationUartSend               816      2  Code  Gb  protocol.o
  0x8002FA06  PureUartSend                    1 296      2  Code  Gb  protocol.o
  0x8002FF02  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  0x8002FF3A  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  0x8002FFC6  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  0x80030028  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  0x800300B4  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  0x80030116  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  0x800301A2  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x80030204  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030290  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800302F2  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  0x8003037E  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  0x800303E0  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x8003046C  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800304CE  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x8003055A  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800305BC  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030648  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x800306AA  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030736  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x8003079A  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  0x80030826  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  0x8003088A  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  0x80030962  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  0x80030A8A  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  0x80030B82  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  0x80030CB6  ReadPara                          268      2  Code  Gb  SetParaBao.o
  0x80030DA2  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  0x80030E6E  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  0x80030F3A  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  0x80031006  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  0x800310D2  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  0x8003122E  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  0x8003138A  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  0x80031506  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  0x800315CE  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  0x800316B4  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  0x800317D6  Smi980_Init                       152      2  Code  Gb  Smi980.o
  0x80031846  Smi980_ReadData                   448      2  Code  Gb  Smi980.o
  0x800319C4  sysctl_resource_any_is_busy
                                                 28      2  Code  Lc  board.c.o
  0x800319E0  sysctl_resource_target_set_mode
                                                 64      2  Code  Lc  board.c.o
  0x80031A20  gptmr_check_status                 36      2  Code  Lc  board.c.o
  0x80031A44  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  0x80031A58  usb_phy_disable_dp_dm_pulldown
                                                 32      2  Code  Lc  board.c.o
  0x80031A78  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  0x80031A94  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  0x80031AB0  board_init_console                116      2  Code  Gb  board.c.o
  0x80031B0E  board_init                         68      2  Code  Gb  board.c.o
  0x80031B2E  board_init_clock                1 112      2  Code  Gb  board.c.o
  0x80031E8A  board_delay_ms                     24      2  Code  Gb  board.c.o
  0x80031E9E  board_init_spi_clock               64      2  Code  Gb  board.c.o
  0x80031ED6  board_init_spi_pins                24      2  Code  Gb  board.c.o
  0x80031EE8  board_init_pmp                      4      2  Code  Gb  board.c.o
  0x80031EEC  board_init_uart_clock             288      2  Code  Gb  board.c.o
  0x80031FDC  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  0x80032028  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  0x8003209C  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  0x80032102  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  0x80032180  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  0x8003218C  __SEGGER_RTL_X_file_bufsize
                                                 12      2  Code  Gb  hpm_debug_console.c.o
  0x80032198  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  0x800321E0  pllctlv2_init_pll_with_freq
                                                184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x80032298  pllctlv2_get_pll_freq_in_hz
                                                248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  0x80032370  spi_get_data_length_in_bits
                                                 32      2  Code  Lc  hpm_spi_drv.c.o
  0x80032390  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  0x800323D0  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  0x80032404  spi_master_get_default_format_config
                                                 68      2  Code  Gb  hpm_spi_drv.c.o
  0x80032448  spi_slave_get_default_format_config
                                                 60      2  Code  Gb  hpm_spi_drv.c.o
  0x80032484  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  0x80032504  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  0x80032620  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  0x800327B6  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  0x800327F2  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  0x8003280E  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  0x80032826  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  0x800328BA  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  0x800328FA  uart_init_rxline_idle_detection
                                                104      2  Code  Gb  hpm_uart_drv.c.o
  0x80032956  reset_handler                      32      2  Code  Wk  reset.c.o
  0x8003296E  _init                               4      2  Code  Wk  reset.c.o
  0x80032972  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  0x80032976  swi_isr                             4      2  Code  Wk  trap.c.o
  0x8003297A  exception_handler                  44      2  Code  Wk  trap.c.o
  0x800329A2  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  0x80032A40  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  0x80032AD2  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  0x80032AFA  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  0x80032B26  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  0x80032B54  clock_connect_group_to_cpu
                                                 40      2  Code  Gb  hpm_clock_drv.c.o
  0x80032B7C  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  0x80032C54  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  0x80032C72  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  0x80032C8A  sysctl_enable_group_resource
                                                200      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80032D4E  sysctl_check_group_resource_enable
                                                116      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80032DC2  sysctl_config_cpu0_domain_clock
                                                188      2  Code  Gb  hpm_sysctl_drv.c.o
  0x80032E76  enable_plic_feature                44      2  Code  Gb  system.c.o
  0x80032EA2  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80032EAE  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  0x80032EE4  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800331BC  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003326C  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003337C  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003347C  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003363C  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x8003363C  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033668  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800336B8  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033718  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033760  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80033784  __SEGGER_RTL_float64_PolyEvalQ
                                                104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800337DE  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80033802  __SEGGER_RTL_float32_signbit
                                                  4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80033806  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80033806  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003384A  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80033876  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003397A  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x8003397E  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80033982  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  0x80033AFE  __SEGGER_RTL_float64_asinacos_fpu
                                                528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80033CB6  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033D1E  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  0x80033D86  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  0x80033E1E  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  0x80033E2A  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80033EC6  __SEGGER_RTL_print_padding
                                                 48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80033EF0  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80033F66  __SEGGER_RTL_vfprintf_short_float_long
                                              3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x80033F66  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  0x80034BFA  __SEGGER_RTL_ascii_isctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C26  __SEGGER_RTL_ascii_tolower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C36  __SEGGER_RTL_ascii_iswctype
                                                 44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C62  __SEGGER_RTL_ascii_towlower
                                                 16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C72  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C86  __SEGGER_RTL_current_locale
                                                 20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800353B8  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  0x800353CC  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)

Function symbols by descending size:

  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  Acc_Compen_Para_Init            9 246      2  Code  Gb  compen.o
  Gyro_Compen_Para_Init           9 214      2  Code  Gb  compen.o
  AccANNCompen_X_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Y_Init             8 134      2  Code  Gb  AnnTempCompen.o
  AccANNCompen_Z_Init             8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_X_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Y_Init            8 134      2  Code  Gb  AnnTempCompen.o
  GyroANNCompen_Z_Init            8 134      2  Code  Gb  AnnTempCompen.o
  __SEGGER_RTL_vfprintf           3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_vfprintf_short_float_long
                                  3 600      2  Code  Gb  __SEGGER_RTL_vfprintf_short_float_long.o (libc_rv32imac_balanced.a)
  NavDataOutputSet                2 318      2  Code  Gb  arithmetic.o
  ZUPTDetection                   2 014      2  Code  Gb  ZUPT.o
  QToCnb                          1 342      2  Code  Gb  navi.o
  PureUartSend                    1 296      2  Code  Gb  protocol.o
  PureUartSend36B                 1 238      2  Code  Gb  protocol.o
  CnbToQ                          1 224      2  Code  Gb  navi.o
  board_init_clock                1 112      2  Code  Gb  board.c.o
  __umoddi3                       1 102      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  SetParaAll                      1 098      2  Code  Gb  SetParaBao.o
  __udivdi3                       1 074      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  Mat_Inv                         1 050      2  Code  Gb  matvecmath.o
  UartDmaRecSetPara                 998      2  Code  Gb  SetParaBao.o
  ComputeQ                          926      2  Code  Gb  navi.o
  ComputeAccTempDiff                886      2  Code  Gb  compen.o
  analysisRxdata                    882      2  Code  Gb  Uart_Irq.o
  ComputeGyroTempDiff               880      2  Code  Gb  compen.o
  UserTask                          862      2  Code  Lc  main.o
  Qua_Mul                           854      2  Code  Gb  matvecmath.o
  ANN_Predict                       818      2  Code  Gb  AnnTempCompen.o
  CombinationUartSend               816      2  Code  Gb  protocol.o
  CombinationSpi2Send               806      2  Code  Gb  protocol.o
  Smi240DataToAlgorithm             800      2  Code  Gb  protocol.o
  sqrt                              778      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  InitialBiasEstimate               772      2  Code  Gb  arithmetic.o
  CombinationUartSend22B            758      2  Code  Gb  protocol.o
  AttiToCnb                         750      2  Code  Gb  navi.o
  PureSpi2Send                      730      2  Code  Gb  protocol.o
  CnbToAtti                         728      2  Code  Gb  navi.o
  __adddf3                          728      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  AlgorithmAct                      634      2  Code  Gb  arithmetic.o
  InertialSysAlignCompute           610      2  Code  Gb  align.o
  RestoreFactory                    610      2  Code  Gb  SetParaBao.o
  SaveParaToFlash                   582      2  Code  Gb  SetParaBao.o
  ReadPara_3                        534      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_float64_asinacos_fpu
                                    528      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  uart_calculate_baudrate           526      2  Code  Lc  hpm_uart_drv.c.o
  atan                              510      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  AccCompenCompute                  490      2  Code  Gb  compen.o
  tan                               488      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  GyroCompenCompute                 474      2  Code  Gb  compen.o
  ApplyBiasCorrectionToCombineData
                                    472      2  Code  Gb  arithmetic.o
  IMUdataPredo                      468      2  Code  Gb  arithmetic.o
  NaviCompute                       460      2  Code  Gb  navi.o
  spi_transfer                      460      2  Code  Gb  hpm_spi_drv.c.o
  uart_init                         454      2  Code  Gb  hpm_uart_drv.c.o
  Smi980_ReadData                   448      2  Code  Gb  Smi980.o
  __divdf3                          448      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ZUPTInit                          446      2  Code  Gb  ZUPT.o
  ComputeG                          444      2  Code  Gb  navi.o
  SetParaGnssInitValue              434      2  Code  Gb  SetParaBao.o
  __addsf3                          430      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  RTCompenPara                      422      2  Code  Gb  compen.o
  SetParaTemperCompen               412      2  Code  Gb  SetParaBao.o
  ParaUpdateHandle                  400      2  Code  Gb  SetParaBao.o
  SetParaFactorAcc                  392      2  Code  Gb  SetParaBao.o
  SetParaFactorGyro                 392      2  Code  Gb  SetParaBao.o
  ReadPara_4                        368      2  Code  Gb  SetParaBao.o
  ReadPara_0                        366      2  Code  Gb  SetParaBao.o
  SetParaUpdateStart                358      2  Code  Gb  SetParaBao.o
  ReadParaFromFlash                 356      2  Code  Gb  SetParaBao.o
  ComputeCen                        354      2  Code  Gb  align.o
  __SEGGER_RTL_float64_cos_inline
                                    354      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ComputeCie                        340      2  Code  Gb  align.o
  __SEGGER_RTL_float64_sin_inline
                                    338      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  spi_write_read_data               338      2  Code  Gb  hpm_spi_drv.c.o
  ComputeCib0i                      336      2  Code  Gb  align.o
  SetParaUpdateSend                 334      2  Code  Gb  SetParaBao.o
  Vec_Cross                         316      2  Code  Gb  matvecmath.o
  Navi_Init                         314      2  Code  Gb  navi.o
  SetParaAngle                      314      2  Code  Gb  SetParaBao.o
  SetParaDeviation                  314      2  Code  Gb  SetParaBao.o
  SetParaGnss                       314      2  Code  Gb  SetParaBao.o
  SetParaVector                     314      2  Code  Gb  SetParaBao.o
  SpiInitMaster                     314      2  Code  Gb  spi.o
  ComputeRmRn                       312      2  Code  Gb  navi.o
  ReadPara_1                        308      2  Code  Gb  SetParaBao.o
  gptmr_channel_config              306      2  Code  Gb  hpm_gptmr_drv.c.o
  ReadPara_2                        298      2  Code  Gb  SetParaBao.o
  UartIrqInit                       296      2  Code  Gb  Uart_Irq.o
  SetParaUpdateEnd                  290      2  Code  Gb  SetParaBao.o
  board_init_uart_clock             288      2  Code  Gb  board.c.o
  spi_control_init                  284      2  Code  Gb  hpm_spi_drv.c.o
  TemperCompenAccAll_3              282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_X2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Y2           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z0           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z1           282      2  Code  Gb  SetParaBao.o
  TemperCompenAccNerve_Z2           282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_3             282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_X2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Y2          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z0          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z1          282      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNerve_Z2          282      2  Code  Gb  SetParaBao.o
  board_init_usb_dp_dm_pins         282      2  Code  Gb  board.c.o
  TemperCompenAccNerve_X1           280      2  Code  Gb  SetParaBao.o
  SetParaCalibration                278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_0              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_1              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccAll_2              278      2  Code  Gb  SetParaBao.o
  TemperCompenAccNormal             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_0             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_1             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroAll_2             278      2  Code  Gb  SetParaBao.o
  TemperCompenGyroNormal            278      2  Code  Gb  SetParaBao.o
  SetParaBaud                       274      2  Code  Gb  SetParaBao.o
  __muldf3                          272      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_set_source_divider          270      2  Code  Gb  hpm_clock_drv.c.o
  ReadPara                          268      2  Code  Gb  SetParaBao.o
  ComputeVi                         266      2  Code  Gb  align.o
  __divsf3                          260      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fmodf                             260      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  SetParaCoord                      256      2  Code  Gb  SetParaBao.o
  SetParaFrequency                  254      2  Code  Gb  SetParaBao.o
  SetParaTime                       254      2  Code  Gb  SetParaBao.o
  SetParaFilter                     250      2  Code  Gb  SetParaBao.o
  SetParaKalmanQ                    250      2  Code  Gb  SetParaBao.o
  SetParaKalmanR                    250      2  Code  Gb  SetParaBao.o
  SpiInitSlave                      250      2  Code  Gb  spi.o
  pllctlv2_get_pll_freq_in_hz
                                    248      2  Code  Gb  hpm_pllctlv2_drv.c.o
  TaskMange                         244      2  Code  Lc  main.o
  Sys_Init                          242      2  Code  Gb  navi.o
  spi_read_data                     242      2  Code  Gb  hpm_spi_drv.c.o
  SetParaDataOutType                240      2  Code  Gb  SetParaBao.o
  SetParaDebugMode                  240      2  Code  Gb  SetParaBao.o
  SetParaGpsType                    240      2  Code  Gb  SetParaBao.o
  SetParaGyroType                   240      2  Code  Gb  SetParaBao.o
  ComputeWnbb                       238      2  Code  Gb  navi.o
  Timer_Init                        238      2  Code  Gb  Timer.o
  FinishInertialSysAlign            236      2  Code  Gb  align.o
  SetParaUpdateStop                 232      2  Code  Gb  SetParaBao.o
  ComputeDelSenbb                   230      2  Code  Gb  navi.o
  Mat_Mul                           228      2  Code  Gb  matvecmath.o
  Smi240UartSend                    226      2  Code  Gb  protocol.o
  clock_cpu_delay_ms                224      2  Code  Gb  hpm_clock_drv.c.o
  InertialSysAlign_Init             222      2  Code  Gb  align.o
  LinerCompen_60_ANN_Order          222      2  Code  Gb  compen.o
  board_print_clock_freq            222      2  Code  Gb  board.c.o
  main                              222      2  Code  Gb  main.o
  pllctlv2_get_pll_postdiv_freq_in_hz
                                    222      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi240Spi2Send                    216      2  Code  Gb  protocol.o
  get_frequency_for_source          206      2  Code  Gb  hpm_clock_drv.c.o
  clock_get_frequency               200      2  Code  Gb  hpm_clock_drv.c.o
  sysctl_enable_group_resource
                                    200      2  Code  Gb  hpm_sysctl_drv.c.o
  spi_write_data                    198      2  Code  Gb  hpm_spi_drv.c.o
  dma_check_transfer_status         196      2  Code  Lc  uart_dma.o
  GetTempRangeNum                   194      2  Code  Gb  compen.o
  ComputeWenn                       192      2  Code  Gb  navi.o
  sysctl_config_cpu0_domain_clock
                                    188      2  Code  Gb  hpm_sysctl_drv.c.o
  pllctlv2_init_pll_with_freq
                                    184      2  Code  Gb  hpm_pllctlv2_drv.c.o
  Smi980SpiTransfer                 180      2  Code  Gb  spi.o
  __mulsf3                          176      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  SysVarDefaultSet                  174      2  Code  Gb  navi.o
  __floatundisf                     170      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  _clean_up                         170      2  Code  Wk  reset.c.o
  ComputeVib0                       166      2  Code  Gb  align.o
  get_frequency_for_adc             162      2  Code  Lc  hpm_clock_drv.c.o
  __SEGGER_RTL_putc                 160      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  spi_master_timing_init            158      2  Code  Gb  hpm_spi_drv.c.o
  get_frequency_for_dac             156      2  Code  Lc  hpm_clock_drv.c.o
  Smi980_Init                       152      2  Code  Gb  Smi980.o
  strnlen                           152      2  Code  Wk  strops.o (libc_rv32imac_balanced.a)
  uart_default_config               148      2  Code  Gb  hpm_uart_drv.c.o
  _start                            142      2  Code  Gb  startup.s.o
  sysctl_config_clock               142      2  Code  Gb  hpm_sysctl_drv.c.o
  ComputeWien                       140      2  Code  Gb  navi.o
  ReadPara0_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara1_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara2_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara3_SetHead                 140      2  Code  Gb  SetParaBao.o
  ReadPara4_SetHead                 140      2  Code  Gb  SetParaBao.o
  SendPara_SetHead                  140      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetHead                 140      2  Code  Gb  SetParaBao.o
  UpdateSend_SetHead                140      2  Code  Gb  SetParaBao.o
  UpdateStart_SetHead               140      2  Code  Gb  SetParaBao.o
  UpdateStop_SetHead                140      2  Code  Gb  SetParaBao.o
  __SEGGER_RTL_X_file_write         140      2  Code  Gb  hpm_debug_console.c.o
  init_uart_pins                    138      2  Code  Gb  pinmux.c.o
  __truncdfsf2                      134      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  memcpy                            134      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  vfprintf_l                        132      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  MultiDim_Vec_Dot                  130      2  Code  Gb  matvecmath.o
  spi_format_init                   128      2  Code  Gb  hpm_spi_drv.c.o
  __SEGGER_RTL_ldouble_to_double
                                    122      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  hpm_csr_get_core_cycle            122      2  Code  Lc  hpm_clock_drv.c.o
  board_init_console                116      2  Code  Gb  board.c.o
  init_spi_pins                     116      2  Code  Gb  pinmux.c.o
  sysctl_check_group_resource_enable
                                    116      2  Code  Gb  hpm_sysctl_drv.c.o
  get_frequency_for_ip_in_common_group
                                    114      2  Code  Lc  hpm_clock_drv.c.o
  gptmr_channel_get_default_config
                                    114      2  Code  Gb  hpm_gptmr_drv.c.o
  pllctlv2_set_postdiv              114      2  Code  Gb  hpm_pllctlv2_drv.c.o
  ReadPara0_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara1_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara2_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara3_SetEnd                  112      2  Code  Gb  SetParaBao.o
  ReadPara4_SetEnd                  112      2  Code  Gb  SetParaBao.o
  SendPara_SetEnd                   112      2  Code  Gb  SetParaBao.o
  UpdateEnd_SetEnd                  112      2  Code  Gb  SetParaBao.o
  UpdateSend_SetEnd                 112      2  Code  Gb  SetParaBao.o
  UpdateStart_SetEnd                112      2  Code  Gb  SetParaBao.o
  UpdateStop_SetEnd                 112      2  Code  Gb  SetParaBao.o
  console_init                      112      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_float64_PolyEvalP
                                    110      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  ANNCompen_Init                    108      2  Code  Gb  AnnTempCompen.o
  GetSmi240Data                     108      2  Code  Gb  protocol.o
  floorf                            106      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ZUPTAngleConstraint               104      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_float64_PolyEvalQ
                                    104      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  memset                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  strlen                            104      2  Code  Gb  strasmops_rv.o (libc_rv32imac_balanced.a)
  uart_init_rxline_idle_detection
                                    104      2  Code  Gb  hpm_uart_drv.c.o
  __floatsisf                       102      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_transfer_mode_print           102      2  Code  Gb  spi.o
  IMUdataPredp_algParmCache         100      2  Code  Gb  arithmetic.o
  Spi2Task                          100      2  Code  Lc  main.o
  __SEGGER_RTL_pow10f                98      2  Code  Gb  utilops.o (libc_rv32imac_balanced.a)
  __fixunssfdi                       96      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  board_print_banner                 94      2  Code  Gb  board.c.o
  system_init                        90      2  Code  Wk  system.c.o
  INS600mAlgorithmEntry              86      2  Code  Gb  arithmetic.o
  Mat_Tr                             86      2  Code  Gb  matvecmath.o
  __floatunsisf                      86      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  ldexp                              86      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexp.localalias                   86      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  UartIrqSendMsg                     82      2  Code  Gb  Uart_Irq.o
  spi_master_get_default_control_config
                                     82      2  Code  Gb  hpm_spi_drv.c.o
  Relu                               80      2  Code  Gb  matvecmath.o
  __fixdfsi                          80      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __floatsidf                        78      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  fwrite                             78      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  norflash_init                      78      2  Code  Gb  flash.o
  uart_send_byte                     78      2  Code  Gb  hpm_uart_drv.c.o
  init_py_pins_as_pgpio              76      2  Code  Gb  pinmux.c.o
  __fixsfsi                          74      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_cpu              74      2  Code  Lc  hpm_clock_drv.c.o
  xor_check                          74      2  Code  Gb  protocol.o
  SpiSlaveSend                       72      2  Code  Gb  spi.o
  __floatunsidf                      72      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  pcfg_dcdc_set_voltage              72      2  Code  Gb  hpm_pcfg_drv.c.o
  __extendsfdf2                      70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gedf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ledf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __ltdf2                            70      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  spi_read_command                   70      2  Code  Gb  hpm_spi_drv.c.o
  Drv_FlashWrite                     68      2  Code  Gb  FirmwareUpdateFile.o
  board_init                         68      2  Code  Gb  board.c.o
  ldexpf                             68      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ldexpf.localalias                  68      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  puts                               68      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  spi_master_get_default_format_config
                                     68      2  Code  Gb  hpm_spi_drv.c.o
  rom_xpi_nor_program                66      2  Code  Lc  flash.o
  spi_write_command                  66      2  Code  Gb  hpm_spi_drv.c.o
  board_init_spi_clock               64      2  Code  Gb  board.c.o
  spi_wait_for_idle_status           64      2  Code  Gb  hpm_spi_drv.c.o
  sysctl_resource_target_set_mode
                                     64      2  Code  Lc  board.c.o
  uart_flush                         64      2  Code  Gb  hpm_uart_drv.c.o
  __gesf2                            62      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  clock_add_to_group                 62      2  Code  Gb  hpm_clock_drv.c.o
  clock_remove_from_group            62      2  Code  Gb  hpm_clock_drv.c.o
  spi_no_data                        62      2  Code  Lc  hpm_spi_drv.c.o
  Drv_FlashErase                     60      2  Code  Gb  FirmwareUpdateFile.o
  rom_xpi_nor_read                   60      2  Code  Lc  flash.o
  spi_slave_get_default_format_config
                                     60      2  Code  Gb  hpm_spi_drv.c.o
  uart_modem_config                  60      2  Code  Lc  hpm_uart_drv.c.o
  Check_16bit                        58      2  Code  Gb  protocol.o
  Check_8bit                         58      2  Code  Gb  protocol.o
  __ltsf2                            58      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  get_frequency_for_ewdg             58      2  Code  Lc  hpm_clock_drv.c.o
  norflash_read                      58      2  Code  Gb  flash.o
  norflash_write                     58      2  Code  Gb  flash.o
  rom_xpi_nor_erase_sector           58      2  Code  Lc  flash.o
  spi_get_rx_fifo_valid_data_size
                                     58      2  Code  Lc  hpm_spi_drv.c.o
  spi_slave_get_default_control_config
                                     58      2  Code  Gb  hpm_spi_drv.c.o
  Bind_Init                          56      2  Code  Gb  navi.o
  crc_verify_8bit                    56      2  Code  Lc  SetParaBao.o
  __lesf2                            54      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  l1c_dc_enable                      54      2  Code  Gb  hpm_l1c_drv.c.o
  clock_check_in_group               52      2  Code  Gb  hpm_clock_drv.c.o
  spi_write_address                  52      2  Code  Gb  hpm_spi_drv.c.o
  __fixunsdfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __fixunssfsi                       50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __gtsf2                            50      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  norflash_erase_sector              50      2  Code  Gb  flash.o
  __SEGGER_RTL_print_padding
                                     48      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  get_frequency_for_ahb              48      2  Code  Lc  hpm_clock_drv.c.o
  l1c_ic_enable                      46      2  Code  Gb  hpm_l1c_drv.c.o
  printf                             46      2  Code  Wk  prinops.o (libc_rv32imac_balanced.a)
  sysctl_clock_target_is_busy
                                     46      2  Code  Lc  hpm_sysctl_drv.c.o
  vfprintf                           46      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_isctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_iswctype
                                     44      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __eqsf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __nesf2                            44      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __trunctfsf2                       44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  enable_plic_feature                44      2  Code  Gb  system.c.o
  exception_handler                  44      2  Code  Wk  trap.c.o
  frexpf                             44      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  gptmr_start_counter                44      2  Code  Lc  Timer.o
  __SEGGER_RTL_ascii_mbtowc          42      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  fputc                              42      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  sysctl_clock_set_preset            42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  board.c.o
  sysctl_resource_target_is_busy
                                     42      2  Code  Lc  hpm_sysctl_drv.c.o
  clock_connect_group_to_cpu
                                     40      2  Code  Gb  hpm_clock_drv.c.o
  get_frequency_for_pewdg            40      2  Code  Lc  hpm_clock_drv.c.o
  rom_xpi_nor_auto_config            40      2  Code  Lc  flash.o
  __SEGGER_RTL_init_prin_l           38      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __ashldi3                          38      2  Code  Gb  intasmops_rv.o (libc_rv32imac_balanced.a)
  pllctlv2_xtal_set_rampup_time
                                     38      2  Code  Lc  board.c.o
  sysctl_resource_target_get_mode
                                     38      2  Code  Lc  board.c.o
  __SEGGER_RTL_SquareHi_U64          36      2  Code  Lc  floatops.o (libc_rv32imac_balanced.a)
  clock_update_core_clock            36      2  Code  Gb  hpm_clock_drv.c.o
  gptmr_check_status                 36      2  Code  Lc  Timer.o
  gptmr_check_status                 36      2  Code  Lc  board.c.o
  __SEGGER_RTL_prin_flush            34      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  board_init_uart                    34      2  Code  Gb  board.c.o
  spi_get_data_length_in_bytes
                                     34      2  Code  Lc  hpm_spi_drv.c.o
  sysctl_cpu_clock_any_is_busy
                                     34      2  Code  Lc  hpm_sysctl_drv.c.o
  uart_check_status                  34      2  Code  Lc  Uart_Irq.o
  Drv_FlashRead                      32      2  Code  Gb  FirmwareUpdateFile.o
  reset_handler                      32      2  Code  Wk  reset.c.o
  spi_get_data_length_in_bits
                                     32      2  Code  Lc  hpm_spi_drv.c.o
  usb_phy_disable_dp_dm_pulldown
                                     32      2  Code  Lc  board.c.o
  __SEGGER_RTL_pre_padding           30      2  Code  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_copy                 28      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_enable_irq                   28      2  Code  Lc  Timer.o
  pllctlv2_xtal_is_enabled           28      2  Code  Lc  board.c.o
  pllctlv2_xtal_is_stable            28      2  Code  Lc  board.c.o
  sysctl_resource_any_is_busy
                                     28      2  Code  Lc  board.c.o
  uart_disable_irq                   28      2  Code  Lc  Uart_Irq.o
  uart_disable_irq                   28      2  Code  Lc  hpm_uart_drv.c.o
  Drv_SystemReset                    26      2  Code  Gb  FirmwareUpdateFile.o
  __SEGGER_init_heap                 26      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  spi_master_get_default_timing_config
                                     26      2  Code  Gb  hpm_spi_drv.c.o
  board_delay_ms                     24      2  Code  Gb  board.c.o
  board_init_spi_pins                24      2  Code  Gb  board.c.o
  l1c_dc_invalidate_all              24      2  Code  Gb  hpm_l1c_drv.c.o
  uart_enable_irq                    24      2  Code  Lc  Uart_Irq.o
  uart_enable_irq                    24      2  Code  Lc  hpm_uart_drv.c.o
  uart_get_irq_id                    24      2  Code  Lc  Uart_Irq.o
  uart_write_byte                    24      2  Code  Lc  Uart_Irq.o
  __SEGGER_RTL_init_heap             22      2  Code  Wk  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  AlgorithmDo                        20      2  Code  Gb  arithmetic.o
  __SEGGER_RTL_ascii_wctomb          20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_current_locale
                                     20      2  Code  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_init_zero                 20      2  Code  Wk  SEGGER_RV32_crtinit.o (SEGGER_RV32_crtinit_rv32imac_balanced.a)
  gptmr_clear_status                 20      2  Code  Lc  Timer.o
  gptmr_clear_status                 20      2  Code  Lc  board.c.o
  __SEGGER_RTL_float32_isnan
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isnormal
                                     18      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  ppor_sw_reset                      18      2  Code  Lc  FirmwareUpdateFile.o
  syscall_handler                    18      2  Code  Wk  trap.c.o
  uart_read_byte                     18      2  Code  Lc  Uart_Irq.o
  GetZUPTFlag                        16      2  Code  Gb  ZUPT.o
  __SEGGER_RTL_ascii_tolower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towlower
                                     16      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stream_write          16      2  Code  Lc  prinops.o (libc_rv32imac_balanced.a)
  putchar                            16      2  Code  Wk  fileops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_toupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_towupper
                                     14      2  Code  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float32_isinf
                                     14      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __subdf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __subsf3                           14      2  Code  Gb  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_X_file_bufsize
                                     12      2  Code  Gb  hpm_debug_console.c.o
  __SEGGER_RTL_X_file_stat           12      2  Code  Gb  hpm_debug_console.c.o
  abs                                10      2  Code  Wk  intops.o (libc_rv32imac_balanced.a)
  asin                               10      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  cos                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  sin                                 8      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float32_signbit
                                      4      2  Code  Wk  floatops.o (libc_rv32imac_balanced.a)
  _init                               4      2  Code  Wk  reset.c.o
  board_init_pmp                      4      2  Code  Gb  board.c.o
  mchtmr_isr                          4      2  Code  Wk  trap.c.o
  swi_isr                             4      2  Code  Wk  trap.c.o
  exit                                2      2  Code  Gb  startup.s.o
  __SEGGER_init_done                         2  Code  Gb  startup.s.o
  start                                      2  Code  Gb  startup.s.o

Read-write data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  Acc                        0x00089208  gp+0x0660          24      8  Zero  Lc  arithmetic.o
  ImuData                    0x0008AEE8                     28      4  Zero  Gb  main.o
  InavOutData                0x0008AE5C                     80      4  Zero  Gb  arithmetic.o
  LastAcc                    0x000891F0  gp+0x0648          24      8  Zero  Lc  arithmetic.o
  TimeStamp                  0x0008AF40                      4      4  Zero  Gb  main.o
  __RAL_global_locale        0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                             0x00080000                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_heap_globals  0x0008AF48                      4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr    0x0008AF44                      4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file   0x0008AF3C                      4      4  Zero  Lc  hpm_debug_console.c.o
  acc_sum                    0x000891D8  gp+0x0630          24      8  Zero  Lc  arithmetic.o
  bias_estimate_done         0x0008AF38                      4      4  Zero  Lc  arithmetic.o
  bias_sample_count          0x0008AF34                      4      4  Zero  Lc  arithmetic.o
  combineData                0x0008AEAC                     32      4  Zero  Gb  arithmetic.o
  control_Slave_config       0x0008AF10                     12      4  Zero  Gb  spi.o
  control_config             0x0008AF04                     12      4  Zero  Gb  spi.o
  estimated_acc_bias         0x000891C0  gp+0x0618          24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias        0x000891A8  gp+0x0600          24      8  Zero  Lc  arithmetic.o
  flag.2                     0x0008AF30                      4      4  Zero  Lc  SetParaBao.o
  fpga_syn                   0x0008AF52                      1         Zero  Gb  main.o
  g_AccANNCompen             0x00087CA0                  1 800      8  Zero  Gb  main.o
  g_Align                    0x00088FD8  gp+0x0430         184      8  Zero  Gb  main.o
  g_CmdFullTempCompenData    0x00086E18                  1 920      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData  0x00088E78  gp+0x02D0         352      8  Zero  Gb  main.o
  g_Compen                   0x00083B30                 10 200      8  Zero  Gb  main.o
  g_GyroANNCompen            0x00087598                  1 800      8  Zero  Gb  main.o
  g_InertialSysAlign         0x00088978  gp-0x0230         832      8  Zero  Gb  main.o
  g_InitBind                 0x00089110  gp+0x0568          80      8  Zero  Gb  main.o
  g_Kalman                   0x00080018                 15 128      8  Zero  Gb  main.o
  g_Navi                     0x00086308                  2 832      8  Zero  Gb  main.o
  g_SelfTest                 0x00089090  gp+0x04E8         128      8  Zero  Gb  main.o
  g_StartUpdateFirm          0x0008AF51                      1         Zero  Gb  SetParaBao.o
  g_SysVar                   0x00088CB8  gp+0x0110         448      8  Zero  Gb  main.o
  g_UpdateBackFlag           0x0008AF64                      1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful         0x0008AF50                      1         Zero  Gb  SetParaBao.o
  g_ZUPT                     0x000883A8  gp-0x0800       1 488      8  Zero  Gb  main.o
  g_console_uart             0x0008AF2C                      4      4  Zero  Lc  hpm_debug_console.c.o
  g_ucSystemResetFlag        0x0008AF4F                      1         Zero  Gb  SetParaBao.o
  gbtxcompleted              0x0008AF60                      4      4  Init  Gb  Uart_Irq.o
  gframeParsebuf             0x0008A95C                  1 024      4  Zero  Gb  Uart_Irq.o
  grxbuffer                  0x0008AF68                  4 096      4  None  Gb  Uart_Irq.o
  grxlen                     0x0008AF28                      4      4  Zero  Gb  Uart_Irq.o
  grxst                      0x0008AF24                      4      4  Zero  Gb  Uart_Irq.o
  gyro_sum                   0x00089190  gp+0x05E8          24      8  Zero  Lc  arithmetic.o
  hpm_core_clock             0x0008AF20                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send           0x0008AF5C                      4      4  Init  Gb  Uart_Irq.o
  r_Gyro                     0x00089178  gp+0x05D0          24      8  Zero  Lc  arithmetic.o
  r_LastGyro                 0x00089160  gp+0x05B8          24      8  Zero  Lc  arithmetic.o
  s_xpi_nor_config           0x0008AD5C                    256      4  Zero  Lc  flash.o
  stSetPara                  0x00089220  gp+0x0678       5 946      4  Zero  Gb  SetParaBao.o
  stSmi240Data               0x0008AECC                     28      4  Zero  Gb  protocol.o
  stdout                     0x0008AF58                      4      4  Init  Gb  hpm_debug_console.c.o
  tCnt.0                     0x0008AF4C                      2      2  Zero  Lc  main.o
  timer_cb                   0x0008AF1C                      4      4  Zero  Lc  board.c.o
  tx_buffer                  0x0008BF68                  4 096      4  None  Gb  Uart_Irq.o
  tx_counter                 0x0008A95A                      2      2  Zero  Gb  Uart_Irq.o
  uart_rx_dma_done           0x0008AF4E                      1         Zero  Gb  uart_dma.o
  uart_tx_dma_done           0x0008AF53                      1         Init  Gb  uart_dma.o
  uiLastBaoInDex.3           0x0008AF54                      4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1             0x00080014                      4      4  Zero  Lc  SetParaBao.o

Read-write data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00080000             __SEGGER_RTL_global_locale
                                                            20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080000             __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x00080014             uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  0x00080018             g_Kalman                       15 128      8  Zero  Gb  main.o
  0x00083B30             g_Compen                       10 200      8  Zero  Gb  main.o
  0x00086308             g_Navi                          2 832      8  Zero  Gb  main.o
  0x00086E18             g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  0x00087598             g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  0x00087CA0             g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  0x000883A8  gp-0x0800  g_ZUPT                          1 488      8  Zero  Gb  main.o
  0x00088978  gp-0x0230  g_InertialSysAlign                832      8  Zero  Gb  main.o
  0x00088CB8  gp+0x0110  g_SysVar                          448      8  Zero  Gb  main.o
  0x00088E78  gp+0x02D0  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  0x00088FD8  gp+0x0430  g_Align                           184      8  Zero  Gb  main.o
  0x00089090  gp+0x04E8  g_SelfTest                        128      8  Zero  Gb  main.o
  0x00089110  gp+0x0568  g_InitBind                         80      8  Zero  Gb  main.o
  0x00089160  gp+0x05B8  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  0x00089178  gp+0x05D0  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  0x00089190  gp+0x05E8  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  0x000891A8  gp+0x0600  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  0x000891C0  gp+0x0618  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  0x000891D8  gp+0x0630  acc_sum                            24      8  Zero  Lc  arithmetic.o
  0x000891F0  gp+0x0648  LastAcc                            24      8  Zero  Lc  arithmetic.o
  0x00089208  gp+0x0660  Acc                                24      8  Zero  Lc  arithmetic.o
  0x00089220  gp+0x0678  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  0x0008A95A             tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  0x0008A95C             gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  0x0008AD5C             s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  0x0008AE5C             InavOutData                        80      4  Zero  Gb  arithmetic.o
  0x0008AEAC             combineData                        32      4  Zero  Gb  arithmetic.o
  0x0008AECC             stSmi240Data                       28      4  Zero  Gb  protocol.o
  0x0008AEE8             ImuData                            28      4  Zero  Gb  main.o
  0x0008AF04             control_config                     12      4  Zero  Gb  spi.o
  0x0008AF10             control_Slave_config               12      4  Zero  Gb  spi.o
  0x0008AF1C             timer_cb                            4      4  Zero  Lc  board.c.o
  0x0008AF20             hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  0x0008AF24             grxst                               4      4  Zero  Gb  Uart_Irq.o
  0x0008AF28             grxlen                              4      4  Zero  Gb  Uart_Irq.o
  0x0008AF2C             g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF30             flag.2                              4      4  Zero  Lc  SetParaBao.o
  0x0008AF34             bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  0x0008AF38             bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  0x0008AF3C             __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  0x0008AF40             TimeStamp                           4      4  Zero  Gb  main.o
  0x0008AF44             __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x0008AF48             __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  0x0008AF4C             tCnt.0                              2      2  Zero  Lc  main.o
  0x0008AF4E             uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  0x0008AF4F             g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  0x0008AF50             g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  0x0008AF51             g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  0x0008AF52             fpga_syn                            1         Zero  Gb  main.o
  0x0008AF53             uart_tx_dma_done                    1         Init  Gb  uart_dma.o
  0x0008AF54             uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  0x0008AF58             stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  0x0008AF5C             nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  0x0008AF60             gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  0x0008AF64             g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  0x0008AF68             grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  0x0008BF68             tx_buffer                       4 096      4  None  Gb  Uart_Irq.o

Read-write data symbols by descending size:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
  Symbol name                      Size  Align  Type  Bd  Object File
  -------------------------  ----------  -----  ----  --  -----------
  g_Kalman                       15 128      8  Zero  Gb  main.o
  g_Compen                       10 200      8  Zero  Gb  main.o
  stSetPara                       5 946      4  Zero  Gb  SetParaBao.o
  grxbuffer                       4 096      4  None  Gb  Uart_Irq.o
  tx_buffer                       4 096      4  None  Gb  Uart_Irq.o
  g_Navi                          2 832      8  Zero  Gb  main.o
  g_CmdFullTempCompenData         1 920      8  Zero  Gb  main.o
  g_AccANNCompen                  1 800      8  Zero  Gb  main.o
  g_GyroANNCompen                 1 800      8  Zero  Gb  main.o
  g_ZUPT                          1 488      8  Zero  Gb  main.o
  gframeParsebuf                  1 024      4  Zero  Gb  Uart_Irq.o
  g_InertialSysAlign                832      8  Zero  Gb  main.o
  g_SysVar                          448      8  Zero  Gb  main.o
  g_CmdNormalTempCompenData         352      8  Zero  Gb  main.o
  s_xpi_nor_config                  256      4  Zero  Lc  flash.o
  g_Align                           184      8  Zero  Gb  main.o
  g_SelfTest                        128      8  Zero  Gb  main.o
  InavOutData                        80      4  Zero  Gb  arithmetic.o
  g_InitBind                         80      8  Zero  Gb  main.o
  combineData                        32      4  Zero  Gb  arithmetic.o
  ImuData                            28      4  Zero  Gb  main.o
  stSmi240Data                       28      4  Zero  Gb  protocol.o
  Acc                                24      8  Zero  Lc  arithmetic.o
  LastAcc                            24      8  Zero  Lc  arithmetic.o
  acc_sum                            24      8  Zero  Lc  arithmetic.o
  estimated_acc_bias                 24      8  Zero  Lc  arithmetic.o
  estimated_gyro_bias                24      8  Zero  Lc  arithmetic.o
  gyro_sum                           24      8  Zero  Lc  arithmetic.o
  r_Gyro                             24      8  Zero  Lc  arithmetic.o
  r_LastGyro                         24      8  Zero  Lc  arithmetic.o
  __RAL_global_locale                20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_global_locale
                                     20      4  Init  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  control_Slave_config               12      4  Zero  Gb  spi.o
  control_config                     12      4  Zero  Gb  spi.o
  TimeStamp                           4      4  Zero  Gb  main.o
  __SEGGER_RTL_heap_globals           4      4  Zero  Lc  heapops_basic.o (heapops_basic_rv32imac_balanced.a)
  __SEGGER_RTL_locale_ptr             4      4  Zero  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_stdout_file            4      4  Zero  Lc  hpm_debug_console.c.o
  bias_estimate_done                  4      4  Zero  Lc  arithmetic.o
  bias_sample_count                   4      4  Zero  Lc  arithmetic.o
  flag.2                              4      4  Zero  Lc  SetParaBao.o
  g_console_uart                      4      4  Zero  Lc  hpm_debug_console.c.o
  gbtxcompleted                       4      4  Init  Gb  Uart_Irq.o
  grxlen                              4      4  Zero  Gb  Uart_Irq.o
  grxst                               4      4  Zero  Gb  Uart_Irq.o
  hpm_core_clock                      4      4  Zero  Gb  hpm_clock_drv.c.o
  nbr_data_to_send                    4      4  Init  Gb  Uart_Irq.o
  stdout                              4      4  Init  Gb  hpm_debug_console.c.o
  timer_cb                            4      4  Zero  Lc  board.c.o
  uiLastBaoInDex.3                    4      4  Init  Lc  SetParaBao.o
  uiOffsetAddr.1                      4      4  Zero  Lc  SetParaBao.o
  tCnt.0                              2      2  Zero  Lc  main.o
  tx_counter                          2      2  Zero  Gb  Uart_Irq.o
  fpga_syn                            1         Zero  Gb  main.o
  g_StartUpdateFirm                   1         Zero  Gb  SetParaBao.o
  g_UpdateBackFlag                    1         Init  Gb  SetParaBao.o
  g_UpdateSuccessful                  1         Zero  Gb  SetParaBao.o
  g_ucSystemResetFlag                 1         Zero  Gb  SetParaBao.o
  uart_rx_dma_done                    1         Zero  Gb  uart_dma.o
  uart_tx_dma_done                    1         Init  Gb  uart_dma.o

Read-only data symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
  Symbol name                   Address     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __SEGGER_RTL_Moeller_inverse_lut
                             0x80010F94  tp+0x0706       1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aPower2f      0x80011394                     24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_aSqrtData     0x80010E14  tp+0x0586         384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_map
                             0x80011548                    128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_ascii_ctype_mask
                             0x80012860                     13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale      0x800114C4                     12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_day_names
                             0x80012794                     29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_abbrev_month_names
                             0x800126D8                     49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_am_pm_indicator
                             0x800131E0                      7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_data
                             0x800114D0                     88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_format
                             0x80012A74                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_date_time_format
                             0x800131D0                     15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_day_names
                             0x80011BE0                     58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_month_names
                             0x800131E8                     87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_c_locale_time_format
                             0x80012998                      9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_codeset_ascii
                             0x80011528                     32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_empty_string
                             0x80012690                      1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_data_utf8_period
                             0x80011B44                      2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ASinACos
                             0x80010560  tp-0x032E         112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_ATan  0x800104C8  tp-0x03C6          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_SinCos
                             0x800105D0  tp-0x02BE          64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_float64_Tan   0x80010528  tp-0x0366          56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_lc        0x800113AC                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_hex_uc        0x800113BC                     16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_RTL_ipow10        0x80010610  tp-0x027E         160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  __SEGGER_init_data__       0x80034CF0                [1 736]      4  Cnst  Lc  [ Linker created ]
  __SEGGER_init_table__      0x80034C9C                   [84]      4  Cnst  Lc  [ Linker created ]
  fw_info                    0x8000E010                    128      4  Cnst  Gb  hpm_bootheader.c.o
  header                     0x8000E000                     16      4  Cnst  Gb  hpm_bootheader.c.o
  option                     0x8000D000                     16      4  Cnst  Gb  board.c.o
  s_adc_clk_mux_node         0x80011740                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_dac_clk_mux_node         0x80011818                      2      4  Cnst  Lc  hpm_clock_drv.c.o
  s_wdgs                     0x80010C8C  tp+0x03FE           8      4  Cnst  Lc  hpm_clock_drv.c.o

Read-only data symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
     Address     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x8000D000             option                             16      4  Cnst  Gb  board.c.o
  0x8000E000             header                             16      4  Cnst  Gb  hpm_bootheader.c.o
  0x8000E010             fw_info                           128      4  Cnst  Gb  hpm_bootheader.c.o
  0x800104C8  tp-0x03C6  __SEGGER_RTL_float64_ATan          96      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010528  tp-0x0366  __SEGGER_RTL_float64_Tan           56      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010560  tp-0x032E  __SEGGER_RTL_float64_ASinACos
                                                           112      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x800105D0  tp-0x02BE  __SEGGER_RTL_float64_SinCos
                                                            64      8  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010610  tp-0x027E  __SEGGER_RTL_ipow10               160      8  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x80010C8C  tp+0x03FE  s_wdgs                              8      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80010E14  tp+0x0586  __SEGGER_RTL_aSqrtData            384      4  Cnst  Lc  floatops.o (libc_rv32imac_balanced.a)
  0x80010F94  tp+0x0706  __SEGGER_RTL_Moeller_inverse_lut
                                                         1 024      4  Cnst  Lc  intops.o (libc_rv32imac_balanced.a)
  0x80011394             __SEGGER_RTL_aPower2f              24      4  Cnst  Lc  utilops.o (libc_rv32imac_balanced.a)
  0x800113AC             __SEGGER_RTL_hex_lc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x800113BC             __SEGGER_RTL_hex_uc                16      4  Cnst  Gb  prinops.o (libc_rv32imac_balanced.a)
  0x800114C4             __SEGGER_RTL_c_locale              12      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800114D0             __SEGGER_RTL_c_locale_data
                                                            88      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011528             __SEGGER_RTL_codeset_ascii
                                                            32      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011548             __SEGGER_RTL_ascii_ctype_map
                                                           128      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011740             s_adc_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80011818             s_dac_clk_mux_node                  2      4  Cnst  Lc  hpm_clock_drv.c.o
  0x80011B44             __SEGGER_RTL_data_utf8_period
                                                             2      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80011BE0             __SEGGER_RTL_c_locale_day_names
                                                            58      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012690             __SEGGER_RTL_data_empty_string
                                                             1      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800126D8             __SEGGER_RTL_c_locale_abbrev_month_names
                                                            49      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012794             __SEGGER_RTL_c_locale_abbrev_day_names
                                                            29      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012860             __SEGGER_RTL_ascii_ctype_mask
                                                            13      4  Cnst  Lc  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012998             __SEGGER_RTL_c_locale_time_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80012A74             __SEGGER_RTL_c_locale_date_format
                                                             9      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800131D0             __SEGGER_RTL_c_locale_date_time_format
                                                            15      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800131E0             __SEGGER_RTL_c_locale_am_pm_indicator
                                                             7      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x800131E8             __SEGGER_RTL_c_locale_month_names
                                                            87      4  Cnst  Gb  mbops.o (mbops_timeops_rv32imac_balanced.a)
  0x80034C9C             __SEGGER_init_table__            [84]      4  Cnst  Lc  [ Linker created ]
  0x80034CF0             __SEGGER_init_data__          [1 736]      4  Cnst  Lc  [ Linker created ]

Untyped symbols by name:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
  Symbol name                     Value     Access        Size  Align  Type  Bd  Object File
  -------------------------  ----------  ---------  ----------  -----  ----  --  -----------
  __AHB_SRAM_segment_end__   0xF0408000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_size__  0x00008000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_end__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_size__
                             0x00000000                                ----  Gb  [ Linker created ]
  __AHB_SRAM_segment_used_start__
                             0xF0400000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_end__
                             0x80010000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_size__
                             0x00002000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_end__
                             0x8000E090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_size__
                             0x00000090                                ----  Gb  [ Linker created ]
  __BOOT_HEADER_segment_used_start__
                             0x8000E000                                ----  Gb  [ Linker created ]
  __DLM_segment_end__        0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_start__      0x00080000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_end__   0x000A0000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_size__  0x00020000                                ----  Gb  [ Linker created ]
  __DLM_segment_used_start__
                             0x00080000                                ----  Gb  [ Linker created ]
  __HEAPSIZE__               0x00004000                                ----  Gb  [ Linker created ]
  __ILM_segment_end__        0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_size__       0x00020000                                ----  Gb  [ Linker created ]
  __ILM_segment_start__      0x00000000                                ----  Gb  [ Linker created ]
  __ILM_segment_used_end__   0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_size__  0x000006A2                                ----  Gb  [ Linker created ]
  __ILM_segment_used_start__
                             0x00000000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_end__
                             0x8000DC00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_size__
                             0x00000C00                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_end__
                             0x8000D010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_size__
                             0x00000010                                ----  Gb  [ Linker created ]
  __NOR_CFG_OPTION_segment_used_start__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __SEGGER_RTL_fdiv_reciprocal_table
                             0x80010D14  tp+0x0486         256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  __STACKSIZE__              0x00004000                                ----  Gb  [ Linker created ]
  __XPI0_segment_end__       0x80100000                                ----  Gb  [ Linker created ]
  __XPI0_segment_size__      0x000F0000                                ----  Gb  [ Linker created ]
  __XPI0_segment_start__     0x80010000                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_end__  0x800353E8                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_size__
                             0x000253E8                                ----  Gb  [ Linker created ]
  __XPI0_segment_used_start__
                             0x80010000                                ----  Gb  [ Linker created ]
  __app_load_addr__          0x80010000                                ----  Gb  [ Linker created ]
  __app_offset__             0x00002000                                ----  Gb  [ Linker created ]
  __boot_header_length__     0x00000090                                ----  Gb  [ Linker created ]
  __boot_header_load_addr__  0x8000E000                                ----  Gb  [ Linker created ]
  __fsymtab_end              0x80010066                                ----  Gb  [ Linker created ]
  __fsymtab_start            0x80010066                                ----  Gb  [ Linker created ]
  __fw_size__                0x00001000                                ----  Gb  [ Linker created ]
  __global_pointer$          0x00088BA8  gp+0x0000                     ----  Gb  [ Linker created ]
  __heap_end__               0x00090F68                                ----  Gb  [ Linker created ]
  __heap_start__             0x0008CF68                                ----  Gb  [ Linker created ]
  __nor_cfg_option_load_addr__
                             0x8000D000                                ----  Gb  [ Linker created ]
  __rt_init_end              0x80010066                                ----  Gb  [ Linker created ]
  __rt_init_start            0x80010066                                ----  Gb  [ Linker created ]
  __rtmsymtab_end            0x80010066                                ----  Gb  [ Linker created ]
  __rtmsymtab_start          0x80010066                                ----  Gb  [ Linker created ]
  __stack_end__              0x000A0000                                ----  Gb  [ Linker created ]
  __startup_complete         0x80010054                             2  Code  Lc  startup.s.o
  __thread_pointer$          0x8001088E  tp+0x0000                     ----  Gb  [ Linker created ]
  __usbh_class_info_end__    0x00080000                                ----  Gb  [ Linker created ]
  __usbh_class_info_start__  0x00080000                                ----  Gb  [ Linker created ]
  __vector_table             0x00000000                  [292]    512  Init  Gb  startup.s.o
  __vsymtab_end              0x80010066                                ----  Gb  [ Linker created ]
  __vsymtab_start            0x80010066                                ----  Gb  [ Linker created ]
  _flash_size                0x00100000                                ----  Gb  [ Linker created ]
  _stack                     0x000A0000                                ----  Gb  [ Linker created ]
  _stack_safe                0x000A0000                                ----  Gb  [ Linker created ]
  default_irq_handler        0x00000562                             4  Init  Wk  startup.s.o
  default_isr_1              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_10             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_11             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_12             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_13             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_14             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_15             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_16             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_17             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_18             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_19             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_2              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_20             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_21             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_22             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_23             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_24             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_25             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_26             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_27             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_28             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_29             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_3              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_30             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_31             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_32             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_33             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_34             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_35             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_36             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_37             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_38             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_39             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_4              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_40             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_41             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_42             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_43             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_44             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_45             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_46             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_47             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_48             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_49             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_5              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_50             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_51             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_52             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_53             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_54             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_55             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_56             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_57             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_58             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_59             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_6              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_60             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_61             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_62             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_63             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_64             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_65             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_66             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_67             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_68             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_69             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_7              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_70             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_71             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_72             0x00000562                             4  Init  Wk  startup.s.o
  default_isr_8              0x00000562                             4  Init  Wk  startup.s.o
  default_isr_9              0x00000562                             4  Init  Wk  startup.s.o
  nmi_handler                0x00000560                    [6]      4  Init  Wk  startup.s.o

Untyped symbols by address:

  Global base (gp) at 0x00088BA8
  Thread base (tp) at 0x8001088E
  
       Value     Access  Symbol name                      Size  Align  Type  Bd  Object File
  ----------  ---------  -------------------------  ----------  -----  ----  --  -----------
  0x00000000             __vector_table                  [292]    512  Init  Gb  startup.s.o
  0x00000000             __ILM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00000000             __ILM_segment_start__                         ----  Gb  [ Linker created ]
  0x00000000             __AHB_SRAM_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000010             __NOR_CFG_OPTION_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000090             __boot_header_length__                        ----  Gb  [ Linker created ]
  0x00000090             __BOOT_HEADER_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00000560             nmi_handler                       [6]      4  Init  Wk  startup.s.o
  0x00000562             default_isr_9                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_8                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_72                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_71                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_70                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_7                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_69                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_68                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_67                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_66                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_65                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_64                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_63                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_62                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_61                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_60                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_6                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_59                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_58                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_57                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_56                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_55                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_54                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_53                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_52                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_51                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_50                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_5                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_49                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_48                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_47                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_46                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_45                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_44                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_43                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_42                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_41                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_40                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_4                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_39                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_38                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_37                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_36                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_35                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_34                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_33                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_32                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_31                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_30                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_3                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_29                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_28                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_27                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_26                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_25                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_24                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_23                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_22                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_21                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_20                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_2                              4  Init  Wk  startup.s.o
  0x00000562             default_isr_19                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_18                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_17                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_16                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_15                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_14                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_13                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_12                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_11                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_10                             4  Init  Wk  startup.s.o
  0x00000562             default_isr_1                              4  Init  Wk  startup.s.o
  0x00000562             default_irq_handler                        4  Init  Wk  startup.s.o
  0x000006A2             __ILM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x000006A2             __ILM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x00000C00             __NOR_CFG_OPTION_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00001000             __fw_size__                                   ----  Gb  [ Linker created ]
  0x00002000             __app_offset__                                ----  Gb  [ Linker created ]
  0x00002000             __BOOT_HEADER_segment_size__
                                                                       ----  Gb  [ Linker created ]
  0x00004000             __STACKSIZE__                                 ----  Gb  [ Linker created ]
  0x00004000             __HEAPSIZE__                                  ----  Gb  [ Linker created ]
  0x00008000             __AHB_SRAM_segment_size__                     ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_size__                          ----  Gb  [ Linker created ]
  0x00020000             __ILM_segment_end__                           ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_used_size__                     ----  Gb  [ Linker created ]
  0x00020000             __DLM_segment_size__                          ----  Gb  [ Linker created ]
  0x000253E8             __XPI0_segment_used_size__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_start__                     ----  Gb  [ Linker created ]
  0x00080000             __usbh_class_info_end__                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x00080000             __DLM_segment_start__                         ----  Gb  [ Linker created ]
  0x00088BA8  gp+0x0000  __global_pointer$                             ----  Gb  [ Linker created ]
  0x0008CF68             __heap_start__                                ----  Gb  [ Linker created ]
  0x00090F68             __heap_end__                                  ----  Gb  [ Linker created ]
  0x000A0000             _stack_safe                                   ----  Gb  [ Linker created ]
  0x000A0000             _stack                                        ----  Gb  [ Linker created ]
  0x000A0000             __stack_end__                                 ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_used_end__                      ----  Gb  [ Linker created ]
  0x000A0000             __DLM_segment_end__                           ----  Gb  [ Linker created ]
  0x000F0000             __XPI0_segment_size__                         ----  Gb  [ Linker created ]
  0x00100000             _flash_size                                   ----  Gb  [ Linker created ]
  0x8000D000             __nor_cfg_option_load_addr__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D000             __NOR_CFG_OPTION_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000D010             __NOR_CFG_OPTION_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000DC00             __NOR_CFG_OPTION_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __boot_header_load_addr__                     ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E000             __BOOT_HEADER_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0x8000E090             __BOOT_HEADER_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __app_load_addr__                             ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0x80010000             __XPI0_segment_start__                        ----  Gb  [ Linker created ]
  0x80010000             __BOOT_HEADER_segment_end__
                                                                       ----  Gb  [ Linker created ]
  0x80010054             __startup_complete                         2  Code  Lc  startup.s.o
  0x80010066             __vsymtab_start                               ----  Gb  [ Linker created ]
  0x80010066             __vsymtab_end                                 ----  Gb  [ Linker created ]
  0x80010066             __rtmsymtab_start                             ----  Gb  [ Linker created ]
  0x80010066             __rtmsymtab_end                               ----  Gb  [ Linker created ]
  0x80010066             __rt_init_start                               ----  Gb  [ Linker created ]
  0x80010066             __rt_init_end                                 ----  Gb  [ Linker created ]
  0x80010066             __fsymtab_start                               ----  Gb  [ Linker created ]
  0x80010066             __fsymtab_end                                 ----  Gb  [ Linker created ]
  0x8001088E  tp+0x0000  __thread_pointer$                             ----  Gb  [ Linker created ]
  0x80010D14  tp+0x0486  __SEGGER_RTL_fdiv_reciprocal_table
                                                           256      4  Cnst  Lc  floatasmops_rv.o (libc_rv32imac_balanced.a)
  0x800353E8             __XPI0_segment_used_end__                     ----  Gb  [ Linker created ]
  0x80100000             __XPI0_segment_end__                          ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_used_end__
                                                                       ----  Gb  [ Linker created ]
  0xF0400000             __AHB_SRAM_segment_start__
                                                                       ----  Gb  [ Linker created ]
  0xF0408000             __AHB_SRAM_segment_end__                      ----  Gb  [ Linker created ]


***********************************************************************************************
***                                                                                         ***
***                                      LINK SUMMARY                                       ***
***                                                                                         ***
***********************************************************************************************

Memory breakdown:

  145 194 bytes read-only  code    + 
    9 190 bytes read-only  data    = 154 384 bytes read-only (total)
   85 861 bytes read-write data

Region summary:

  Name        Range                     Size                 Used               Unused       Alignment Loss
  ----------  -----------------  -----------  -------------------  -------------------  -------------------
  ILM         00000000-0001ffff      131 072        1 696   1.29%      129 374  98.70%            2   0.00%
  DLM         00080000-0009ffff      131 072       85 861  65.51%       45 211  34.49%            0   0.00%
  NOR_CFG_OPTION
              8000d000-8000dbff        3 072           16   0.52%        3 056  99.48%            0   0.00%
  BOOT_HEADER
              8000e000-8000ffff        8 192          144   1.76%        8 048  98.24%            0   0.00%
  XPI0        80010000-800fffff      983 040      152 528  15.52%      830 500  84.48%           12   0.00%

Link complete: 0 errors, 0 warnings, 0 remarks
