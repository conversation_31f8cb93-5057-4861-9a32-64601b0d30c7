# Copyright (c) 2021 HPMicro
# SPDX-License-Identifier: BSD-3-Clause
#
# assumptions:
#   - HPM_SDK_BASE has been defined as environment variable pointing to correct hpm_sdk path
#   - current directory is ${HPM_SDK_BASE}/boards/openocd
#
# usage:
# *unix:
# # connect hpm6750evkmini via ft2232, debugging single core
#   $ openocd -c "set HPM_SDK_BASE ${HPM_SDK_BASE}; set BOARD hpm6750evkmini; set PROBE ft2232;" -f hpm6750_all_in_one.cfg
# # connect hpm6750evkmini via ft2232, debugging dual core
#   $ openocd -c "set HPM_SDK_BASE ${HPM_SDK_BASE}; set BOARD hpm6750evkmini; set PROBE ft2232; set ENABLE_DUALCORE 1" -f hpm6750_all_in_one.cfg
#
# Windows DOS batch:
# # connect hpm6750evkmini via ft2232, debugging single core
#   $ openocd -c "set HPM_SDK_BASE %HPM_SDK_BASE:\=/%; set BOARD hpm6750evkmini; set PROBE ft2232;" -f hpm6750_all_in_one.cfg
# # connect hpm6750evkmini via ft2232, debugging dual core
#   $ openocd -c "set HPM_SDK_BASE %HPM_SDK_BASE:\=/%; set BOARD hpm6750evkmini; set PROBE ft2232; set ENABLE_DUALCORE 1" -f hpm6750_all_in_one.cfg
#
# # supported board to be set to BOARD:
#   - hpm6750evk
#   - hpm6750evkmini
# # supported probes to be set to PROBE:
#   - ft2232
#   - ft232
#   - jlink
#   - cmsis_dap
#   - nds_aice_micro
# # supported core debug type to ENABLE_DUALCORE:
#   - 1: enable dual core debugging

set HPM_OPENOCD_CONFIG ${HPM_SDK_BASE}/boards/openocd

if { ![info exists PROBE ] } {
    set PROBE ft2232
}

if { ![info exists BOARD] } {
    set BOARD hpm6750evkmini
}

if { [info exists ENABLE_DUALCORE] && $ENABLE_DUALCORE == 1 } {
    set CORE hpm6750-dual-core
} else {
    set CORE hpm6750-single-core
}

source ${HPM_OPENOCD_CONFIG}/probes/${PROBE}.cfg
source ${HPM_OPENOCD_CONFIG}/soc/${CORE}.cfg
source ${HPM_OPENOCD_CONFIG}/boards/${BOARD}.cfg
