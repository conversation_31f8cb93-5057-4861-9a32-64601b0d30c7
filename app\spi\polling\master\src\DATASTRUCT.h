#ifndef _DATASTRUCT_H
#define _DATASTRUCT_H
/*****************************************************s******************************************************************************/
/*sDATASTRUCT.h                                                                                                                   */
/*  Ver 0.1                                                                                                                        */
/*G/                                                                                                */
/*G                                                                                                                             */
/*sTYPEDEFINE.h                                                                                                                   */
/*iGNSSlocusGen.mssiiI                                                              */
/*sGGGiIssGiIG                           */
/*******************************************************************************************************************************************/
//#include "appmain.h"
#include "CONST.h"
#include "TYPEDEFINE.h"
/*************************************************************************************************************************/
typedef struct s_SelfTest
{
    DPARA LastGyroRaw[3];                       //??????,??:????????

    DPARA LastAccRaw[3];                        //????????,??:????????

    DPARA GyroRaw[3];                       //??????,??:????????

    DPARA AccRaw[3];                        //????????,??:????????

    UINT8 IMU_Valid;

    COUNT GyroRepeat_Count[3]; //

    COUNT AccRepeat_Count[3];//
}SelfTest,*p_SelfTest;





typedef struct s_IMUSmoothAverage
{
    DPARA GyroBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA AccBuffer[IMU_SMOOTH_AVE_NUM][3];

    DPARA GyroSmoothSum[3];

    DPARA AccSmoothSum[3];

    DPARA GyroSmoothMean[3];

    DPARA AccSmoothMean[3];

    DPARA GyroSmoothSum_200Hz[3];

    DPARA AccSmoothSum_200Hz[3];

    DPARA GyroSmoothMean_200Hz[3];

    DPARA AccSmoothMean_200Hz[3];

    DPARA GyroSmoothSum_100Hz[3];

    DPARA AccSmoothSum_100Hz[3];

    DPARA GyroSmoothMean_100Hz[3];

    DPARA AccSmoothMean_100Hz[3];

    DPARA Temp;

    COUNT Smooth_Count;

    COUNT Smooth_200Hz_Count;

    COUNT Smooth_100Hz_Count;

    BOOL isSmoothBufferFull;

    DPARA r_VG_Pitch;
    DPARA r_VG_Roll;
    ACCELER Total_Acc;
    TIME SmoothTime;
    DPARA Cnb[9];
    DPARA Q[4];
    BOOL isVGInit;
    DPARA Err_I[3];
}IMUSmoothAverage,*p_IMUSmoothAverage;




/*************************************************************************************************************************/
typedef struct s_SysVar
{
	PHASE WorkPhase;           	//CONST.h
	
	TIME Time;                 	//CONST.hs
	
	TIME Time_INS_Alone;  		//
	
	TIME Time_Integrated_Navi;	//
	
	TIME Time_Since_Last_GNSS;
		
	LEN Arm_GNSSToINS_b[3];		//bGNSS
	
	LEN Arm_GNSSToINS_n[3];		//nGNSS
	
	LEN Arm_INSToCenter_b[3];	//b
	
	LEN Arm_INSToCenter_n[3];	//n
		
	BOOL isGNSS_Update;			//
	
	BOOL isGNSSValid;			//
	
	BOOL isPPSStart;			//PPS
	
	BOOL isFineAlign;
	
	BOOL isRapidAlign;
	
	COUNT Virtual_PPS_Count;
		
	BOOL isDampedOK;
	
	BOOL isVGInit;
	
	UINT8 System_Ctr;
	
	UINT8 LastSystem_Ctr;
	
	TIME Time_FineAlign;
	
	COUNT Valid_PPS_Count;
	
	ATTI r_ShootYaw;
	
	DPARA Xk_M[DIM_STATE];
	
	DPARA Pk_M[DIM_STATE];
	
	COUNT Count_GyroErr[3];
	
	COUNT Count_AccErr[3];
	
	UINT8 IMU_Valid;
}SysVar,*p_SysVar;




/*********************************************************************************************************************/
typedef struct s_InitBind
{
	LATI r_InitLati;             //rad-G/2 ~ +G/2,0i
  
  LOGI r_InitLogi;             //rad-G ~ +G0i
  
  HEIGHT InitHeight;              // m
	
	
	VEL InitVn[3];             //m/siiInitVn[0]InitVn[1]InitVn[2]
	
	ATTI r_InitHead;
	
	ATTI r_Pitch;
	
	ATTI r_Roll;
	
	BOOL isBind;
	
	BOOL isHeadBind;
	
}InitBind,*p_InitBind;

/*********************************************************************************************************************/
typedef struct s_Align
{	
  MATR AlignCnb[9];          //SGi
  
  QUAT AlignQ[4];            //iSiGi
  
  ACCELER AccSum[3];
  
  ANGRATE GyroSum[3];
  
  ATTI r_AlignAtti[3];       //i(G)rad
  
  COUNT AlignCount;          //ii
	
	//TIME AlignTime;            //
    	
}Align,*p_Align;


/***************************************************Gi******************************************************************/
typedef struct s_InertialSysAlign
{
    //IiI
    ANGRATE r_Wibb[2][3];           //GiigIrad/s

    ACCELER Fibb[3];                //Giigm/s2

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    LATI r_Lati;               //rad-G/2 ~ +G/2,0i

    LOGI r_Logi;               //rad-G ~ +G0i

    HEIGHT Height;             // m

    MATR Cbib0[9];             //bGiib0

    MATR Cib0b[9];             //Giib0b

    MATR Cie[9];               //Gie

    MATR Cen[9];               //en

    MATR Cib0i[9];             //GiGi

    QUAT Qbib0[4];             //bib0i

    VEL Vi[3];                 //ii

    VEL Vib0[3];               //ib0i

    VEL Vi_T1[3];              //t1ii

    VEL Vib0_T1[3];            //t1ib0i

    ACCELER Gn;                //m/s2

    MATR AlignCnb[9];          //SGi

    QUAT AlignQ[4];            //iSiGi

    ATTI r_AlignAtti[3];       //i(G?rad

    BOOL isT1Record;

    TIME T1;                   //Gt1Gt2t2

    TIME AlignTime;            //:s
		
	TIME AlignEndTime;

    COUNT AlignCount;          //ii   

    BOOL isAlign_Finish;       //
}InertialSysAlign,*p_InertialSysAlign;





/*********************************************************************************************************************/
typedef struct s_DynamicInertialSysAlign
{	
  //
  ANGRATE r_Wibb[2][3];              //rad/s
	
	DELANG r_DelSenbb_1[3];
	
	DELANG r_DelSenbb_2[3];
	
	DELANG r_DelSenbb[3];
  
  ACCELER Fibb[3];                //m/s2
  
  LATI r_Lati;               //rad-/2 ~ +/2,0
  
  LOGI r_Logi;               //rad- ~ +0
  
  HEIGHT Height;             // m

  VEL Vn[3];

  VEL LastVn[3];

  LEN Rm;                    //m

  LEN Rn;                    //m

  DPARA invRm;               //1/m

  DPARA invRn;               //1/m

  ANGRATE r_Wien[3];         //Wierad/s

  ANGRATE r_Wenn[3];         //Wenrad/s

  //ANGRATE r_Wnbb[3];         //rad/s

  MATR Cbib0[9];             //bib0
  
  MATR Cib0b[9];             //ib0b
  
  MATR Cie[9];               //ie
  
  MATR Cen[9];               //en
  
  MATR Cib0i[9];             //i
  
  QUAT Qbib0[4];             //bib0
  
  VEL Vi[3];                 //i

  VEL Vib0[3];               //ib0
  
  VEL Si_T1[3];              //t1i

  VEL Sib0_T1[3];            //t1ib0

  LEN Si[3];

  LEN Sib0[3];
  
  ACCELER Gn;                //m/s2
  
  MATR AlignCnb[9];          //
  
  QUAT AlignQ[4];            //
  
  ATTI r_AlignAtti[3];       //()rad
  
  BOOL isT1Record;
  
  TIME T1;                   //t1t2t2
  
  TIME AlignTime;            //:s
  
  COUNT AlignCount;          //

  BOOL isAlignInit;
	
	BOOL isAlign_Finish;       //
    	
}DynamicInertialSysAlign,*p_DynamicInertialSysAlign;





/*****************************************************G********************************************************************/
typedef struct s_Navi
{            
    //IiI
    ANGRATE r_Wibb[2][3];              //GiigIrad/s
    ACCELER Fibb[2][3];                //Giigm/s2

    //iI

    ATTI r_Atti[3];            //igrad

    MATR Cnb[9];               //SGi

    MATR Cbn[9];

    QUAT Q[4];                 //iSiGi

    LEN Rm;                    //m

    LEN Rn;                    //m

    DPARA invRm;               //i1/m

    DPARA invRn;               //i1/m

    ANGRATE r_Wien[3];         //Wieirad/s

    ANGRATE r_Wenn[3];         //GiWenirad/s

    ANGRATE r_Wnbb[2][3];         //Giiigrad/s

    DELANG r_DelSenbb_1[3];

    DELANG r_DelSenbb_2[3];

    DELANG r_DelSenbb[3];

    ACCELER Vibn[3];           //ii)m/s2,igi

    ACCELER Fibn[3];

    ACCELER Aenn[3];           //Gm/s2,igi

    ACCELER Gn;                //m/s2                

    LATI r_Lati;               //rad-G/2 ~ +G/2,0i

    LOGI r_Logi;               //rad-G ~ +G0i

    LATI r_Lati_Err;               

    LOGI r_Logi_Err; 
		
		HEIGHT Height_Err;

    DPARA K_Correct_Lati;

    DPARA K_Correct_Logi;
		
		DPARA K_Correct_Height;

    DPARA VnErr[3];

    DPARA r_AttiErr_n[3];

    COUNT Correct_Count;

    ANGRATE r_AttiRate[3];     //igrad/s

    //IGSi
    ANGRATE r_GyroBias[3];

    ACCELER AccBias[3];
    //iI
    COUNT Navi_Count;          //ii

    ATTI d_Atti[3];            //ig,/s

    ANGRATE d_AttiRate[3];     ///s

    LATI d_Lati;               //-90 ~ +90,0i

    LOGI d_Logi;               //-180 ~ +1800i

    VEL LastVn[3];              //Gm/siiVn[0]Vn[1]Vn[2]

    VEL Vn[3];                  //Gm/siiVn[0]Vn[1]Vn[2]

    //VEL Vb[3];                  //Gm/s, iiVb[0]Vb[1]Vb[2]

    LEN Sn[3];                    //GmiiSn[0]Sn[1]Sn[2]

    HEIGHT Height;                // m

    HEIGHT DampHeight;

    BOOL isDampHeight;

    BOOL isHeadBind;

    DPARA K3_Integral;

    BOOL isHeadingchange;

    BOOL isHeadingChangeLarge;

    LEN GNSSLeverArm[3];                  //GPS

    VEL Vn_r[3];                         //n

    ANGRATE d_HeadingRateBuffer[HEADINGRATE_BUFFER_SIZE];          //

    ANGRATE d_HeadingRate_Mean;

    COUNT HeadingRate_Circle_Count;

    MATR Cpb[9];

    VEL Vp[3];

    VEL VnCorrBuffer[3];

    //ANGRATE r_Gyro_Bias[3];

    //ACCELER Acc_Bias[3];

    VEL DampedV[3];

    ACCELER Horizontal_Acc;

    COUNT VnBufferCount;
		
		VEL Vs[3];
		
		LEN Ss[3];
		
		ATTI r_RealTimeYaw;

} Navi,*p_Navi;




/*****************************************************Kalman********************************************************************/
typedef struct s_Kalman
{
	MODE Obv_Fit_Mode;
	
	MODE Work_Mode;
	
	BOOL isInsRecord;                    //GNSSiI
	
	BOOL isKalmanStart;                  //G
	
	BOOL isKalmanComputeStart;           //G
	
	BOOL isCorrectError;                 //GiG
	
	BOOL isVnCorrect;                    //GSi
	
	BOOL isPosCorrect;                   //GSi
	
	BOOL isAttiCorrect;                  //GSi
	
	BOOL isHeadingCorrect;               //GSi
	
	BOOL isGyroDriftCorrect;             //GSiI
	
	BOOL isAccDriftCorrect;              //GSi
	
	IPARA Actual_Dim_Obv;               //i
	
	VEC Zk[DIM_MAX_OBV];                 // GiDIM_MAX_OBVIG
	
	MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV];  //GiDIM_MAX_OBV * DIM_MAX_OBV
	
	VEC Xk[DIM_STATE];                   //Gi:DIM_STATEIG
	
	VEC Xkk_1[DIM_STATE];                //Gi:DIM_STATEIG
	
	MATR Hk[DIM_MAX_OBV * DIM_STATE];    //i: DIM_MAX_OBV * DIM_STATE
	
	MATR Fn[DIM_STATE * DIM_STATE];      //,iDIM_STATE * DIM_STATE
	
	MATR Fk[DIM_STATE * DIM_STATE];      //iDIM_STATE * DIM_STATE
	
	MATR Kk[DIM_STATE * DIM_MAX_OBV];    //GSiiDIM_STATE * DIM_MAX_OBV
	
  MATR Pk[DIM_STATE * DIM_STATE];      //GiDIM_STATE * DIM_STATE
	
  MATR Pkk_1[DIM_STATE * DIM_STATE];   //GiDIM_STATE * DIM_STATE
	
  MATR Qk[DIM_STATE * DIM_STATE];      //GiDIM_STATE * DIM_STATE
	
	MATR TrFk[DIM_STATE * DIM_STATE];
	
	MATR Fk_Pk[DIM_STATE * DIM_STATE];
	
  VEC InsVn[3];                        //m/s
  
  LEN GPSLeverArm[3];                  //GPS
  
  VEL Vn_r[3];                         //n
  
  ATTI r_InsFai;
	
	LATI r_InsLati; 
	
	LOGI r_InsLogi;
	
	HEIGHT InsHeight;
	
	ACCELER Acc_Horizontal;
	
	ACCELER Acc_All;
	
	//COUNT HeadingValidCount;
	
	COUNT Kal_Count;                     //Kalmani
	
	COUNT ComputeFn_Count;
	
	COUNT Kal_Predict_Count;
	
	//BOOL isZeroSpeedInsRecord;
	
	//BOOL isDRInsRecord;
	
	BOOL isInsPosRecord;
	
	BOOL isHeightRecord;
	
	COUNT State;
	
} Kalman, *p_Kalman;

/*****************************************************GNSSiI********************************************************************/
typedef struct s_GNSSData 
{
    DPARA Hdop;                          //GNSSiIGDOP
	
	DPARA Vdop;

    LATI r_GNSSLati;                     //GNSSrad

    LOGI r_GNSSLogi;                     //GNSSrad

    HEIGHT GNSSHeight;                   //GNSSm

    //HEIGHT GNSSDamped_Height;

    ATTI r_GPSHead;						//

    ATTI r_TrackAtti;					//GNSS

    VEL GNSSVn[3];     					//iGNSSigGNSSGNSSGNSSm/s

    VEL GNSS_V;							//

	//VEL MAX_GNSS_V;

	COUNT UseSatNum;

    BOOL GNSS_State;
		
	INT8 GNSS_POS_State;

	UINT16 GNSS_V_State;

	UINT8 GNSS_Head_State;

    BOOL isVelEn;

    BOOL isPosEn;

    BOOL isHeadingEn;

    BOOL isHeightEn; 

    COUNT GNSS_Valid_Count;
		
	BOOL isGNSS_Valid_3s;

	//BOOL isGNSS_V_Static;
}GNSSData,*p_GNSSData;


/*****************************************************IGiI********************************************************************/
typedef struct s_SenorRaw
{
		INT32 GyroRaw[3];
		
		INT32 GyroTempRaw[3];
		
		INT32 AccRaw[3];
		
		INT32 AccTempRaw[3];
		
		INT32 BoardtempRaw;//		
}SenorRaw,*p_SenorRaw;
/*****************************************************???????????********************************************************************/
typedef struct s_Compen
{
	//?????????????????
	DPARA GyroRaw[3];                       //??????,??:????????
	DPARA AccRaw[3];                        //????????,??:????????
	
	DRAW RTGyroBias[3];
	
	MATR RTGyroScalFac[3];
	
	IPARA GyroTempRangeNum[3];
	
	
	MATR AccLeverArm[9];  //???????????????,??:m 
	
	DRAW RTAccBias[3];
	
	MATR RTAccScalFac[3];
	
	//MATR RTAccFacNonLiner[3];
	
	IPARA AccTempRangeNum[3];
	          
	//??????????????????
	DPARA Gyro[3];                            //???????,??:????????:rad?rad/s,/s,?
	DPARA Acc[3];                             //?????????,??:????????:g?m/s2?
	
	DPARA LastGyro[3];                            
	DPARA LastAcc[3];
	
	DPARA GyroTemp[3];                        //?????????,??:C
	DPARA AccTemp[3];    	//???????????,??:C
	
	//????????
	DPARA GyroTemp_1s_Sum[3];
	DPARA GyroTemp_1s_Mean[3];
	DPARA AccTemp_1s_Sum[3];
	DPARA AccTemp_1s_Mean[3];
	COUNT GyroCount_1s;
	COUNT AccCount_1s;

	//
	DPARA GyroTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];
	DPARA AccTemp_Move_Mean_Buffer[3][NUM_TEMP_DIFF_BUFFER];

	DPARA GyroTemp_Diff[3];
	DPARA AccTemp_Diff[3];

	COUNT AccCircle_Count;
	COUNT GyroCircle_Count;
	BOOL isGyroMove_Mean_BufferInit;
	BOOL isAccMove_Mean_BufferInit;

	DPARA Boardtemp;
	
	COUNT Count_GyroErr[3];
	
	COUNT Count_AccErr[3];
	
	//BOOL isIMUFail;	
	
	UINT8 IMU_Valid;
	
	COUNT IMU_Count;
}Compen,*p_Compen;
typedef struct s_CmdGyroNormalTempCompen
{
	DPARA Temp[3];
	DPARA BoardTemp;
	DPARA Normal_Temperature_Bias[3]; //????LSB
	DPARA Normal_Temperature_Scale_Factor[3]; //??????LSB
	DPARA Misalignment_Compensation_Matrix[9]; //??????
	DPARA Bias_Correct_Val[3];            //?????,???s,??m/s2
}CmdGyroNormalTempCompen,*p_CmdGyroNormalTempCompen;

typedef struct s_CmdAccNormalTempCompen
{
	DPARA Temp[3];
	DPARA BoardTemp;
	DPARA Normal_Temperature_Bias[3]; //????LSB
	DPARA Normal_Temperature_Scale_Factor[3]; //??????LSB
	DPARA Misalignment_Compensation_Matrix[9]; //??????
	DPARA Bias_Correct_Val[3];            //?????,???s,??m/s2
}CmdAccNormalTempCompen,*p_CmdAccNormalTempCompen;

typedef struct s_CmdGyroFullTempCompen
{
	DPARA TempSamPoint[NUM_TEMP_SAM][3]; //?C
	DPARA BoardTempPoint[NUM_TEMP_SAM];  //?C
	DPARA Bias_Value[NUM_TEMP_SAM][3];   //LSB
	DPARA Scale_Factor[NUM_TEMP_SAM][3]; //LSB
}CmdGyroFullTempCompen,*p_CmdGyroFullTempCompen;

typedef struct s_CmdAccFullTempCompen
{
	DPARA TempSamPoint[NUM_TEMP_SAM][3]; //?C
	DPARA BoardTempPoint[NUM_TEMP_SAM];  //?C
	DPARA Bias_Value[NUM_TEMP_SAM][3];   //LSB
	DPARA Scale_Factor[NUM_TEMP_SAM][3]; //LSB
}CmdAccFullTempCompen,*p_CmdAccFullTempCompen;
/*****************************************************?????????????????********************************************************************/
typedef struct s_ANNCompen
{
	//?????????
	DPARA Dense1_Bias[DENSE_1_CELL_NUM];
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM];
	
	DPARA Dense2_Bias[DENSE_2_CELL_NUM];
	DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM];
	
	DPARA Dense3_Bias;
	DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM];
	
	
	DPARA Normalized_Temp_Max;
	DPARA Normalized_Temp_Diff_Max;
	DPARA Normalized_Output_Max;
	
	DPARA Normalized_Temp_Min;
	DPARA Normalized_Temp_Diff_Min;
	DPARA Normalized_Output_Min;
	
	DPARA Normalized_Temp_Mean;
	DPARA Normalized_Temp_Diff_Mean;
	DPARA Normalized_Output_Mean;
	//??????
	DPARA Correct_Value;
}ANNCompen,*p_ANNCompen;

typedef struct s_CmdNormalTempCompenData
{
	CmdGyroNormalTempCompen GyroNormalTempData;
	
	CmdAccNormalTempCompen AccNormalTempData;
	
}CmdNormalTempCompenData,*p_CmdNormalTempCompenData;


typedef struct s_CmdFullTempCompenData
{
	
	CmdGyroFullTempCompen GyroFullTempData;
	
	CmdAccFullTempCompen AccFullTempData;
	
}CmdFullTempCompenData,*p_CmdFullTempCompenData;

typedef struct s_CmdANNCompenData
{

	ANNCompen ANNCompen_X;
	
	ANNCompen ANNCompen_Y;
	
	ANNCompen ANNCompen_Z;

}CmdANNCompenData,*p_CmdANNCompenData;

/*********************************************************************************************************************/
typedef struct s_ZUPT
{
    // 窗口管理
    COUNT WindowIndex;                      // 当前窗口索引
    COUNT SampleCount;                      // 当前样本计数
    BOOL isWindowFilled;                    // 窗口是否已填满

    // IMU数据滑动窗口 (使用float类型以匹配CombineDataTypeDef)
    float AccWindow[ZUPT_WINDOW_SIZE][3];                 // 加速度滑动窗口 [窗口大小][XYZ轴]
    float GyroWindow[ZUPT_WINDOW_SIZE][3];                // 角速度滑动窗口 [窗口大小][XYZ轴]

    // 增量统计量 - 用于高效计算均值和方差
    DPARA AccSum[3];                        // 加速度累加和
    DPARA GyroSum[3];                       // 角速度累加和
    DPARA AccSumSquare[3];                  // 加速度平方累加和
    DPARA GyroSumSquare[3];                 // 角速度平方累加和

    // 合成幅值统计量
    DPARA AccMagnitudeSum;                  // 加速度合成幅值累加和
    DPARA GyroMagnitudeSum;                 // 角速度合成幅值累加和
    DPARA AccMagnitudeSumSquare;            // 加速度合成幅值平方累加和
    DPARA GyroMagnitudeSumSquare;           // 角速度合成幅值平方累加和

    // 统计结果
    DPARA AccMean[3];                       // 加速度均值
    DPARA GyroMean[3];                      // 角速度均值
    DPARA AccVariance[3];                   // 加速度方差
    DPARA GyroVariance[3];                  // 角速度方差

    // 合成幅值统计结果
    DPARA AccMagnitudeMean;                 // 加速度合成幅值均值
    DPARA GyroMagnitudeMean;                // 角速度合成幅值均值
    DPARA AccMagnitudeVariance;             // 加速度合成幅值方差
    DPARA GyroMagnitudeVariance;            // 角速度合成幅值方差

    // ZUPT检测结果
    BOOL ZUPTFlag;                          // ZUPT静止检测标志
    BOOL isZUPTValid;                       // ZUPT检测是否有效（窗口已填满）

    // 调试和监控信息
    COUNT DetectionCount;                   // 检测次数计数
    COUNT StaticCount;                      // 静止状态计数
    COUNT DynamicCount;                     // 运动状态计数

}ZUPT,*p_ZUPT;

#endif
