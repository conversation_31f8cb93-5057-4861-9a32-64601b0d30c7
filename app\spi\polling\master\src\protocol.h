//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：protocol.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.24
//---------------------------------------------------------
#ifndef _PROTOCOL_H
#define _PROTOCOL_H

#include "board.h"
#include "hpm_clock_drv.h"
#include "hpm_uart_drv.h"
#ifdef HPMSOC_HAS_HPMSDK_DMAV2
#include "hpm_dmav2_drv.h"
#else
#include "hpm_dma_drv.h"
#endif
#include "hpm_dmamux_drv.h"
#include "hpm_l1c_drv.h"
#include "hpm_common.h"
#include "arithmetic.h"

//定义Smi240数据结构，用于向算法接口传输原始数据
typedef struct _Smi240_Data
{
        float accel_x;
        float accel_y;
        float accel_z;
        float gyro_x;
        float gyro_y;
        float gyro_z;
        float temp_UNO;			
}__attribute__((packed)) Smi240_Data,*p_Smi240_Data;

/**********************************原始数据结构体**************************************/
//--包头--------------------
typedef struct _Imubaghead																
{
	unsigned char header[2];//报文头固定:0x4A，0x02																														
        unsigned char cmd[2];	//0x0A，0x01														
}__attribute__((packed)) Imubaghead,*p_Imubaghead;

//--包尾--------------------
typedef struct _Imubagend																
{	
        unsigned char check;	//校验码																												
}__attribute__((packed)) Imubagend,*p_Imubagend;

//发送数据信息体结构
typedef struct _Imubag_info_Send
{
        uint32_t ttemp;//惯导系统时间

        float accel_x;
        float accel_y;
        float accel_z;
        float gyro_x;
        float gyro_y;
        float gyro_z;
        float temp_UNO;			
}__attribute__((packed)) Imubag_info_Send,*p_Imubag_info_Send;

//发送数据信息数据包结构
typedef struct _Imubag_Send															
{
	Imubaghead head;         //包头
	Imubag_info_Send info;   //信息体
	Imubagend ender;         //包尾
}__attribute__((packed)) Imubag_Send,*p_Imubag_Send;

/**********************************组合惯导数据结构体**************************************/
typedef  struct poll_data_F
{
    uint16_t	data1;
    uint16_t 	data2;
    uint16_t	data3;
    //uint32_t	gps_time;
    //uint8_t	type;
} __attribute__((packed)) POLL_DATA_Fx, *pPOLL_DATA_F;

//组合惯导数据结构体 65Btye
typedef  struct
{
    uint8_t                     header[3];	//0xbd,0xdb,0x0b
    short 			roll;		//横滚角
    short 			pitch;		//俯仰角
    short			azimuth;	//方位角
    short 			gyroX;		//陀螺x轴
    short 			gyroY;		//陀螺y轴

    //short			gyroZ;		//陀螺z轴
    long                        gyroZ;		//陀螺z轴
   
    short 			accelX;		//加表x轴
    short 			accelY;		//加表y轴
    short			accelZ;		//加表z轴

    long			latitude;	//纬度
    long			longitude;	//经度
    long			altitude;	//高度
    short			ve;             //东向速度
    short			vn;		//北向速度
    short			vu;             //天向速度
    uint8_t			status;		//bit0:位置 bit1:速度 bit2:姿态 bit3:航向角
    
    //short                       temp;           // 温度
    uint8_t			reserved[6];

    POLL_DATA_Fx		poll_frame;
    uint32_t			gps_time;
    uint8_t                     type;
    uint8_t                     xor_verify1;
    uint32_t			gps_week;
    uint8_t                     xor_verify2;
} __attribute__((packed)) DATA_STREAM;


//组合惯导数据结构体 22Btye
typedef  struct
{
    uint8_t                     header[3];	//0xbd,0xdb,0x0b

    short 			roll;		//横滚角
    short 			pitch;		//俯仰角
    short			azimuth;	//方位角

    short 			gyroX;		//陀螺x轴
    short 			gyroY;		//陀螺y轴
    short			gyroZ;		//陀螺z轴
   
    short 			accelX;		//加表x轴
    short 			accelY;		//加表y轴
    short			accelZ;		//加表z轴
    
    uint8_t                     CheckXor;       //异或校验
} __attribute__((packed)) DATA_STREAM_22B;

/**********************************纯惯导数据结构体**************************************/
typedef struct _PureImubagData_info
{
        uint32_t       ttemp;	//惯导系统时间	
        
        //校准后的加速度计数据
        float accel_x;
        float accel_y;
        float accel_z;

        //校准后的加速度计数据
        float gyro_x;
        float gyro_y;
        float gyro_z;	
        
        //线加速度数据	
        float Accel_X;	
        float Accel_Y;	
        float Accel_Z;		
        
        //四元数        
        float Quart_W;		
        float Quart_X;	
        float Quart_Y;	
        float Quart_Z;			
}__attribute__((packed)) PureImubagData_info,*p_PureImubagData_info;

typedef struct _PureImubagData																
{
	unsigned char header[5];//0x3A，0x01,0x00,0x09,0x00																														
        unsigned short len;	

        //纯惯导数据
        PureImubagData_info info;

        //校验和	
        unsigned short CheckSum;
        unsigned char ender[2];//0x0D，0x0A						
}__attribute__((packed)) PureImubagData,*p_PureImubagData;

//纯惯导数据结构体 36Btye
typedef  struct _PureImubagData_36B
{
    uint8_t                     header[3];	//0x3B,0x02,0x0b

    uint32_t                    ttemp;          //惯导系统时间

    short 			gyroX;		//陀螺x轴
    short 			gyroY;		//陀螺y轴
    short			gyroZ;		//陀螺z轴
   
    short 			accelX;		//加表x轴
    short 			accelY;		//加表y轴
    short			accelZ;		//加表z轴

    //四元数        
    float                       Quart_W;		
    float                       Quart_X;	
    float                       Quart_Y;	
    float                       Quart_Z;	
    
    uint8_t                     CheckXor;       //异或校验
} __attribute__((packed)) PureImubagData_36B,*p_PureImubagData_36B;



//原始数据输出
void Smi240UartSend(float *txbuf);

//组合惯导数据输出65字节
void CombinationUartSend(float *txbuf);
//组合惯导数据输出22字节
void CombinationUartSend22B(float *txbuf);

//纯惯导数据输出67字节
void PureUartSend(p_InavOutDataDef lp_InavOutData);
//纯惯导数据输出32字节
void PureUartSend36B(p_InavOutDataDef lp_InavOutData);


//原始数据输出
void Smi240Spi2Send(float *txbuf);
//组合惯导数据输出65字节
void CombinationSpi2Send(float *txbuf);
//纯惯导数据输出67字节
void PureSpi2Send(float *txbuf);

void GetSmi240Data(float *ImuData);
void Smi240DataToAlgorithm(p_Compen lp_Compen);

#endif