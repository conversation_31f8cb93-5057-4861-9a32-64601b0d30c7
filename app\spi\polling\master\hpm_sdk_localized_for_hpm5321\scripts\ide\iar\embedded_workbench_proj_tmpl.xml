<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>GDeviceSelect</name>
                    <state>{{ target["target_device_name" ]}}</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Debug\BrowseInfo</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>1</version>
                    <state>{{ target["iar_runtime_lib"] }}</state>
                </option>
                <option>
                    <name>GRTDescription</name>
                    <state>A compact configuration of the C/C++14 runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>GRTConfigPath</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>1</version>
                    {%- if target["enable_dsp"] %}
                    <state>3</state>
                    {%- else %}
                    <state>1</state>
                    {%- endif %}
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
                <option>
                    <name>GGeneralStack</name>
                    <state>{{ target["stack_size"] }}</state>
                </option>
                <option>
                    <name>GHeapSize</name>
                    <state>{{ target["heap_size"] }}</state>
                </option>
                <option>
                    <name>GNumCores</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>GDeviceSelectSlave</name>
                    <state>RV32	RV32</state>
                </option>
                <option>
                    <name>GGeneralAutoVectorSetup</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCoreIBASRadioSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceMultSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceAtomicSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCompactSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceFloatSelectSlave</name>
                    <version>1</version>
                    <state>{{ target["fpu_type"]}}</state>
                </option>
                <option>
                    <name>GCoreDevice</name>
                    <state>{{ target["compiler_arch"] }}</state>
                </option>
                <option>
                    <name>RadioStdOutErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioLibLowLev</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceUserLvlIntSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipASlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipBSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXandesperfSlave</name>
                    <state>{{ target["enable_andesperf"]}}</state>
                </option>
                <option>
                    <name>GDeviceBitmanipSSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Debug\</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCountZeroesSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GCodeModelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseJalSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXZenVBitfieldsSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceNXPVirgoSupervisorSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceResumableNMISlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDevicePackedSIMDZpsfoperandSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceDspRadioSlave</name>
                    <state>{{ target["enable_dsp"]}}</state>
                </option>
                <option>
                    <name>GDeviceCacheManagementSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCachePrefetchSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheZeroSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheEswinSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>8</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>ICore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>{{ target["iar_cc_lang"] }}</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>{{ target["iar_opt_level"] }}</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>{{ target["iar_opt_strategy"] }}</state>
                </option>
                <option>
                    <name>CCNoSizeConst</name>
                    <state>{{ target["iar_opt_no_size_const"] }}</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>{{ target["iar_opt_level_slave"] }}</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>0</version>
                    <state>1111111</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptions</name>
                    {%- for opt in target["extra_iar_options"] %}
                    <state>{{ opt }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    {%- for def in target["defines"] %}
                    <state>{{ def }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    {%- if target["cc_preinclude"] %}
                    <state>{{ target["cc_preinclude"] }}</state>
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    {%- for inc in target["includes"] %}
                    <state>{{ inc }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ICodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IASMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AsmCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    {%- for inc in target["includes"] %}
                    <state>{{ inc }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>AsmDefines</name>
                    {%- for def in target["defines"] %}
                    <state>{{ def }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>PreInclude</name>
                    {%- if target["asm_preinclude"] %}
                    <state>{{ target["asm_preinclude"] }}</state>
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ACodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>demo.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>demo.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    {%- for sym in target["iar_link_symbols"] %}
                    <state>{{ sym }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>{{ target["linker"] }}</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    {%- if target["iar_link_input"] %}
                    {%- for lib in target["iar_link_input"] %}
                    <state>{{ lib }}</state>
                    {%- endfor %}
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoVectorSetupSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILINKStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ILinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCoDenseJal</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Uninitialized###</state>
                </option>
            </data>
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>RISCV</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>GDeviceSelect</name>
                    <state>{{ target["target_device_name" ]}}</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Release\BrowseInfo</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>1</version>
                    <state>{{ target["iar_runtime_lib"] }}</state>
                </option>
                <option>
                    <name>GRTDescription</name>
                    <state>A compact configuration of the C/C++14 runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>GRTConfigPath</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>1</version>
                    {%- if target["enable_dsp"] %}
                    <state>3</state>
                    {%- else %}
                    <state>1</state>
                    {%- endif %}
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GInputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputDescription</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GenMathFunctionVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>GenMathFunctionDescription</name>
                    <state>Default variants of cos, sin, tan, log, log10, pow, and exp.</state>
                </option>
                <option>
                    <name>GGeneralStack</name>
                    <state>{{ target["stack_size"] }}</state>
                </option>
                <option>
                    <name>GHeapSize</name>
                    <state>{{ target["heap_size"] }}</state>
                </option>
                <option>
                    <name>GNumCores</name>
                    <state></state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>GDeviceSelectSlave</name>
                    <state>RV32	RV32</state>
                </option>
                <option>
                    <name>GGeneralAutoVectorSetup</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceCoreIBASRadioSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceMultSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceAtomicSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCompactSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceFloatSelectSlave</name>
                    <version>1</version>
                    <state>{{ target["fpu_type"]}}</state>
                </option>
                <option>
                    <name>GCoreDevice</name>
                    <state>{{ target["compiler_arch"] }}</state>
                </option>
                <option>
                    <name>RadioStdOutErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RadioLibLowLev</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceUserLvlIntSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipASlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipBSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXandesperfSlave</name>
                    <state>{{ target["enable_andesperf"]}}</state>
                </option>
                <option>
                    <name>GDeviceBitmanipSSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Release\</state>
                </option>
                <option>
                    <name>GDeviceBitmanipCountZeroesSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GCodeModelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceXCoDenseJalSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceXZenVBitfieldsSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceNXPVirgoSupervisorSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceResumableNMISlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDevicePackedSIMDZpsfoperandSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GDeviceDspRadioSlave</name>
                    <state>{{ target["enable_dsp"]}}</state>
                </option>
                <option>
                    <name>GDeviceCacheManagementSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCachePrefetchSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheZeroSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GDeviceCacheEswinSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>8</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ICore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>{{ target["iar_cc_lang"] }}</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccLanguageConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCharIs</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>{{ target["iar_opt_level"] }}</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>{{ target["iar_opt_strategy"] }}</state>
                </option>
                <option>
                    <name>CCNoSizeConst</name>
                    <state>{{ target["iar_opt_no_size_const"] }}</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>{{ target["iar_opt_level_slave"] }}</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>0</version>
                    <state>1111111</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCExtraOptionsCheck</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCExtraOptions</name>
                    {%- for opt in target["extra_iar_options"] %}
                    <state>{{ opt }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCDefines</name>
                    {%- for def in target["defines"] %}
                    <state>{{ def }}</state>
                    {%- endfor %}
                    <state>NDEBUG</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    {%- if target["cc_preinclude"] %}
                    <state>{{ target["cc_preinclude"] }}</state>
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    {%- for inc in target["includes"] %}
                    <state>{{ inc }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ICodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IASMRISCV</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AsmCore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmCaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AsmAllowMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmAllowDirectives</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoDiagnostics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListIncludeCrossRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListMacroDefinitions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListNoMacroExpansion</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListAssembledOnly</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmListTruncateMultiline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmStdIncludeIgnore</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmIncludePath</name>
                    {%- for inc in target["includes"] %}
                    <state>{{ inc }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>AsmDefines</name>
                    {%- for def in target["defines"] %}
                    <state>{{ def }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>PreInclude</name>
                    {%- if target["asm_preinclude"] %}
                    <state>{{ target["asm_preinclude"] }}</state>
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>AsmPreprocOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocComment</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmDiagnosticsSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsError</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmDiagnosticsWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmLimitNumberOfErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmMaxNumberOfErrors</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AsmUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AsmExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>ACodeModel</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>demo.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>9</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStackSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSize</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>demo.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    {%- for sym in target["iar_link_symbols"] %}
                    <state>{{ sym }}</state>
                    {%- endfor %}
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>{{ target["linker"] }}</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    {%- if target["iar_link_input"] %}
                    {%- for lib in target["iar_link_input"] %}
                    <state>{{ lib }}</state>
                    {%- endfor %}
                    {%- else %}
                    <state></state>
                    {%- endif %}
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkAutoVectorSetupSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILINKStdOutErr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkCspyDebugSupportEnable2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ILinkCodeModel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCore</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ILinkCoDenseJal</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>5</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Uninitialized###</state>
                </option>
            </data>
        </settings>
    </configuration>
    {{ target["file_structure"] }}
</project>
