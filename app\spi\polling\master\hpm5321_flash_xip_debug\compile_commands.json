[{"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o CMakeFiles\\app.dir\\src\\spi.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\src\\spi.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\src\\spi.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\E_\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\pinmux.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\pinmux.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\pinmux.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\E_\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\board.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\board.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\boards\\hpm5321\\board.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\toolchains\\reset.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\reset.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\reset.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\toolchains\\trap.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\trap.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\trap.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\system.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\system.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\system.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\hpm_sysctl_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_sysctl_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_sysctl_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\hpm_l1c_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_l1c_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_l1c_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\hpm_clock_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_clock_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_clock_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\hpm_otp_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_otp_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\hpm_otp_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\soc\\HPM5300\\HPM5361\\boot\\hpm_bootheader.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\boot\\hpm_bootheader.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\boot\\hpm_bootheader.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_uart_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_uart_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_uart_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_sdp_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_sdp_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_sdp_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_i2c_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_i2c_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_i2c_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pmp_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pmp_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pmp_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_rng_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_rng_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_rng_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_gpio_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_gpio_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_gpio_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_spi_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_spi_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_spi_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_gptmr_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_gptmr_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_gptmr_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pwm_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pwm_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pwm_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pllctlv2_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pllctlv2_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pllctlv2_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_usb_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_usb_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_usb_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_acmp_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_acmp_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_acmp_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_adc16_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_adc16_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_adc16_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_pcfg_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pcfg_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_pcfg_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_ptpc_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_ptpc_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_ptpc_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_mchtmr_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mchtmr_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mchtmr_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_tsns_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_tsns_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_tsns_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_dac_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_dac_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_dac_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_crc_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_crc_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_crc_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_mcan_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mcan_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mcan_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_qeiv2_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_qeiv2_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_qeiv2_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_enc_pos_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_enc_pos_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_enc_pos_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_sei_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_sei_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_sei_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_qeo_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_qeo_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_qeo_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_rdc_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_rdc_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_rdc_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_mmc_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mmc_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_mmc_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_dmav2_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_dmav2_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_dmav2_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_ewdg_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_ewdg_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_ewdg_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_plb_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_plb_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_plb_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\drivers\\src\\hpm_opamp_drv.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_opamp_drv.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\drivers\\src\\hpm_opamp_drv.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_sbrk.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_sbrk.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_sbrk.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_swap.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_swap.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_swap.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_ffssi.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_ffssi.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_ffssi.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\utils\\hpm_crc32.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_crc32.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\utils\\hpm_crc32.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_lib.dir\\components\\debug_console\\hpm_debug_console.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\components\\debug_console\\hpm_debug_console.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\components\\debug_console\\hpm_debug_console.c"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_gcc_lib.dir\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\start.S.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\start.S", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\start.S"}, {"directory": "E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug", "command": "E:\\2014902\\HPM\\HPM6750\\sdk_env_v1.6.01\\toolchains\\rv32imac_zicsr_zifencei_multilib_b_ext-win\\bin\\riscv32-unknown-elf-gcc.exe -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs -o build_tmp\\CMakeFiles\\hpm_sdk_gcc_lib.dir\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\initfini.c.obj -c E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\initfini.c", "file": "E:\\2014902\\HPM\\HPM5331\\MEMS-HPM5331\\Smi270\\app\\spi\\polling\\master\\hpm_sdk_localized_for_hpm5321\\soc\\HPM5300\\HPM5361\\toolchains\\gcc\\initfini.c"}]