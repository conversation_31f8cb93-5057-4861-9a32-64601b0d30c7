#ifndef _GLOBALDATA_H
#define _GLOBALDATA_H
/*****************************************************�ļ�˵��******************************************************************************/
/*�ļ����ƣ�GLOBALDATA.h                                                                                                                   */
/*�汾�ţ�  Ver 0.1                                                                                                                        */
/*��д����/ʱ�䣺                                                                                                */
/*��д�ˣ�                                                                                                                             */
/*�����ļ�����                                                                                                                             */
/*������������GNSSlocusGen.m�ļ�������������ϵͳ��������������ģ������������ȱ                                                              */
/*˵����ļ������˳�����������ȫ�ֱ���������ļ�Ϊ��ʼ���԰汾���ļ���������ȫ�ֳ�������������Ա�ο�ѡ�ã�                       */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
//#include "data.h"
//�ṹ��ȫ�ֱ��
SelfTest g_SelfTest;//����ϵͳ�Լ��ṹ��ȫ�ֱ��

SysVar g_SysVar;  //����ϵͳ����ṹ��ȫ�ֱ��

InitBind g_InitBind;//�����ߵ���ʼװ���ṹ��ȫ�ֱ��

Align g_Align;//�����ߵ���ʼ��׼�ṹ��ȫ�ֱ��

Navi g_Navi;//������ṹ��ȫ�ֱ��

Kalman g_Kalman;//����Kalman�˲��ṹ��ȫ�ֱ��

GNSSData g_GNSSData_In_Use,g_GNSSData_For_FineAlign;//����GNSS���ݽṹ��ȫ�ֱ��

Compen g_Compen;//����������ṹ��ȫ�ֱ��

InertialSysAlign g_InertialSysAlign; //惯性凝固坐标系初始对准结构体全局变量

DynamicInertialSysAlign g_DynamicInertialSysAlign;

IMUSmoothAverage g_IMUSmoothAverage;

CmdNormalTempCompenData g_CmdNormalTempCompenData;

CmdFullTempCompenData g_CmdFullTempCompenData;

CmdANNCompenData g_GyroANNCompen;

CmdANNCompenData g_AccANNCompen;

ZUPT g_ZUPT; //ZUPT静止检测结构体全局变量
#endif
