# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.24

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: spi_polling_master_example
# Configurations: debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/
# =============================================================================
# Object build statements for OBJECT_LIBRARY target app


#############################################
# Order-only phony target for app

build cmake_object_order_depends_target_app: phony || CMakeFiles/app.dir

build CMakeFiles/app.dir/src/spi.c.obj: C_COMPILER__app_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/src/spi.c || cmake_object_order_depends_target_app
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = CMakeFiles\app.dir\src\spi.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = CMakeFiles\app.dir
  OBJECT_FILE_DIR = CMakeFiles\app.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\app.dir\
  TARGET_PDB = ""



#############################################
# Object library app

build app: phony CMakeFiles/app.dir/src/spi.c.obj

# =============================================================================
# Object build statements for EXECUTABLE target demo.elf


#############################################
# Order-only phony target for demo.elf

build cmake_object_order_depends_target_demo.elf: phony || cmake_object_order_depends_target_app cmake_object_order_depends_target_hpm_sdk_gcc_lib cmake_object_order_depends_target_hpm_sdk_lib


# =============================================================================
# Link build statements for EXECUTABLE target demo.elf


#############################################
# Link the executable output\demo.elf

build output/demo.elf: C_EXECUTABLE_LINKER__demo.2eelf_debug CMakeFiles/app.dir/src/spi.c.obj | lib/libhpm_sdk_gcc_lib.a lib/libhpm_sdk_lib.a E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/gcc/flash_xip.ld || app lib/libhpm_sdk_gcc_lib.a lib/libhpm_sdk_lib.a
  FLAGS = -g
  LINK_LIBRARIES = -Wl,-Map=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/output/demo.map  -Wl,--whole-archive  lib/libhpm_sdk_gcc_lib.a  lib/libhpm_sdk_lib.a  -Wl,--no-whole-archive  -static  -nostartfiles  -Wl,--gc-sections  -Wl,--print-memory-usage  --specs=nano.specs  -u _printf_float  -u _scanf_float  -Xlinker --defsym=_heap_size=0x4000  -Xlinker --defsym=_stack_size=0x4000  -Xlinker --defsym=_flash_size=1M  -mabi=ilp32  -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs  -T E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/gcc/flash_xip.ld
  OBJECT_DIR = CMakeFiles\demo.elf.dir
  POST_BUILD = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-objcopy -O binary -S E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/output/demo.elf E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/output/demo.bin && cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\toolchains\rv32imac_zicsr_zifencei_multilib_b_ext-win\bin\riscv32-unknown-elf-objdump -S -d E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/output/demo.elf > E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/output/demo.asm"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\demo.elf.dir\
  TARGET_FILE = output\demo.elf
  TARGET_PDB = output\demo.elf.pdb


#############################################
# Utility command for distclean

build distclean: phony CMakeFiles/distclean


#############################################
# Utility command for localize_sdk

build localize_sdk: phony CMakeFiles/localize_sdk


#############################################
# Utility command for unlocalize_sdk

build unlocalize_sdk: phony CMakeFiles/unlocalize_sdk


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Custom command for CMakeFiles\distclean

build CMakeFiles/distclean | ${cmake_ninja_workdir}CMakeFiles/distclean: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe -DBIN_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug -DSRC_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master -P E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/cleanup.cmake"


#############################################
# Custom command for CMakeFiles\localize_sdk

build CMakeFiles/localize_sdk | ${cmake_ninja_workdir}CMakeFiles/localize_sdk: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe -DPYTHON_EXECUTABLE=E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/python3/python3.exe -DHPM_SDK_BASE=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321 -DSRC_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master -DDST_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master -DBIN_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug -DBUILD_TYPE=flash_xip -DGENERATOR=Ninja -DBOARD=hpm5321 -P E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/localize_sdk.cmake"


#############################################
# Custom command for CMakeFiles\unlocalize_sdk

build CMakeFiles/unlocalize_sdk | ${cmake_ninja_workdir}CMakeFiles/unlocalize_sdk: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe -DPYTHON_EXECUTABLE=E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/python3/python3.exe -DHPM_SDK_BASE=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321 -DSRC_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master -DBIN_DIR=E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug -DGENERATOR=Ninja -DBOARD=hpm5321 -DUNLOCALIZE_PROJECT=1 -P E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/localize_sdk.cmake"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/application.cmake
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target hpm_sdk_lib


#############################################
# Order-only phony target for hpm_sdk_lib

build cmake_object_order_depends_target_hpm_sdk_lib: phony || build_tmp/CMakeFiles/hpm_sdk_lib.dir

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/E_/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/pinmux.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/pinmux.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\E_\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\boards\hpm5321\pinmux.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\E_\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\boards\hpm5321
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/E_/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/board.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/board.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\E_\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\boards\hpm5321\board.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\E_\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\boards\hpm5321
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/toolchains/reset.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/reset.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\toolchains\reset.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\toolchains
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/toolchains/trap.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/trap.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\toolchains\trap.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\toolchains
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/system.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/system.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\system.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_sysctl_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_sysctl_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\hpm_sysctl_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_l1c_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_l1c_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\hpm_l1c_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_clock_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_clock_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\hpm_clock_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_otp_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_otp_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\hpm_otp_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/boot/hpm_bootheader.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot/hpm_bootheader.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\boot\hpm_bootheader.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\soc\HPM5300\HPM5361\boot
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_uart_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_uart_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_uart_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_sdp_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sdp_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_sdp_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_i2c_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_i2c_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_i2c_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pmp_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pmp_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_pmp_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_rng_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rng_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_rng_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_gpio_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gpio_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_gpio_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_spi_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_spi_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_spi_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_gptmr_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gptmr_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_gptmr_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pwm_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pwm_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_pwm_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pllctlv2_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pllctlv2_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_pllctlv2_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_usb_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_usb_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_usb_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_acmp_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_acmp_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_acmp_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_adc16_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_adc16_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_adc16_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pcfg_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pcfg_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_pcfg_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_ptpc_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ptpc_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_ptpc_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mchtmr_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mchtmr_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_mchtmr_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_tsns_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_tsns_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_tsns_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_dac_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dac_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_dac_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_crc_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_crc_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_crc_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mcan_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mcan_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_mcan_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_qeiv2_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeiv2_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_qeiv2_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_enc_pos_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_enc_pos_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_enc_pos_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_sei_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sei_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_sei_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_qeo_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeo_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_qeo_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_rdc_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rdc_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_rdc_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mmc_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mmc_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_mmc_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_dmav2_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dmav2_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_dmav2_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_ewdg_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ewdg_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_ewdg_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_plb_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_plb_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_plb_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_opamp_drv.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/src/hpm_opamp_drv.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src\hpm_opamp_drv.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\drivers\src
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_sbrk.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_sbrk.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils\hpm_sbrk.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_swap.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_swap.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils\hpm_swap.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_ffssi.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_ffssi.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils\hpm_ffssi.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_crc32.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/hpm_crc32.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils\hpm_crc32.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\utils
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_lib.dir/components/debug_console/hpm_debug_console.c.obj: C_COMPILER__hpm_sdk_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/hpm_debug_console.c || cmake_object_order_depends_target_hpm_sdk_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_lib.dir\components\debug_console\hpm_debug_console.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir\components\debug_console
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_lib.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target hpm_sdk_lib


#############################################
# Link the static library lib\libhpm_sdk_lib.a

build lib/libhpm_sdk_lib.a: C_STATIC_LIBRARY_LINKER__hpm_sdk_lib_debug build_tmp/CMakeFiles/hpm_sdk_lib.dir/E_/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/pinmux.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/E_/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/board.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/toolchains/reset.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/toolchains/trap.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/system.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_sysctl_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_l1c_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_clock_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/hpm_otp_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/soc/HPM5300/HPM5361/boot/hpm_bootheader.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_uart_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_sdp_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_i2c_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pmp_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_rng_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_gpio_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_spi_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_gptmr_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pwm_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pllctlv2_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_usb_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_acmp_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_adc16_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_pcfg_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_ptpc_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mchtmr_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_tsns_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_dac_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_crc_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mcan_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_qeiv2_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_enc_pos_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_sei_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_qeo_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_rdc_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_mmc_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_dmav2_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_ewdg_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_plb_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/drivers/src/hpm_opamp_drv.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_sbrk.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_swap.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_ffssi.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/utils/hpm_crc32.c.obj build_tmp/CMakeFiles/hpm_sdk_lib.dir/components/debug_console/hpm_debug_console.c.obj
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_lib.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_lib.dir\hpm_sdk_lib.pdb
  TARGET_FILE = lib\libhpm_sdk_lib.a
  TARGET_PDB = lib\libhpm_sdk_lib.pdb

# =============================================================================
# Object build statements for STATIC_LIBRARY target hpm_sdk_gcc_lib


#############################################
# Order-only phony target for hpm_sdk_gcc_lib

build cmake_object_order_depends_target_hpm_sdk_gcc_lib: phony || build_tmp/CMakeFiles/hpm_sdk_gcc_lib.dir

build build_tmp/CMakeFiles/hpm_sdk_gcc_lib.dir/soc/HPM5300/HPM5361/toolchains/gcc/start.S.obj: ASM_COMPILER__hpm_sdk_gcc_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/gcc/start.S || cmake_object_order_depends_target_hpm_sdk_gcc_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\soc\HPM5300\HPM5361\toolchains\gcc\start.S.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\soc\HPM5300\HPM5361\toolchains\gcc
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\hpm_sdk_gcc_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_gcc_lib.pdb

build build_tmp/CMakeFiles/hpm_sdk_gcc_lib.dir/soc/HPM5300/HPM5361/toolchains/gcc/initfini.c.obj: C_COMPILER__hpm_sdk_gcc_lib_debug E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/gcc/initfini.c || cmake_object_order_depends_target_hpm_sdk_gcc_lib
  DEFINES = -DFLASH_XIP=1 -DHPMSOC_HAS_HPMSDK_ACMP=y -DHPMSOC_HAS_HPMSDK_ADC16=y -DHPMSOC_HAS_HPMSDK_CRC=y -DHPMSOC_HAS_HPMSDK_DAC=y -DHPMSOC_HAS_HPMSDK_DMAMUX=y -DHPMSOC_HAS_HPMSDK_DMAV2=y -DHPMSOC_HAS_HPMSDK_EWDG=y -DHPMSOC_HAS_HPMSDK_GPIO=y -DHPMSOC_HAS_HPMSDK_GPIOM=y -DHPMSOC_HAS_HPMSDK_GPTMR=y -DHPMSOC_HAS_HPMSDK_I2C=y -DHPMSOC_HAS_HPMSDK_IOC=y -DHPMSOC_HAS_HPMSDK_KEYM=y -DHPMSOC_HAS_HPMSDK_MBX=y -DHPMSOC_HAS_HPMSDK_MCAN=y -DHPMSOC_HAS_HPMSDK_MCHTMR=y -DHPMSOC_HAS_HPMSDK_MMC=y -DHPMSOC_HAS_HPMSDK_MON=y -DHPMSOC_HAS_HPMSDK_OPAMP=y -DHPMSOC_HAS_HPMSDK_OTP=y -DHPMSOC_HAS_HPMSDK_PCFG=y -DHPMSOC_HAS_HPMSDK_PDGO=y -DHPMSOC_HAS_HPMSDK_PGPR=y -DHPMSOC_HAS_HPMSDK_PLB=y -DHPMSOC_HAS_HPMSDK_PLIC=y -DHPMSOC_HAS_HPMSDK_PLICSW=y -DHPMSOC_HAS_HPMSDK_PLLCTLV2=y -DHPMSOC_HAS_HPMSDK_PMP=y -DHPMSOC_HAS_HPMSDK_PPOR=y -DHPMSOC_HAS_HPMSDK_PTPC=y -DHPMSOC_HAS_HPMSDK_PWM=y -DHPMSOC_HAS_HPMSDK_QEIV2=y -DHPMSOC_HAS_HPMSDK_QEO=y -DHPMSOC_HAS_HPMSDK_RDC=y -DHPMSOC_HAS_HPMSDK_RNG=y -DHPMSOC_HAS_HPMSDK_SDP=y -DHPMSOC_HAS_HPMSDK_SEC=y -DHPMSOC_HAS_HPMSDK_SEI=y -DHPMSOC_HAS_HPMSDK_SPI=y -DHPMSOC_HAS_HPMSDK_SYNT=y -DHPMSOC_HAS_HPMSDK_SYSCTL=y -DHPMSOC_HAS_HPMSDK_TRGM=y -DHPMSOC_HAS_HPMSDK_TSNS=y -DHPMSOC_HAS_HPMSDK_UART=y -DHPMSOC_HAS_HPMSDK_USB=y
  DEP_FILE = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\soc\HPM5300\HPM5361\toolchains\gcc\initfini.c.obj.d
  FLAGS = -g -Wall -Wundef -Wno-format -fomit-frame-pointer -fno-builtin -ffunction-sections -fdata-sections -g -mabi=ilp32 -march=rv32imac_zicsr_zifencei_zba_zbb_zbc_zbs
  INCLUDES = -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361 -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/inc -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/. -IE:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/generated/include -isystem E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/toolchains/rv32imac_zicsr_zifencei_multilib_b_ext-win/riscv32-unknown-elf/include/c++/include
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir
  OBJECT_FILE_DIR = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\soc\HPM5300\HPM5361\toolchains\gcc
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\hpm_sdk_gcc_lib.pdb
  TARGET_PDB = lib\libhpm_sdk_gcc_lib.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target hpm_sdk_gcc_lib


#############################################
# Link the static library lib\libhpm_sdk_gcc_lib.a

build lib/libhpm_sdk_gcc_lib.a: C_STATIC_LIBRARY_LINKER__hpm_sdk_gcc_lib_debug build_tmp/CMakeFiles/hpm_sdk_gcc_lib.dir/soc/HPM5300/HPM5361/toolchains/gcc/start.S.obj build_tmp/CMakeFiles/hpm_sdk_gcc_lib.dir/soc/HPM5300/HPM5361/toolchains/gcc/initfini.c.obj
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = build_tmp\CMakeFiles\hpm_sdk_gcc_lib.dir\hpm_sdk_gcc_lib.pdb
  TARGET_FILE = lib\libhpm_sdk_gcc_lib.a
  TARGET_PDB = lib\libhpm_sdk_gcc_lib.pdb


#############################################
# Utility command for edit_cache

build build_tmp/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/edit_cache: phony build_tmp/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/rebuild_cache: phony build_tmp/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/arch/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\arch && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/arch/edit_cache: phony build_tmp/arch/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/arch/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\arch && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/arch/rebuild_cache: phony build_tmp/arch/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/boards/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\boards && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/boards/edit_cache: phony build_tmp/boards/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/boards/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\boards && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/boards/rebuild_cache: phony build_tmp/boards/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/boards/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/boards/hpm5321/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\boards\hpm5321 && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/boards/hpm5321/edit_cache: phony build_tmp/boards/hpm5321/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/boards/hpm5321/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\boards\hpm5321 && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/boards/hpm5321/rebuild_cache: phony build_tmp/boards/hpm5321/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/soc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\soc && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/soc/edit_cache: phony build_tmp/soc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/soc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\soc && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/soc/rebuild_cache: phony build_tmp/soc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/soc/HPM5300/HPM5361/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\soc\HPM5300\HPM5361 && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/soc/HPM5300/HPM5361/edit_cache: phony build_tmp/soc/HPM5300/HPM5361/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/soc/HPM5300/HPM5361/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\soc\HPM5300\HPM5361 && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/soc/HPM5300/HPM5361/rebuild_cache: phony build_tmp/soc/HPM5300/HPM5361/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/drivers/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\drivers && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/drivers/edit_cache: phony build_tmp/drivers/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/drivers/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\drivers && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/drivers/rebuild_cache: phony build_tmp/drivers/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/utils/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\utils && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/utils/edit_cache: phony build_tmp/utils/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/utils/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\utils && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/utils/rebuild_cache: phony build_tmp/utils/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/components/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\components && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/components/edit_cache: phony build_tmp/components/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/components/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\components && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/components/rebuild_cache: phony build_tmp/components/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/components/debug_console/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\components\debug_console && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/components/debug_console/edit_cache: phony build_tmp/components/debug_console/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/components/debug_console/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\components\debug_console && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/components/debug_console/rebuild_cache: phony build_tmp/components/debug_console/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/middleware/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/middleware/edit_cache: phony build_tmp/middleware/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/middleware/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/middleware/rebuild_cache: phony build_tmp/middleware/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/middleware/eclipse_threadx/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\eclipse_threadx && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/middleware/eclipse_threadx/edit_cache: phony build_tmp/middleware/eclipse_threadx/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/middleware/eclipse_threadx/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\eclipse_threadx && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/middleware/eclipse_threadx/rebuild_cache: phony build_tmp/middleware/eclipse_threadx/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/middleware/CMSIS/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\CMSIS && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/middleware/CMSIS/edit_cache: phony build_tmp/middleware/CMSIS/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/middleware/CMSIS/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\CMSIS && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/middleware/CMSIS/rebuild_cache: phony build_tmp/middleware/CMSIS/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build build_tmp/middleware/ptpd/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\ptpd && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake-gui.exe -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build build_tmp/middleware/ptpd/edit_cache: phony build_tmp/middleware/ptpd/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build build_tmp/middleware/ptpd/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D E:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug\build_tmp\middleware\ptpd && E:\2014902\HPM\HPM6750\sdk_env_v1.6.01\tools\cmake\bin\cmake.exe --regenerate-during-build -SE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master -BE:\2014902\HPM\HPM5331\MEMS-HPM5331\Smi270\app\spi\polling\master\hpm5321_flash_xip_debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build build_tmp/middleware/ptpd/rebuild_cache: phony build_tmp/middleware/ptpd/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build demo.elf: phony output/demo.elf

build hpm_sdk_gcc_lib: phony lib/libhpm_sdk_gcc_lib.a

build hpm_sdk_lib: phony lib/libhpm_sdk_lib.a

build libhpm_sdk_gcc_lib.a: phony lib/libhpm_sdk_gcc_lib.a

build libhpm_sdk_lib.a: phony lib/libhpm_sdk_lib.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug

build all: phony app output/demo.elf build_tmp/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp

build build_tmp/all: phony lib/libhpm_sdk_lib.a lib/libhpm_sdk_gcc_lib.a build_tmp/arch/all build_tmp/boards/all build_tmp/soc/all build_tmp/drivers/all build_tmp/utils/all build_tmp/components/all build_tmp/middleware/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/arch

build build_tmp/arch/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/boards

build build_tmp/boards/all: phony build_tmp/boards/hpm5321/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/boards/hpm5321

build build_tmp/boards/hpm5321/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/components

build build_tmp/components/all: phony build_tmp/components/debug_console/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/components/debug_console

build build_tmp/components/debug_console/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/drivers

build build_tmp/drivers/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/middleware

build build_tmp/middleware/all: phony build_tmp/middleware/eclipse_threadx/all build_tmp/middleware/CMSIS/all build_tmp/middleware/ptpd/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/middleware/CMSIS

build build_tmp/middleware/CMSIS/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/middleware/eclipse_threadx

build build_tmp/middleware/eclipse_threadx/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/middleware/ptpd

build build_tmp/middleware/ptpd/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/soc

build build_tmp/soc/all: phony build_tmp/soc/HPM5300/HPM5361/all

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/soc/HPM5300/HPM5361

build build_tmp/soc/HPM5300/HPM5361/all: phony

# =============================================================================

#############################################
# Folder: E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm5321_flash_xip_debug/build_tmp/utils

build build_tmp/utils/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.24.0/CMakeASMCompiler.cmake CMakeFiles/3.24.0/CMakeCCompiler.cmake CMakeFiles/3.24.0/CMakeCXXCompiler.cmake CMakeFiles/3.24.0/CMakeSystem.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/boards/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/application.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ccache.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/cmake-ext.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/extra_flags.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/hex.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/hpm-sdk-config.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide/iar.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide/segger.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/python.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain/gcc.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain/ld.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/version.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMSIS/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/eclipse_threadx/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/ptpd/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/CMakeLists.txt E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeASMCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeASMInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCXXInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCompilerIdDetection.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineASMCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCXXCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCompilerId.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineSystem.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeFindBinUtils.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeGenericSystem.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeNinjaFindMake.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystem.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestASMCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestCCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestCXXCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ADSP-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ARMCC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ARMClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/AppleClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Borland-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Bruce-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompilerInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Compaq-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Cray-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Embarcadero-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Fujitsu-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GHS-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-ASM.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-C.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-FindBinUtils.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/HP-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/HP-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IAR-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Intel-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/LCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/MSVC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/NVHPC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/NVIDIA-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/PGI-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/PathScale-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SCO-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SDCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SunPro-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/TI-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Watcom-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XL-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XL-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XLClang-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/zOS-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Platform/Generic.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.24.0/CMakeASMCompiler.cmake CMakeFiles/3.24.0/CMakeCCompiler.cmake CMakeFiles/3.24.0/CMakeCXXCompiler.cmake CMakeFiles/3.24.0/CMakeSystem.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/arch/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/boards/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/application.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ccache.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/cmake-ext.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/extra_flags.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/hex.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/hpm-sdk-config.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide/iar.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/ide/segger.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/python.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain/gcc.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/toolchain/ld.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/cmake/version.cmake E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/components/debug_console/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/drivers/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMSIS/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/eclipse_threadx/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/middleware/ptpd/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/app/spi/polling/master/hpm_sdk_localized_for_hpm5321/utils/CMakeLists.txt E$:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/CMakeLists.txt E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeASMCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeASMInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCXXCompiler.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCXXInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeCompilerIdDetection.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineASMCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCXXCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineCompilerId.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeDetermineSystem.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeFindBinUtils.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeGenericSystem.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeNinjaFindMake.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystem.cmake.in E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestASMCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestCCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/CMakeTestCXXCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ADSP-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ARMCC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/ARMClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/AppleClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Borland-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Bruce-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompilerInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Compaq-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Cray-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Embarcadero-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Fujitsu-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GHS-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-ASM.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-C.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU-FindBinUtils.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/HP-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/HP-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IAR-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Intel-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/LCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/MSVC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/NVHPC-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/NVIDIA-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/PGI-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/PathScale-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SCO-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SDCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SunPro-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/TI-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/Watcom-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XL-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XL-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XLClang-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/zOS-C-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake E$:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/cmake/share/cmake-3.24/Modules/Platform/Generic.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
