/***********************************************************************************************************************************/
/*COMPENSATION.C                                                                                                                 */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*const.htypedefine.hmath.hDATASTRUCT.hEXTERNGLOBALDATA.hFUNCTION.hmemory.h                                          */
/*GNSSlocusGen.m                                                                  */
/* 加计陀螺温度（常温/全温）参数                      */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include <math.h>
#include "SetParaBao.h"
//#include <string.h>
#include <EXTERNGLOBALDATA.h>
/*********************************************????*******************************************************/
/*????:Gyro_Compen_Para_Init_60                                                                        */
/*??????:60???????????                                                                    */
/*???:  Ver 0.1                                                                                         */
/*????                                                                                           */
/*???:                                                                                                  */
/*????:                                                                                                */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:????????????                                                                           */
/*???:?                                                                                                */
/************************************************************************************************************/
void Gyro_Compen_Para_Init(p_CmdGyroNormalTempCompen lp_GyroNormalTempCompen,p_CmdGyroFullTempCompen lp_GyroFullTempCompen)
{
	IPARA i,j;
	//MATR GyroTempSamPoint[NUM_TEMP_SAM][3] ={    -40 ,  -40 ,  -40,
 //                                                    -30 ,  -30 ,  -30,
 //                                                    -20 ,  -20 ,  -20,
 //                                                    -10 ,  -10 ,  -10,
 //                                                      0 ,    0 ,    0,
 //                                                     10 ,   10 ,   10,
 //                                                     20 ,   20 ,   20,
 //                                                     30 ,   30 ,   30,
 //                                                     40 ,   40 ,   40,
 //                                                     50 ,   50 ,   50,
 //                                                     60 ,   60 ,   60,
 //                                                     70 ,   70 ,   70};

    MATR GyroTempSamPoint[NUM_TEMP_SAM][3] ={stSetPara.TemperCompenData.GyroAll_0Para.tempx_40,stSetPara.TemperCompenData.GyroAll_0Para.tempy_40,stSetPara.TemperCompenData.GyroAll_0Para.tempz_40,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx_30,stSetPara.TemperCompenData.GyroAll_0Para.tempy_30,stSetPara.TemperCompenData.GyroAll_0Para.tempz_30,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx_20,stSetPara.TemperCompenData.GyroAll_0Para.tempy_20,stSetPara.TemperCompenData.GyroAll_0Para.tempz_20,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx_10,stSetPara.TemperCompenData.GyroAll_0Para.tempy_10,stSetPara.TemperCompenData.GyroAll_0Para.tempz_10,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx0,stSetPara.TemperCompenData.GyroAll_0Para.tempy0,stSetPara.TemperCompenData.GyroAll_0Para.tempz0,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx10,stSetPara.TemperCompenData.GyroAll_0Para.tempy10,stSetPara.TemperCompenData.GyroAll_0Para.tempz10,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx20,stSetPara.TemperCompenData.GyroAll_0Para.tempy20,stSetPara.TemperCompenData.GyroAll_0Para.tempz20,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx30,stSetPara.TemperCompenData.GyroAll_0Para.tempy30,stSetPara.TemperCompenData.GyroAll_0Para.tempz30,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx40,stSetPara.TemperCompenData.GyroAll_0Para.tempy40,stSetPara.TemperCompenData.GyroAll_0Para.tempz40,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx50,stSetPara.TemperCompenData.GyroAll_0Para.tempy50,stSetPara.TemperCompenData.GyroAll_0Para.tempz50,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx60,stSetPara.TemperCompenData.GyroAll_0Para.tempy60,stSetPara.TemperCompenData.GyroAll_0Para.tempz60,
                                                stSetPara.TemperCompenData.GyroAll_0Para.tempx70,stSetPara.TemperCompenData.GyroAll_0Para.tempy70,stSetPara.TemperCompenData.GyroAll_0Para.tempz70};
																						
	//MATR GyroBias[NUM_TEMP_SAM][3] = {       6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05,
 //                                               6.51709033484038e-06 ,     0.000115724251808377  ,   -8.76366818983895e-05};

 	MATR GyroBias[NUM_TEMP_SAM][3] = {stSetPara.TemperCompenData.GyroAll_1Para.biasvx_40,stSetPara.TemperCompenData.GyroAll_1Para.biasvy_40,stSetPara.TemperCompenData.GyroAll_1Para.biasvz_40,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx_30,stSetPara.TemperCompenData.GyroAll_1Para.biasvy_30,stSetPara.TemperCompenData.GyroAll_1Para.biasvz_30,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx_20,stSetPara.TemperCompenData.GyroAll_1Para.biasvy_20,stSetPara.TemperCompenData.GyroAll_1Para.biasvz_20,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx_10,stSetPara.TemperCompenData.GyroAll_1Para.biasvy_10,stSetPara.TemperCompenData.GyroAll_1Para.biasvz_10,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx0,stSetPara.TemperCompenData.GyroAll_1Para.biasvy0,stSetPara.TemperCompenData.GyroAll_1Para.biasvz0,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx10,stSetPara.TemperCompenData.GyroAll_1Para.biasvy10,stSetPara.TemperCompenData.GyroAll_1Para.biasvz10,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx20,stSetPara.TemperCompenData.GyroAll_1Para.biasvy20,stSetPara.TemperCompenData.GyroAll_1Para.biasvz20,
                                          stSetPara.TemperCompenData.GyroAll_1Para.biasvx30,stSetPara.TemperCompenData.GyroAll_1Para.biasvy30,stSetPara.TemperCompenData.GyroAll_1Para.biasvz30,
                                          stSetPara.TemperCompenData.GyroAll_2Para.biasvx40,stSetPara.TemperCompenData.GyroAll_2Para.biasvy40,stSetPara.TemperCompenData.GyroAll_2Para.biasvz40,
                                          stSetPara.TemperCompenData.GyroAll_2Para.biasvx50,stSetPara.TemperCompenData.GyroAll_2Para.biasvy50,stSetPara.TemperCompenData.GyroAll_2Para.biasvz50,
                                          stSetPara.TemperCompenData.GyroAll_2Para.biasvx60,stSetPara.TemperCompenData.GyroAll_2Para.biasvy60,stSetPara.TemperCompenData.GyroAll_2Para.biasvz60,
                                          stSetPara.TemperCompenData.GyroAll_2Para.biasvx70,stSetPara.TemperCompenData.GyroAll_2Para.biasvy70,stSetPara.TemperCompenData.GyroAll_2Para.biasvz70};

        //MATR GyroScalFac[NUM_TEMP_SAM][3] = {           1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691   ,       1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691   ,       1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691    ,      1.00655966046382,
        //                                                1.00344659790307   ,      0.999216982224691     ,     1.00655966046382};
	
	MATR GyroScalFac[NUM_TEMP_SAM][3] = {stSetPara.TemperCompenData.GyroAll_2Para.scalefx_40,stSetPara.TemperCompenData.GyroAll_2Para.scalefy_40,stSetPara.TemperCompenData.GyroAll_2Para.scalefz_40,
                                             stSetPara.TemperCompenData.GyroAll_2Para.scalefx_30,stSetPara.TemperCompenData.GyroAll_2Para.scalefy_30,stSetPara.TemperCompenData.GyroAll_2Para.scalefz_30,
                                             stSetPara.TemperCompenData.GyroAll_2Para.scalefx_20,stSetPara.TemperCompenData.GyroAll_2Para.scalefy_20,stSetPara.TemperCompenData.GyroAll_2Para.scalefz_20,
                                             stSetPara.TemperCompenData.GyroAll_2Para.scalefx_10,stSetPara.TemperCompenData.GyroAll_2Para.scalefy_10,stSetPara.TemperCompenData.GyroAll_2Para.scalefz_10,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx0,stSetPara.TemperCompenData.GyroAll_3Para.scalefy0,stSetPara.TemperCompenData.GyroAll_3Para.scalefz0,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx10,stSetPara.TemperCompenData.GyroAll_3Para.scalefy10,stSetPara.TemperCompenData.GyroAll_3Para.scalefz10,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx20,stSetPara.TemperCompenData.GyroAll_3Para.scalefy20,stSetPara.TemperCompenData.GyroAll_3Para.scalefz20,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx30,stSetPara.TemperCompenData.GyroAll_3Para.scalefy30,stSetPara.TemperCompenData.GyroAll_3Para.scalefz30,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx40,stSetPara.TemperCompenData.GyroAll_3Para.scalefy40,stSetPara.TemperCompenData.GyroAll_3Para.scalefz40,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx50,stSetPara.TemperCompenData.GyroAll_3Para.scalefy50,stSetPara.TemperCompenData.GyroAll_3Para.scalefz50,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx60,stSetPara.TemperCompenData.GyroAll_3Para.scalefy60,stSetPara.TemperCompenData.GyroAll_3Para.scalefz60,
                                             stSetPara.TemperCompenData.GyroAll_3Para.scalefx70,stSetPara.TemperCompenData.GyroAll_3Para.scalefy70,stSetPara.TemperCompenData.GyroAll_3Para.scalefz70};

																						
	//MATR GyroInstErr[9] = {         0.999984798269366  ,   -0.000482567258982799   ,    0.00455001309054507,
 //      0.00131782663946164  ,       0.999999377039636   ,   1.88325506988343e-05,
 //     -0.00319987478297328  ,     0.00101346046882315   ,      0.999985453295809};

      	MATR GyroInstErr[9] = {stSetPara.TemperCompenData.GyroNormalPara.MCMatrix00,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix01,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix02,
                               stSetPara.TemperCompenData.GyroNormalPara.MCMatrix10,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix11,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix12,
                               stSetPara.TemperCompenData.GyroNormalPara.MCMatrix20,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix21,stSetPara.TemperCompenData.GyroNormalPara.MCMatrix22};
	
	for(i = 0 ; i < NUM_TEMP_SAM;i++)
	{
		for(j = 0;j < 3 ; j++)	
		{																						
			lp_GyroFullTempCompen -> TempSamPoint[i][j] = GyroTempSamPoint[i][j];
			lp_GyroFullTempCompen -> Bias_Value[i][j] = GyroBias[i][j];
			lp_GyroFullTempCompen -> Scale_Factor[i][j] = GyroScalFac[i][j];
		}
	}
	for(i = 0;i < 9;i++)
	{
		lp_GyroNormalTempCompen -> Misalignment_Compensation_Matrix[i] = GyroInstErr[i];
	}
}
/*********************************************????*******************************************************/
/*????:Acc_Compen_Para_Init_60                                                                         */
/*??????:60?????????????                                                                */
/*???:  Ver 0.1                                                                                         */
/*????                                                                                           */
/*???:                                                                                                  */
/*????:                                                                                                */
/*????:?                                                                                              */
/*????:??                                                                                            */
/*??1:????????????                                                                           */
/*???:?                                                                                                */
/************************************************************************************************************/
void Acc_Compen_Para_Init(p_CmdAccNormalTempCompen lp_AccNormalTempCompen,p_CmdAccFullTempCompen lp_AccFullTempCompen)
{
	IPARA i,j;
	//MATR AccTempSamPoint[NUM_TEMP_SAM][3] ={    -40 ,  -40 ,  -40,
 //                                                    -30 ,  -30 ,  -30,
 //                                                    -20 ,  -20 ,  -20,
 //                                                    -10 ,  -10 ,  -10,
 //                                                      0 ,    0 ,    0,
 //                                                     10 ,   10 ,   10,
 //                                                     20 ,   20 ,   20,
 //                                                     30 ,   30 ,   30,
 //                                                     40 ,   40 ,   40,
 //                                                     50 ,   50 ,   50,
 //                                                     60 ,   60 ,   60,
 //                                                     70 ,   70 ,   70};

 	MATR AccTempSamPoint[NUM_TEMP_SAM][3] ={stSetPara.TemperCompenData.AccAll_0Para.tempx_40,stSetPara.TemperCompenData.AccAll_0Para.tempy_40,stSetPara.TemperCompenData.AccAll_0Para.tempz_40,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx_30,stSetPara.TemperCompenData.AccAll_0Para.tempy_30,stSetPara.TemperCompenData.AccAll_0Para.tempz_30,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx_20,stSetPara.TemperCompenData.AccAll_0Para.tempy_20,stSetPara.TemperCompenData.AccAll_0Para.tempz_20,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx_10,stSetPara.TemperCompenData.AccAll_0Para.tempy_10,stSetPara.TemperCompenData.AccAll_0Para.tempz_10,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx0,stSetPara.TemperCompenData.AccAll_0Para.tempy0,stSetPara.TemperCompenData.AccAll_0Para.tempz0,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx10,stSetPara.TemperCompenData.AccAll_0Para.tempy10,stSetPara.TemperCompenData.AccAll_0Para.tempz10,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx20,stSetPara.TemperCompenData.AccAll_0Para.tempy20,stSetPara.TemperCompenData.AccAll_0Para.tempz20,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx30,stSetPara.TemperCompenData.AccAll_0Para.tempy30,stSetPara.TemperCompenData.AccAll_0Para.tempz30,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx40,stSetPara.TemperCompenData.AccAll_0Para.tempy40,stSetPara.TemperCompenData.AccAll_0Para.tempz40,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx50,stSetPara.TemperCompenData.AccAll_0Para.tempy50,stSetPara.TemperCompenData.AccAll_0Para.tempz50,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx60,stSetPara.TemperCompenData.AccAll_0Para.tempy60,stSetPara.TemperCompenData.AccAll_0Para.tempz60,
                                                stSetPara.TemperCompenData.AccAll_0Para.tempx70,stSetPara.TemperCompenData.AccAll_0Para.tempy70,stSetPara.TemperCompenData.AccAll_0Para.tempz70};
																																						
	//MATR AccBias[NUM_TEMP_SAM][3] = {      -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228,
 //                                              -0.000927622078292207 ,     0.000730380859697485  ,   -0.000656864241890228};

        MATR AccBias[NUM_TEMP_SAM][3] = {stSetPara.TemperCompenData.AccAll_1Para.biasvx_40,stSetPara.TemperCompenData.AccAll_1Para.biasvy_40,stSetPara.TemperCompenData.AccAll_1Para.biasvz_40,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx_30,stSetPara.TemperCompenData.AccAll_1Para.biasvy_30,stSetPara.TemperCompenData.AccAll_1Para.biasvz_30,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx_20,stSetPara.TemperCompenData.AccAll_1Para.biasvy_20,stSetPara.TemperCompenData.AccAll_1Para.biasvz_20,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx_10,stSetPara.TemperCompenData.AccAll_1Para.biasvy_10,stSetPara.TemperCompenData.AccAll_1Para.biasvz_10,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx0,stSetPara.TemperCompenData.AccAll_1Para.biasvy0,stSetPara.TemperCompenData.AccAll_1Para.biasvz0,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx10,stSetPara.TemperCompenData.AccAll_1Para.biasvy10,stSetPara.TemperCompenData.AccAll_1Para.biasvz10,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx20,stSetPara.TemperCompenData.AccAll_1Para.biasvy20,stSetPara.TemperCompenData.AccAll_1Para.biasvz20,
                                         stSetPara.TemperCompenData.AccAll_1Para.biasvx30,stSetPara.TemperCompenData.AccAll_1Para.biasvy30,stSetPara.TemperCompenData.AccAll_1Para.biasvz30,
                                         stSetPara.TemperCompenData.AccAll_2Para.biasvx40,stSetPara.TemperCompenData.AccAll_2Para.biasvy40,stSetPara.TemperCompenData.AccAll_2Para.biasvz40,
                                         stSetPara.TemperCompenData.AccAll_2Para.biasvx50,stSetPara.TemperCompenData.AccAll_2Para.biasvy50,stSetPara.TemperCompenData.AccAll_2Para.biasvz50,
                                         stSetPara.TemperCompenData.AccAll_2Para.biasvx60,stSetPara.TemperCompenData.AccAll_2Para.biasvy60,stSetPara.TemperCompenData.AccAll_2Para.biasvz60,
                                         stSetPara.TemperCompenData.AccAll_2Para.biasvx70,stSetPara.TemperCompenData.AccAll_2Para.biasvy70,stSetPara.TemperCompenData.AccAll_2Para.biasvz70};
	
	//MATR AccScalFac[NUM_TEMP_SAM][3] = {         0.999167662685798  ,       0.999226104220928    ,     0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928    ,     0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928    ,     0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928    ,     0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928    ,     0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235,
 //                                                    0.999167662685798  ,       0.999226104220928   ,      0.999193966236235};

	MATR AccScalFac[NUM_TEMP_SAM][3] = {stSetPara.TemperCompenData.AccAll_2Para.scalefx_40,stSetPara.TemperCompenData.AccAll_2Para.scalefy_40,stSetPara.TemperCompenData.AccAll_2Para.scalefz_40,
                                            stSetPara.TemperCompenData.AccAll_2Para.scalefx_30,stSetPara.TemperCompenData.AccAll_2Para.scalefy_30,stSetPara.TemperCompenData.AccAll_2Para.scalefz_30,
                                            stSetPara.TemperCompenData.AccAll_2Para.scalefx_20,stSetPara.TemperCompenData.AccAll_2Para.scalefy_20,stSetPara.TemperCompenData.AccAll_2Para.scalefz_20,
                                            stSetPara.TemperCompenData.AccAll_2Para.scalefx_10,stSetPara.TemperCompenData.AccAll_2Para.scalefy_10,stSetPara.TemperCompenData.AccAll_2Para.scalefz_10,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx0,stSetPara.TemperCompenData.AccAll_3Para.scalefy0,stSetPara.TemperCompenData.AccAll_3Para.scalefz0,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx10,stSetPara.TemperCompenData.AccAll_3Para.scalefy10,stSetPara.TemperCompenData.AccAll_3Para.scalefz10,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx20,stSetPara.TemperCompenData.AccAll_3Para.scalefy20,stSetPara.TemperCompenData.AccAll_3Para.scalefz20,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx30,stSetPara.TemperCompenData.AccAll_3Para.scalefy30,stSetPara.TemperCompenData.AccAll_3Para.scalefz30,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx40,stSetPara.TemperCompenData.AccAll_3Para.scalefy40,stSetPara.TemperCompenData.AccAll_3Para.scalefz40,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx50,stSetPara.TemperCompenData.AccAll_3Para.scalefy50,stSetPara.TemperCompenData.AccAll_3Para.scalefz50,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx60,stSetPara.TemperCompenData.AccAll_3Para.scalefy60,stSetPara.TemperCompenData.AccAll_3Para.scalefz60,
                                            stSetPara.TemperCompenData.AccAll_3Para.scalefx70,stSetPara.TemperCompenData.AccAll_3Para.scalefy70,stSetPara.TemperCompenData.AccAll_3Para.scalefz70};
																						
	//MATR AccInstErr[9] = {          1.00005496904937  ,     0.00666989446786027    ,    0.0124754687247751,
 //      -0.0110561318110845  ,       0.999925661165111  ,   -0.000305521775196845,
 //       0.0102769934826284   ,    0.00364646231248415 ,         1.00012760360771};

 	MATR AccInstErr[9] = {stSetPara.TemperCompenData.AccNormalPara.MCMatrix00,stSetPara.TemperCompenData.AccNormalPara.MCMatrix01,stSetPara.TemperCompenData.AccNormalPara.MCMatrix02,
                              stSetPara.TemperCompenData.AccNormalPara.MCMatrix10,stSetPara.TemperCompenData.AccNormalPara.MCMatrix11,stSetPara.TemperCompenData.AccNormalPara.MCMatrix12,
                              stSetPara.TemperCompenData.AccNormalPara.MCMatrix20,stSetPara.TemperCompenData.AccNormalPara.MCMatrix21,stSetPara.TemperCompenData.AccNormalPara.MCMatrix22};

 	for(i = 0 ; i < NUM_TEMP_SAM;i++)
	{
		for(j = 0;j < 3 ; j++)	
		{																						
			lp_AccFullTempCompen -> TempSamPoint[i][j] = AccTempSamPoint[i][j];
			lp_AccFullTempCompen -> Bias_Value[i][j] = AccBias[i][j];
			lp_AccFullTempCompen -> Scale_Factor[i][j] = AccScalFac[i][j];
		}				
	}
    for (i = 0; i < 9; i++)
    {
		lp_AccNormalTempCompen -> Misalignment_Compensation_Matrix[i] = AccInstErr[i];
	}
}
/*********************************************����˵��*******************************************************/
/*�������ƣ�GyroCompenCompute                                                                               */
/*�����������������ݲ�������                                                                                */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*���������                                                                                                */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1���ú����������Ϊ��ַ����                                                                           */
/*����ֵ����                                                                                                */
/************************************************************************************************************/
void GyroCompenCompute(p_Compen lp_Compen,p_CmdGyroNormalTempCompen lp_GyroNormalTempCompen,p_CmdGyroFullTempCompen lp_GyroFullTempCompen,p_CmdANNCompenData lp_GyroANNCompenData)
{
//	UINT32 i = 0;
	//��¼��һ֡��������
	lp_Compen -> LastGyro[0] = lp_Compen -> Gyro[0];
	lp_Compen -> LastGyro[1] = lp_Compen -> Gyro[1];
	lp_Compen -> LastGyro[2] = lp_Compen -> Gyro[2];
	//TempSensCompen_60(lp_SenorRaw -> GyroTempRaw,lp_Compen -> GyroTemp);

	GetTempRangeNum(lp_Compen -> GyroTemp,lp_GyroFullTempCompen -> TempSamPoint,lp_Compen -> GyroTempRangeNum);
	//����1sƽ���¶ȼ��¶��ݶȣ��¶Ȳ
	ComputeGyroTempDiff(lp_Compen);
	//RTCompenPara(lp_Compen -> GyroBias,lp_Compen -> GyroTemp,lp_Compen -> GyroTempRangeNum,lp_Compen -> GyroTempSamPoint,lp_Compen -> RTGyroBias);
	
	lp_Compen -> RTGyroBias[0] = ANN_Predict(lp_Compen -> GyroTemp[0], lp_Compen -> GyroTemp_Diff[0],&lp_GyroANNCompenData -> ANNCompen_X);
	lp_Compen -> RTGyroBias[1] = ANN_Predict(lp_Compen -> GyroTemp[1], lp_Compen -> GyroTemp_Diff[1],&lp_GyroANNCompenData -> ANNCompen_Y);
	lp_Compen -> RTGyroBias[2] = ANN_Predict(lp_Compen -> GyroTemp[2], lp_Compen -> GyroTemp_Diff[2],&lp_GyroANNCompenData -> ANNCompen_Z);
	
	RTCompenPara(lp_GyroFullTempCompen -> Scale_Factor,lp_Compen -> GyroTemp,lp_Compen -> GyroTempRangeNum,lp_GyroFullTempCompen -> TempSamPoint,lp_Compen -> RTGyroScalFac);
	//LinerCompen_60(lp_SenorRaw -> GyroRaw,lp_Compen -> RTGyroBias,lp_Compen -> RTGyroScalFac,lp_Compen -> GyroInstErr,d_Gyro);
	LinerCompen_60_ANN_Order(lp_Compen -> GyroRaw,lp_Compen -> RTGyroBias,lp_Compen -> RTGyroScalFac,lp_GyroNormalTempCompen -> Misalignment_Compensation_Matrix,lp_Compen -> Gyro);
	
    lp_Compen->Gyro[0] -= lp_GyroNormalTempCompen->Bias_Correct_Val[0];
    lp_Compen->Gyro[1] -= lp_GyroNormalTempCompen->Bias_Correct_Val[1];
    lp_Compen->Gyro[2] -= lp_GyroNormalTempCompen->Bias_Correct_Val[2];

	//�����������жϴ���
	
	/*if(fabs(lp_Compen -> Gyro[0]) >= THRESHOLD_GYRO)
	{
		lp_Compen -> Gyro[0] = lp_Compen -> LastGyro[0];
		lp_Compen -> Count_GyroErr[0]++;  		
	}
	else
	{			  
		lp_Compen -> Count_GyroErr[0] = 0;
	}
	if(lp_Compen -> Count_GyroErr[0] >= COUNT_IMU_FAIL)
	{
			lp_Compen -> IMU_Valid &=~GYRO_X_VALID; 
	}
	else
	{
			lp_Compen -> IMU_Valid |= GYRO_X_VALID; 
	}
	
	if(fabs(lp_Compen -> Gyro[1]) >= THRESHOLD_GYRO)
	{
		lp_Compen -> Gyro[1] = lp_Compen -> LastGyro[1];
		lp_Compen -> Count_GyroErr[1]++;  		
	}
	else
	{			  
		lp_Compen -> Count_GyroErr[1] = 0;
	}
	if(lp_Compen -> Count_GyroErr[1] >= COUNT_IMU_FAIL)
	{
			lp_Compen -> IMU_Valid &=~GYRO_Y_VALID; 
	}
	else
	{
			lp_Compen -> IMU_Valid |= GYRO_Y_VALID; 
	}
	
	if(fabs(lp_Compen -> Gyro[2]) >= THRESHOLD_GYRO)
	{
		lp_Compen -> Gyro[2] = lp_Compen -> LastGyro[2];
		lp_Compen -> Count_GyroErr[2]++;  		
	}
	else
	{			  
		lp_Compen -> Count_GyroErr[2] = 0;
	}
	if(lp_Compen -> Count_GyroErr[2] >= COUNT_IMU_FAIL)
	{
			lp_Compen -> IMU_Valid &=~GYRO_Z_VALID; 
	}
	else
	{
			lp_Compen -> IMU_Valid |= GYRO_Z_VALID; 
	}    */
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�AccCompenCompute                                                                               */
/*�����������������ݲ�������                                                                                */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*���������                                                                                                */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1���ú����������Ϊ��ַ����                                                                           */
/*����ֵ����                                                                                                */
/************************************************************************************************************/
void AccCompenCompute(p_Compen lp_Compen,p_CmdAccNormalTempCompen lp_AccNormalTempCompen,p_CmdAccFullTempCompen lp_AccFullTempCompen,p_CmdANNCompenData lp_AccANNCompenData)
{
	//��¼��һ֡�ӱ�����
	lp_Compen -> LastAcc[0] = lp_Compen -> Acc[0];
	lp_Compen -> LastAcc[1] = lp_Compen -> Acc[1];
	lp_Compen -> LastAcc[2] = lp_Compen -> Acc[2];
	//TempSensCompen_60(lp_SenorRaw -> AccTempRaw,lp_Compen -> AccTemp);
	GetTempRangeNum(lp_Compen -> AccTemp,lp_AccFullTempCompen -> TempSamPoint,lp_Compen -> AccTempRangeNum);
	//RTCompenPara(lp_Compen -> AccBias,lp_Compen -> AccTemp,lp_Compen -> AccTempRangeNum,lp_Compen -> AccTempSamPoint,lp_Compen -> RTAccBias);
	ComputeAccTempDiff(lp_Compen);
	lp_Compen -> RTAccBias[0] = ANN_Predict(lp_Compen -> AccTemp[0],lp_Compen->AccTemp_Diff[0],&lp_AccANNCompenData -> ANNCompen_X);
	lp_Compen -> RTAccBias[1] = ANN_Predict(lp_Compen -> AccTemp[1],lp_Compen->AccTemp_Diff[1],&lp_AccANNCompenData -> ANNCompen_Y);
	lp_Compen -> RTAccBias[2] = ANN_Predict(lp_Compen -> AccTemp[2],lp_Compen->AccTemp_Diff[2],&lp_AccANNCompenData -> ANNCompen_Z);
	RTCompenPara(lp_AccFullTempCompen -> Scale_Factor,lp_Compen -> AccTemp,lp_Compen -> AccTempRangeNum,lp_AccFullTempCompen -> TempSamPoint,lp_Compen -> RTAccScalFac);
	
	LinerCompen_60_ANN_Order(lp_Compen->AccRaw, lp_Compen->RTAccBias, lp_Compen-> RTAccScalFac, lp_AccNormalTempCompen->Misalignment_Compensation_Matrix, lp_Compen -> Acc);
	
    lp_Compen->Acc[0] -= lp_AccNormalTempCompen->Bias_Correct_Val[0];
    lp_Compen->Acc[1] -= lp_AccNormalTempCompen->Bias_Correct_Val[1];
    lp_Compen->Acc[2] -= lp_AccNormalTempCompen->Bias_Correct_Val[2];

	//�ӱ��������жϴ���
	/*if(fabs(lp_Compen -> Acc[0]) >= THRESHOLD_ACC)
	{
		lp_Compen -> Acc[0] = lp_Compen -> LastAcc[0];
		lp_Compen -> Count_AccErr[0]++;  		
	}
	else
	{			  
		lp_Compen -> Count_AccErr[0] = 0;
	}
	if(lp_Compen -> Count_AccErr[0] >= COUNT_IMU_FAIL)
	{
		lp_Compen -> IMU_Valid &=~ACC_X_VALID; 
	}
	else
	{
		lp_Compen -> IMU_Valid |= ACC_X_VALID; 
	}
	
	if(fabs(lp_Compen -> Acc[1]) >= THRESHOLD_ACC)
	{
		lp_Compen -> Acc[1] = lp_Compen -> LastAcc[1];
		lp_Compen -> Count_AccErr[1]++;  		
	}
	else
	{			  
		lp_Compen -> Count_AccErr[1] = 0;
	}
	if(lp_Compen -> Count_AccErr[1] >= COUNT_IMU_FAIL)
	{
		lp_Compen -> IMU_Valid &=~ACC_Y_VALID; 
	}
	else
	{
		lp_Compen -> IMU_Valid |= ACC_Y_VALID; 
	}
	
	if(fabs(lp_Compen -> Acc[2]) >= THRESHOLD_ACC)
	{
		lp_Compen -> Acc[2] = lp_Compen -> LastAcc[2];
		lp_Compen -> Count_AccErr[2]++;  		
	}
	else
	{			  
		lp_Compen -> Count_AccErr[2] = 0;
	}
	if(lp_Compen -> Count_AccErr[2] >= COUNT_IMU_FAIL)
	{
		lp_Compen -> IMU_Valid &=~ACC_Z_VALID; 
	}
	else
	{
		lp_Compen -> IMU_Valid |= ACC_Z_VALID; 
	}*/
}




/****************************************************************************************************/
/*TempRangeNum                                                                                    */
/*                                                                            */
/*  Ver 0.1                                                                                         */
/*/2014  11  24  13: 30                                                                 */
/*                                                                                              */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void GetTempRangeNum(VEC Temp[3],MATR TempSamPoint[NUM_TEMP_SAM][3],IPARA TempRangeNum[3])
{
    IPARA i,j;
    for(i = 0; i < 3; i++)
    {
        //0NUM_TEMP_SAM0NUM_TEMP_SAM
        TempRangeNum[i] = NUM_TEMP_SAM;
        for(j = 0; j < NUM_TEMP_SAM; j++)
        {
            if((Temp[i] <= TempSamPoint[j][i]))
            {
                TempRangeNum[i] = j;
                break;
            }
        }
        if(TempRangeNum[i] == 0)
        {
            //
            TempRangeNum[i] ++;
        }
        
        if(TempRangeNum[i] == NUM_TEMP_SAM)
        {
            //
            TempRangeNum[i] --;
        }
    }
}




/****************************************************************************************************/
/*ComputeGyroTempDiff                                                                             */
/*                                                                        */
/*  Ver 0.1                                                                                         */
/*/2014  11  24  13: 30                                                                 */
/*                                                                                              */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void ComputeGyroTempDiff(p_Compen lp_Compen)
{
    UINT32 i = 0,j = 0;
    //DPARA GyroTemp_Move_Sum[3] = { 0.0,0.0,0.0 };
    DPARA GyroTemp_Move_Mean[3] = { 0.0,0.0,0.0};
    for (i = 0; i < 3; i++)
    {
        lp_Compen -> GyroTemp_1s_Sum[i] += lp_Compen -> GyroTemp[i];
    }
    lp_Compen -> GyroCount_1s++;
    if (lp_Compen -> GyroCount_1s == NUM_1S_COUNT)
    {
        for (i = 0; i < 3; i++)
        {
            lp_Compen -> GyroTemp_1s_Mean[i] = lp_Compen -> GyroTemp_1s_Sum[i] / NUM_1S_COUNT;
            lp_Compen -> GyroTemp_1s_Sum[i] = 0.0;
            //lp_Compen -> GyroTemp_Move_Mean_Buffer[i][lp_Compen -> GyroCircle_Count] = lp_Compen -> GyroTemp_1s_Mean[i];
            lp_Compen->GyroTemp_Move_Mean_Buffer[i][lp_Compen->GyroCircle_Count] = lp_Compen->GyroTemp[i];//lp_Compen->GyroTemp_1s_Mean[i];
            if (lp_Compen ->isGyroMove_Mean_BufferInit != YES)
            {
                for (j = 0; j < lp_Compen -> GyroCircle_Count + 1; j++)
                {
                    GyroTemp_Move_Mean[i] += lp_Compen -> GyroTemp_Move_Mean_Buffer[i][j];
                }
                GyroTemp_Move_Mean[i] /= (lp_Compen -> GyroCircle_Count + 1);
            }
            else
            {
                for (j = 0; j < NUM_TEMP_DIFF_BUFFER; j++)
                {
                    GyroTemp_Move_Mean[i] += lp_Compen -> GyroTemp_Move_Mean_Buffer[i][j];
                }
                GyroTemp_Move_Mean[i] /= NUM_TEMP_DIFF_BUFFER;
            }
        }
        lp_Compen -> GyroCount_1s = 0;
        lp_Compen -> GyroCircle_Count++;
        if (lp_Compen -> GyroCircle_Count == NUM_TEMP_DIFF_BUFFER)
        {
            lp_Compen -> isGyroMove_Mean_BufferInit = YES;
            lp_Compen -> GyroCircle_Count = 0;
        }
        for (i = 0;i < 3;i++)
        {
            //lp_Compen -> GyroTemp_Diff[i] = lp_Compen -> GyroTemp_1s_Mean[i] - GyroTemp_Move_Mean[i];
            lp_Compen->GyroTemp_Diff[i] = lp_Compen->GyroTemp[i] - GyroTemp_Move_Mean[i];
        }
    }
}





/****************************************************************************************************/
/*ComputeAccTempDiff                                                                             */
/*                                                                        */
/*  Ver 0.1                                                                                         */
/*/2014  11  24  13: 30                                                                 */
/*                                                                                              */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void ComputeAccTempDiff(p_Compen lp_Compen)
{
    IPARA i = 0, j = 0;
    //DPARA AccTemp_Move_Sum[3] = { 0.0,0.0,0.0 };
    DPARA AccTemp_Move_Mean[3] = { 0.0,0.0,0.0 };
    for (i = 0; i < 3; i++)
    {
        lp_Compen->AccTemp_1s_Sum[i] += lp_Compen->AccTemp[i];
    }
    lp_Compen -> AccCount_1s++;
    if (lp_Compen -> AccCount_1s == NUM_1S_COUNT)
    {
        for (i = 0; i < 3; i++)
        {
            lp_Compen -> AccTemp_1s_Mean[i] = lp_Compen->AccTemp_1s_Sum[i] / NUM_1S_COUNT;
            lp_Compen -> AccTemp_1s_Sum[i] = 0.0;
            //lp_Compen -> AccTemp_Move_Mean_Buffer[i][lp_Compen->AccCircle_Count] = lp_Compen -> AccTemp_1s_Mean[i];
            lp_Compen->AccTemp_Move_Mean_Buffer[i][lp_Compen->AccCircle_Count] = lp_Compen->AccTemp[i];//lp_Compen->AccTemp_1s_Mean[i];
            if (lp_Compen ->isAccMove_Mean_BufferInit != YES)
            {
                for (j = 0; j < lp_Compen -> AccCircle_Count + 1; j++)
                {
                    AccTemp_Move_Mean[i] += lp_Compen -> AccTemp_Move_Mean_Buffer[i][j];

                }
                AccTemp_Move_Mean[i] /= (lp_Compen -> AccCircle_Count + 1);
            }
            else
            {
                for (j = 0; j < NUM_TEMP_DIFF_BUFFER; j++)
                {
                    AccTemp_Move_Mean[i] += lp_Compen -> AccTemp_Move_Mean_Buffer[i][j];
                }
                AccTemp_Move_Mean[i] /= NUM_TEMP_DIFF_BUFFER;
            }
        }
        lp_Compen->AccCount_1s = 0;
        lp_Compen->AccCircle_Count++;
        if (lp_Compen->AccCircle_Count == NUM_TEMP_DIFF_BUFFER)
        {
            lp_Compen->isAccMove_Mean_BufferInit = YES;
            lp_Compen->AccCircle_Count = 0;
        }
        for (i = 0; i < 3; i++)
        {
            //lp_Compen->AccTemp_Diff[i] = lp_Compen->AccTemp_1s_Mean[i] - AccTemp_Move_Mean[i];
            lp_Compen->AccTemp_Diff[i] = lp_Compen->AccTemp[i] - AccTemp_Move_Mean[i];
        }
    }
}




/****************************************************************************************************/
/*RTCompenPara                                                                                    */
/*                                                                            */
/*  Ver 0.1                                                                                         */
/*/2014  11  24  13: 30                                                                 */
/*                                                                                              */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void RTCompenPara(MATR Para[NUM_TEMP_SAM][3],VEC Temp[3],IPARA TempRangeNum[3],MATR TempSamPoint[NUM_TEMP_SAM][3],VEC RTPara[3])
{
    IPARA i = 0;
    for(i = 0; i < 3; i++)
    {
        //
        RTPara[i] = Para[TempRangeNum[i] - 1][i] + (Para[TempRangeNum[i]][i] - Para[TempRangeNum[i] - 1][i]) / (TempSamPoint[TempRangeNum[i]][i] - TempSamPoint[TempRangeNum[i] - 1][i]) * (Temp[i] - TempSamPoint[TempRangeNum[i] - 1][i]);
    }
}




/****************************************************************************************************/
/*LinerCompen_60                                                                                  */
/*                                                                    */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void LinerCompen_60(INT32 Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3])
{
    IPARA i = 0;
    DRAW RawTemp[3];
    //
    for(i = 0; i < 3 ; i++)
    {
        CompenData[i] = 0.0;     //
        RawTemp[i] = (DPARA)Raw[i] - RTBias[i];
        RawTemp[i] /= RTScalFac[i];
    }
    Mat_Mul(InstErr, RawTemp, CompenData, 3, 3, 1);
}




/****************************************************************************************************/
/*LinerCompen_60_ANN_Order                                                                        */
/*,                                        */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                 */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void LinerCompen_60_ANN_Order(DPARA Raw[3],DPARA RTBias[3],DPARA RTScalFac[3],MATR InstErr[9],DPARA CompenData[3])
{
    IPARA i = 0;
    DRAW RawTemp[3] = {0.0};
    //
    for(i = 0; i < 3 ; i++)
    {
        CompenData[i] = 0.0;     //
        RawTemp[i] = (DRAW)Raw[i] / RTScalFac[i];
        RawTemp[i] = RawTemp[i] - RTBias[i];
    }
    //
    Mat_Mul(InstErr, RawTemp, CompenData, 3, 3, 1);
}




/****************************************************************************************************/
/*LComputeLeverArmAcc                                                                              */
/*:                                             */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void ComputeLeverArmAcc(DPARA Gyro[3],MATR AccLeverArm[9],DPARA Acc_r[3])
{
	DPARA r_Gyro[3];//���ȱ�ʾ����������
	IPARA i;
	
	for(i = 0;i < 3;i++)
	{
			r_Gyro[i] = Gyro[i] * D2R;//תΪ���ȵ�λ���˴����ݾ��岹�������ĵ�λ���е���
	}
	Acc_r[0] =  - AccLeverArm[0] * (r_Gyro[1] * r_Gyro[1] + r_Gyro[2] * r_Gyro[2]) + AccLeverArm[1] * r_Gyro[0] * r_Gyro[1] + AccLeverArm[2] * r_Gyro[0] * r_Gyro[2];
	Acc_r[1] =  + AccLeverArm[3] * r_Gyro[0] * r_Gyro[1] - AccLeverArm[4] * (r_Gyro[0] * r_Gyro[0] + r_Gyro[2] * r_Gyro[2]) + AccLeverArm[5] * r_Gyro[1] * r_Gyro[2];
	Acc_r[1] =  + AccLeverArm[6] * r_Gyro[0] * r_Gyro[2] + AccLeverArm[7] * r_Gyro[1] * r_Gyro[2] - AccLeverArm[8] * (r_Gyro[0] * r_Gyro[0] + r_Gyro[1] * r_Gyro[1]);
}




/****************************************************************************************************/
/*LComputeLeverArmVn                                                                               */
/*:n                        */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1GPSr_Webb                                              */
/*                                                                                                */
/************************************************************************************************************/
/*void ComputeLeverArmVn(MATR Cnb[9],DPARA r_Webb[3],MATR VnLeverArm[3],VEL Vn_r[3])
{
    MATR Cbn[9];
    VEL Vb_r[3];
    //Vn_r
    Vec_Cross(r_Webb, VnLeverArm, Vb_r);
    Mat_Tr(Cnb, Cbn, 3, 3);
    Mat_Mul(Cbn, Vb_r, Vn_r, 3, 3, 1);
}*/




/****************************************************************************************************/
/*InerLeverArmCompen                                                                              */
/*                                                                      */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1,Gyro[3]/s,r_Gyro[3]rad/s                             */
/*                                                                                                */
/************************************************************************************************************/
void InerLeverArmCompen(DPARA Gyro[3],DPARA Acc[3],MATR AccLeverArm[9])
{
    DPARA Acc_r[3];
    IPARA i;

    ComputeLeverArmAcc(Gyro,AccLeverArm,Acc_r);//

    for(i = 0;i < 3; i++)
    {
        Acc[i] -= Acc_r[i];//
    }
}





/****************************************************************************************************/
/*ComputeColdStartCompenVal                                                                       */
/*60                                                */
/*  Ver 0.1                                                                                         */
/*/                                                                                           */
/*                                                                                                  */
/*                                                                                                */
/*                                                                                              */
/*                                                                                            */
/*1                                                                           */
/*                                                                                                */
/************************************************************************************************************/
void ComputeColdStartCompenVal(TIME Time, DPARA Para[2],DPARA *Output)
{
    *Output = Time * Para[0] + Para[1];
}
