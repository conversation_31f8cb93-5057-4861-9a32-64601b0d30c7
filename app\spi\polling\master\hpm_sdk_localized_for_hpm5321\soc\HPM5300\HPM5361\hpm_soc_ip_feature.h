/*
 * Copyright (c) 2024 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */
#ifndef HPM_SOC_IP_FEATURE_H
#define HPM_SOC_IP_FEATURE_H

/* GPTMR related feature */
#define HPM_IP_FEATURE_GPTMR_MONITOR 1
#define HPM_IP_FEATURE_GPTMR_OP_MODE 1

/* UART related feature */
#define HPM_IP_FEATURE_UART_RX_IDLE_DETECT 1
#define HPM_IP_FEATURE_UART_FCRR 1
#define HPM_IP_FEATURE_UART_RX_EN 1
#define HPM_IP_FEATURE_UART_E00018_FIX 1
#define HPM_IP_FEATURE_UART_9BIT_MODE 1
#define HPM_IP_FEATURE_UART_ADDR_MATCH 1
#define HPM_IP_FEATURE_UART_TRIG_MODE 1
#define HPM_IP_FEATURE_UART_FINE_FIFO_THRLD 1
#define HPM_IP_FEATURE_UART_IIR2 1

/* I2C related feature */
#define HPM_IP_FEATURE_I2C_SUPPORT_RESET 1

/* SPI related feature */
#define HPM_IP_FEATURE_SPI_NEW_TRANS_COUNT 1
#define HPM_IP_FEATURE_SPI_CS_SELECT 1
#define HPM_IP_FEATURE_SPI_SUPPORT_DIRECTIO 1

/* PWM related feature */
#define HPM_IP_FEATURE_PWM_COUNTER_RESET 1

/* ADC16 related feature */
#define HPM_IP_FEATURE_ADC16_HAS_MOT_EN 1

#endif /* HPM_SOC_IP_FEATURE_H */