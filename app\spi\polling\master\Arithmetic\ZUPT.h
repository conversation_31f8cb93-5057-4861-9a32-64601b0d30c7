//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// 文件名称：ZUPT.h
// 文件标识：
// 文件摘要：ZUPT静止检测功能头文件
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2025.01.15
//---------------------------------------------------------
#ifndef _ZUPT_H
#define _ZUPT_H

#include <stdint.h>
#include "EXTERNGLOBALDATA.h"
#include "arithmetic.h"

/**
 * @brief 初始化ZUPT结构体
 * @param lp_ZUPT 指向ZUPT结构体的指针
 */
void ZUPTInit(p_ZUPT lp_ZUPT);

/**
 * @brief ZUPT静止检测函数
 * @param lp_combineData 指向IMU数据结构体的指针
 * @note 调用条件：初始零偏估计已完成
 */
void ZUPTDetection(p_CombineDataTypeDef lp_combineData);

/**
 * @brief 获取ZUPT静止检测标志
 * @return int 1-静止状态，0-运动状态或窗口未填满
 */
int GetZUPTFlag(void);

/**
 * @brief 获取ZUPT检测是否有效
 * @return BOOL 1-检测有效（窗口已填满），0-检测无效（窗口未填满）
 */
BOOL GetZUPTValid(void);

/**
 * @brief 获取ZUPT统计信息
 * @param acc_mean 加速度均值数组（可为NULL）
 * @param gyro_mean 角速度均值数组（可为NULL）
 * @param acc_variance 加速度方差数组（可为NULL）
 * @param gyro_variance 角速度方差数组（可为NULL）
 */
void GetZUPTStatistics(DPARA *acc_mean, DPARA *gyro_mean, DPARA *acc_variance, DPARA *gyro_variance);

/**
 * @brief 获取ZUPT合成幅值统计信息
 * @param acc_magnitude_mean 加速度合成幅值均值（可为NULL）
 * @param gyro_magnitude_mean 角速度合成幅值均值（可为NULL）
 * @param acc_magnitude_variance 加速度合成幅值方差（可为NULL）
 * @param gyro_magnitude_variance 角速度合成幅值方差（可为NULL）
 */
void GetZUPTMagnitudeStatistics(DPARA *acc_magnitude_mean, DPARA *gyro_magnitude_mean,
                                DPARA *acc_magnitude_variance, DPARA *gyro_magnitude_variance);

/**
 * @brief ZUPT功能测试函数
 * @note 用于验证ZUPT功能的正确性
 */
void TestZUPTFunction(void);

/**
 * @brief 获取ZUPT调试信息
 * @param detection_count 检测次数计数（可为NULL）
 * @param static_count 静止状态计数（可为NULL）
 * @param dynamic_count 运动状态计数（可为NULL）
 */
void GetZUPTDebugInfo(COUNT *detection_count, COUNT *static_count, COUNT *dynamic_count);

/**
 * @brief 设置ZUPT检测阈值
 * @param acc_threshold 加速度方差阈值
 * @param gyro_threshold 角速度方差阈值
 */
void SetZUPTThresholds(DPARA acc_threshold, DPARA gyro_threshold);

/**
 * @brief 重置ZUPT统计信息
 */
void ResetZUPTStatistics(void);

/**
 * @brief ZUPT角增量约束函数
 * @param lp_DelSenbb 指向角增量数组的指针（输入/输出）
 * @note 调用位置：纯惯导航阶段、ComputeDelSenbb调用后
 * @note 调用条件：ZUPT标志位有效时
 */
void ZUPTAngleConstraint(DELANG lp_DelSenbb[3]);

/**
 * @brief ZUPT角增量约束功能测试函数
 * @note 用于验证ZUPT角增量约束功能的正确性
 */
void TestZUPTAngleConstraint(void);

#endif //_ZUPT_H
