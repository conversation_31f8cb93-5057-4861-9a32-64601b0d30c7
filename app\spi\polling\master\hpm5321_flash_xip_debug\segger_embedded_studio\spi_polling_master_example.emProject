<!DOCTYPE CrossStudio_Project_File>
<solution Name="spi_polling_master_example" target="20" version="2">
  <configuration
    Name="Common"
    c_preprocessor_definitions="FLASH_XIP=1;HPMSOC_HAS_HPMSDK_GPIO=y;HPMSOC_HAS_HPMSDK_PLIC=y;HPMSOC_HAS_HPMSDK_MCHTMR=y;HPMSOC_HAS_HPMSDK_PLICSW=y;HPMSOC_HAS_HPMSDK_GPTMR=y;HPMSOC_HAS_HPMSDK_UART=y;HPMSOC_HAS_HPMSDK_I2C=y;HPMSOC_HAS_HPMSDK_SPI=y;HPMSOC_HAS_HPMSDK_CRC=y;HPMSOC_HAS_HPMSDK_TSNS=y;HPMSOC_HAS_HPMSDK_MBX=y;HPMSOC_HAS_HPMSDK_EWDG=y;HPMSOC_HAS_HPMSDK_DMAMUX=y;HPMSOC_HAS_HPMSDK_DMAV2=y;HPMSOC_HAS_HPMSDK_GPIOM=y;HPMSOC_HAS_HPMSDK_MCAN=y;HPMSOC_HAS_HPMSDK_PTPC=y;HPMSOC_HAS_HPMSDK_QEIV2=y;HPMSOC_HAS_HPMSDK_QEO=y;HPMSOC_HAS_HPMSDK_MMC=y;HPMSOC_HAS_HPMSDK_PWM=y;HPMSOC_HAS_HPMSDK_RDC=y;HPMSOC_HAS_HPMSDK_PLB=y;HPMSOC_HAS_HPMSDK_SYNT=y;HPMSOC_HAS_HPMSDK_SEI=y;HPMSOC_HAS_HPMSDK_TRGM=y;HPMSOC_HAS_HPMSDK_USB=y;HPMSOC_HAS_HPMSDK_SDP=y;HPMSOC_HAS_HPMSDK_SEC=y;HPMSOC_HAS_HPMSDK_MON=y;HPMSOC_HAS_HPMSDK_RNG=y;HPMSOC_HAS_HPMSDK_OTP=y;HPMSOC_HAS_HPMSDK_KEYM=y;HPMSOC_HAS_HPMSDK_ADC16=y;HPMSOC_HAS_HPMSDK_DAC=y;HPMSOC_HAS_HPMSDK_OPAMP=y;HPMSOC_HAS_HPMSDK_ACMP=y;HPMSOC_HAS_HPMSDK_SYSCTL=y;HPMSOC_HAS_HPMSDK_IOC=y;HPMSOC_HAS_HPMSDK_PLLCTLV2=y;HPMSOC_HAS_HPMSDK_PPOR=y;HPMSOC_HAS_HPMSDK_PCFG=y;HPMSOC_HAS_HPMSDK_PGPR=y;HPMSOC_HAS_HPMSDK_PDGO=y;HPMSOC_HAS_HPMSDK_PMP=y;"
    debug_cpu_registers_file="..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_ses_riscv_cpu_regs.xml"
    debug_register_definition_file="..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\hpm_ses_reg.xml"
    debug_restrict_memory_access="No"
    gdb_server_write_timeout="300"
    link_symbol_definitions="_flash_size=1M;" />
  <configuration
    Name="Debug"
    c_preprocessor_definitions="DEBUG"
    gcc_debugging_level="Level 3"
    gcc_optimization_level="None"
    gdb_server_allow_memory_access_during_execution="Yes"
    gdb_server_ignore_checksum_errors="No"
    gdb_server_register_access="General and Individual" />
  <configuration
    Name="Release"
    c_preprocessor_definitions="NDEBUG"
    gcc_debugging_level="None"
    gcc_omit_frame_pointer="Yes"
    gcc_optimization_level="Level 1" />
  <project Name="spi_polling_master_example - hpm5321">
    <configuration
      LIBRARY_IO_TYPE="STD"
      Name="Common"
      RISCV_TOOLCHAIN_VARIANT="Standard"
      arm_linker_heap_size="0x4000"
      arm_linker_no_warn_on_mismatch="Yes"
      arm_linker_stack_size="0x4000"
      arm_linker_variant="SEGGER"
      arm_rtl_variant="SEGGER"
      build_generic_options_file_name=""
      build_output_file_name="$(OutDir)/demo$(EXE)"
      c_additional_options=""
      c_user_include_directories="../../hpm_sdk_localized_for_hpm5321/arch;../../../../../../boards/hpm5321;../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361;../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/ip;../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains;../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot;../../hpm_sdk_localized_for_hpm5321/drivers/inc;../../hpm_sdk_localized_for_hpm5321/utils;../../hpm_sdk_localized_for_hpm5321/components/debug_console;../build_tmp/generated/include;../../src;../../Drv;../../Arithmetic"
      debug_target_connection="GDB Server"
      gcc_all_warnings_command_line_options="-Wall;-Wextra;-Wno-format"
      gcc_cplusplus_language_standard="c++11"
      gcc_enable_all_warnings="Yes"
      gdb_server_autostart_server="Yes"
      gdb_server_command_line="E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/openocd/openocd.exe -f $(ProjectDir)/../../hpm_sdk_localized_for_hpm5321/boards/openocd/probes/ft2232.cfg -f $(ProjectDir)/../../hpm_sdk_localized_for_hpm5321/boards/openocd/soc/hpm5300.cfg -f E:/2014902/HPM/HPM5331/MEMS-HPM5331/Smi270/boards/hpm5321/hpm5321.cfg"
      gdb_server_port="3333"
      gdb_server_reset_command="reset halt"
      gdb_server_type="Custom"
      heap_size="0x4000"
      libcxx="Yes"
      link_linker_script_file="..\..\hpm_sdk_localized_for_hpm5321\soc\HPM5300\HPM5361\toolchains\segger\flash_xip.icf"
      linker_output_format="bin"
      linker_printf_fmt_level="int"
      linker_printf_fp_enabled="Float"
      linker_printf_wchar_enabled="No"
      linker_printf_width_precision_supported="Yes"
      linker_scanf_character_group_matching_enabled="No"
      linker_scanf_fmt_level="int"
      linker_scanf_fp_enabled="No"
      post_build_command="&quot;$(OBJDUMP)&quot; -S -d &quot;$(OutDir)/demo$(EXE)&quot; &gt; &quot;$(OutDir)/demo.asm&quot;"
      project_directory=""
      project_type="Executable"
      rv_abi="ilp32"
      rv_arch_ext=""
      rv_arch_zba="Yes"
      rv_arch_zbb="Yes"
      rv_arch_zbc="Yes"
      rv_arch_zbs="Yes"
      rv_arch_zicsr="Yes"
      rv_arch_zifencei="Yes"
      rv_architecture="rv32imac"
      rv_debug_extensions="None"
      rv_toolchain_prefix=""
      stack_size="0x4000"
      target_device_name="HPM5361xCBx" />
    <configuration
      Name="Debug"
      debug_target_connection="J-Link"
      gdb_server_command_line="E:/2014902/HPM/HPM6750/sdk_env_v1.6.01/tools/openocd/openocd.exe -f $(ProjectDir)/../../hpm_sdk_localized_for_hpm5321/boards/openocd/probes/ft2232.cfg -f $(ProjectDir)/../../hpm_sdk_localized_for_hpm5321/boards/openocd/soc/hpm5300.cfg -f $(ProjectDir)/../../../../../../boards/hpm5321/hpm5321.cfg"
      link_linker_script_file="$(ProjectDir)/flash_xip.icf" />
    <folder Name="application">
      <folder Name="Arithmetic">
        <file file_name="../../arithmetic/align.c" />
        <file file_name="../../arithmetic/arithmetic.c" />
        <file file_name="../../arithmetic/navi.c" />
        <file file_name="../../Arithmetic/ZUPT.c" />
      </folder>
      <folder Name="Drv">
        <file file_name="../../Drv/flash.c" />
        <file file_name="../../Drv/spi.c" />
        <file file_name="../../Drv/Timer.c" />
        <file file_name="../../Drv/uart_dma.c" />
        <file file_name="../../Drv/Uart_Irq.c" />
      </folder>
      <folder Name="src">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/application/src" />
        <file file_name="../../src/AnnTempCompen.c" />
        <file file_name="../../src/compen.c" />
        <file file_name="../../src/FirmwareUpdateFile.c" />
        <file file_name="../../src/main.c" />
        <file file_name="../../src/matvecmath.c" />
        <file file_name="../../src/protocol.c" />
        <file file_name="../../src/SetParaBao.c" />
        <file file_name="../../src/Smi240.c" />
        <file file_name="../../src/Smi980.c" />
      </folder>
    </folder>
    <folder Name="boards">
      <folder Name="hpm5321">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/boards/hpm5321" />
        <file file_name="..\..\..\..\..\..\boards\hpm5321\board.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/board.c$(OBJ)" />
        </file>
        <file file_name="..\..\..\..\..\..\boards\hpm5321\pinmux.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/pinmux.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="components">
      <folder Name="debug_console">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/components/debug_console" />
        <file file_name="../../hpm_sdk_localized_for_hpm5321/components/debug_console/hpm_debug_console.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_debug_console.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="drivers">
      <folder Name="src">
        <configuration
          Name="Common"
          build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/drivers/src" />
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_acmp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_acmp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_adc16_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_adc16_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_crc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_crc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dac_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_dac_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_dmav2_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_dmav2_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_enc_pos_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_enc_pos_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ewdg_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_ewdg_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gpio_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_gpio_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_gptmr_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_gptmr_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_i2c_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_i2c_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mcan_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_mcan_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mchtmr_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_mchtmr_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_mmc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_mmc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_opamp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_opamp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pcfg_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pcfg_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_plb_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_plb_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pllctlv2_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pllctlv2_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pmp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pmp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_ptpc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_ptpc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_pwm_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_pwm_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeiv2_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_qeiv2_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_qeo_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_qeo_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rdc_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_rdc_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_rng_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_rng_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sdp_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_sdp_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_sei_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_sei_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_spi_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_spi_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_tsns_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_tsns_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_uart_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_uart_drv.c$(OBJ)" />
        </file>
        <file file_name="../../hpm_sdk_localized_for_hpm5321/drivers/src/hpm_usb_drv.c">
          <configuration
            Name="Common"
            build_object_file_name="$(IntDir)/hpm_usb_drv.c$(OBJ)" />
        </file>
      </folder>
    </folder>
    <folder Name="soc">
      <folder Name="HPM5300">
        <folder Name="HPM5361">
          <configuration
            Name="Common"
            build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM5300/HPM5361" />
          <folder Name="boot">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM5300/HPM5361/boot" />
            <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/boot/hpm_bootheader.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/hpm_bootheader.c$(OBJ)" />
            </file>
          </folder>
          <folder Name="toolchains">
            <configuration
              Name="Common"
              build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM5300/HPM5361/toolchains" />
            <folder Name="segger">
              <configuration
                Name="Common"
                build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/soc/HPM5300/HPM5361/toolchains/segger" />
              <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/segger/startup.s">
                <configuration
                  Name="Common"
                  build_object_file_name="$(IntDir)/startup.s$(OBJ)" />
              </file>
            </folder>
            <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/reset.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/reset.c$(OBJ)" />
            </file>
            <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/toolchains/trap.c">
              <configuration
                Name="Common"
                build_object_file_name="$(IntDir)/trap.c$(OBJ)" />
            </file>
          </folder>
          <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_clock_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_clock_drv.c$(OBJ)" />
          </file>
          <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_l1c_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_l1c_drv.c$(OBJ)" />
          </file>
          <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_otp_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_otp_drv.c$(OBJ)" />
          </file>
          <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/hpm_sysctl_drv.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/hpm_sysctl_drv.c$(OBJ)" />
          </file>
          <file file_name="../../hpm_sdk_localized_for_hpm5321/soc/HPM5300/HPM5361/system.c">
            <configuration
              Name="Common"
              build_object_file_name="$(IntDir)/system.c$(OBJ)" />
          </file>
        </folder>
      </folder>
    </folder>
    <folder Name="utils">
      <configuration
        Name="Common"
        build_intermediate_directory="Output/$(Configuration)/Obj/$(ProjectName)/utils" />
      <file file_name="../../hpm_sdk_localized_for_hpm5321/utils/hpm_crc32.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_crc32.c$(OBJ)" />
      </file>
      <file file_name="../../hpm_sdk_localized_for_hpm5321/utils/hpm_ffssi.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_ffssi.c$(OBJ)" />
      </file>
      <file file_name="../../hpm_sdk_localized_for_hpm5321/utils/hpm_swap.c">
        <configuration
          Name="Common"
          build_object_file_name="$(IntDir)/hpm_swap.c$(OBJ)" />
      </file>
    </folder>
  </project>
</solution>
