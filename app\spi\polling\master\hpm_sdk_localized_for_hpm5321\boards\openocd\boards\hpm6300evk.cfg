# Copyright (c) 2021 HPMicro
# SPDX-License-Identifier: BSD-3-Clause

flash bank xpi0 hpm_xpi 0x80000000 0x1000000 1 1 $_TARGET0 0xF3040000

proc init_clock {} {
    $::_TARGET0 riscv dmi_write 0x39 0xF4002000
    $::_TARGET0 riscv dmi_write 0x3C 0x1

    $::_TARGET0 riscv dmi_write 0x39 0xF4002000
    $::_TARGET0 riscv dmi_write 0x3C 0x2

    $::_TARGET0 riscv dmi_write 0x39 0xF4000800
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000810
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000820
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000830
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF
    echo "clocks has been enabled!"
}

proc init_sdram { } {
# configure femc frequency
# 166Mhz pll0_clk1: 333Mhz divide by 2
    $::_TARGET0 riscv dmi_write 0x39 0xF4001808
    $::_TARGET0 riscv dmi_write 0x3C 0x201

    # PA25
    $::_TARGET0 riscv dmi_write 0x39 0xF40400C8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA26
    $::_TARGET0 riscv dmi_write 0x39 0xF40400D0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA27
    $::_TARGET0 riscv dmi_write 0x39 0xF40400D8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA28
    $::_TARGET0 riscv dmi_write 0x39 0xF40400E0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA29
    $::_TARGET0 riscv dmi_write 0x39 0xF40400E8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA30
    $::_TARGET0 riscv dmi_write 0x39 0xF40400F0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PA31
    $::_TARGET0 riscv dmi_write 0x39 0xF40400F8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB00
    $::_TARGET0 riscv dmi_write 0x39 0xF4040100
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB01
    $::_TARGET0 riscv dmi_write 0x39 0xF4040108
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB02
    $::_TARGET0 riscv dmi_write 0x39 0xF4040110
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB03
    $::_TARGET0 riscv dmi_write 0x39 0xF4040118
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB04
    $::_TARGET0 riscv dmi_write 0x39 0xF4040120
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB05
    $::_TARGET0 riscv dmi_write 0x39 0xF4040128
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB06
    $::_TARGET0 riscv dmi_write 0x39 0xF4040130
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB07
    $::_TARGET0 riscv dmi_write 0x39 0xF4040138
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB08
    $::_TARGET0 riscv dmi_write 0x39 0xF4040140
    $::_TARGET0 riscv dmi_write 0x3C 0xC

    # PB09
    $::_TARGET0 riscv dmi_write 0x39 0xF4040148
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB10
    $::_TARGET0 riscv dmi_write 0x39 0xF4040150
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB11
    $::_TARGET0 riscv dmi_write 0x39 0xF4040158
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB12
    $::_TARGET0 riscv dmi_write 0x39 0xF4040160
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB13
    $::_TARGET0 riscv dmi_write 0x39 0xF4040168
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB14
    $::_TARGET0 riscv dmi_write 0x39 0xF4040170
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB15
    $::_TARGET0 riscv dmi_write 0x39 0xF4040178
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB16
    $::_TARGET0 riscv dmi_write 0x39 0xF4040180
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB17
    $::_TARGET0 riscv dmi_write 0x39 0xF4040188
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB18
    $::_TARGET0 riscv dmi_write 0x39 0xF4040190
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB19
    $::_TARGET0 riscv dmi_write 0x39 0xF4040198
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB20
    $::_TARGET0 riscv dmi_write 0x39 0xF40401A0
    $::_TARGET0 riscv dmi_write 0x3C 0xC

    # PB21
    $::_TARGET0 riscv dmi_write 0x39 0xF40401A8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB22
    $::_TARGET0 riscv dmi_write 0x39 0xF40401B0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB23
    $::_TARGET0 riscv dmi_write 0x39 0xF40401B8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB24
    $::_TARGET0 riscv dmi_write 0x39 0xF40401C0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB25
    $::_TARGET0 riscv dmi_write 0x39 0xF40401C8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB26
    $::_TARGET0 riscv dmi_write 0x39 0xF40401D0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB27
    $::_TARGET0 riscv dmi_write 0x39 0xF40401D8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB28
    $::_TARGET0 riscv dmi_write 0x39 0xF40401E0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB29
    $::_TARGET0 riscv dmi_write 0x39 0xF40401E8
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB30
    $::_TARGET0 riscv dmi_write 0x39 0xF40401F0
    $::_TARGET0 riscv dmi_write 0x3C 0xC
    # PB31
    $::_TARGET0 riscv dmi_write 0x39 0xF40401F8
    $::_TARGET0 riscv dmi_write 0x3C 0xC

    # femc configuration
    $::_TARGET0 riscv dmi_write 0x39 0xF3050000
    $::_TARGET0 riscv dmi_write 0x3C 0x1
    sleep 10
    $::_TARGET0 riscv dmi_write 0x39 0xF3050000
    $::_TARGET0 riscv dmi_write 0x3C 0x2
    $::_TARGET0 riscv dmi_write 0x39 0xF3050008
    $::_TARGET0 riscv dmi_write 0x3C 0x30524
    $::_TARGET0 riscv dmi_write 0x39 0xF305000C
    $::_TARGET0 riscv dmi_write 0x3C 0x6030524
    $::_TARGET0 riscv dmi_write 0x39 0xF3050000
    $::_TARGET0 riscv dmi_write 0x3C 0x10000000

    # 32MB
    $::_TARGET0 riscv dmi_write 0x39 0xF3050010
    $::_TARGET0 riscv dmi_write 0x3C 0x4000001b
    $::_TARGET0 riscv dmi_write 0x39 0xF3050014
    $::_TARGET0 riscv dmi_write 0x3C 0
    # 16-bit
    $::_TARGET0 riscv dmi_write 0x39 0xF3050040
    $::_TARGET0 riscv dmi_write 0x3C 0xf31

    # 166Mhz configuration
    $::_TARGET0 riscv dmi_write 0x39 0xF3050044
    $::_TARGET0 riscv dmi_write 0x3C 0x884e33

    $::_TARGET0 riscv dmi_write 0x39 0xF3050048
    $::_TARGET0 riscv dmi_write 0x3C 0x1020d0d
    $::_TARGET0 riscv dmi_write 0x39 0xF3050048
    $::_TARGET0 riscv dmi_write 0x3C 0x1020d0d
    $::_TARGET0 riscv dmi_write 0x39 0xF305004C
    $::_TARGET0 riscv dmi_write 0x3C 0x2020300

    # config delay cell
    $::_TARGET0 riscv dmi_write 0x39 0xF3050150
    $::_TARGET0 riscv dmi_write 0x3C 0x3b
    $::_TARGET0 riscv dmi_write 0x39 0xF3050150
    $::_TARGET0 riscv dmi_write 0x3C 0x203b

    $::_TARGET0 riscv dmi_write 0x39 0xF3050094
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF3050098
    $::_TARGET0 riscv dmi_write 0x3C 0

    # precharge all
    $::_TARGET0 riscv dmi_write 0x39 0xF3050090
    $::_TARGET0 riscv dmi_write 0x3C 0x40000000
    $::_TARGET0 riscv dmi_write 0x39 0xF305009C
    $::_TARGET0 riscv dmi_write 0x3C 0xA55A000F
    sleep 500
    $::_TARGET0 riscv dmi_write 0x39 0xF305003C
    $::_TARGET0 riscv dmi_write 0x3C 0x3
    # auto refresh
    $::_TARGET0 riscv dmi_write 0x39 0xF305009C
    $::_TARGET0 riscv dmi_write 0x3C 0xA55A000C
    sleep 500
    $::_TARGET0 riscv dmi_write 0x39 0xF305003C
    $::_TARGET0 riscv dmi_write 0x3C 0x3
    $::_TARGET0 riscv dmi_write 0x39 0xF305009C
    $::_TARGET0 riscv dmi_write 0x3C 0xA55A000C
    sleep 500
    $::_TARGET0 riscv dmi_write 0x39 0xF305003C
    $::_TARGET0 riscv dmi_write 0x3C 0x3

    # set mode
    $::_TARGET0 riscv dmi_write 0x39 0xF30500A0
    $::_TARGET0 riscv dmi_write 0x3C 0x33
    $::_TARGET0 riscv dmi_write 0x39 0xF305009C
    $::_TARGET0 riscv dmi_write 0x3C 0xA55A000A
    sleep 500
    $::_TARGET0 riscv dmi_write 0x39 0xF305003C
    $::_TARGET0 riscv dmi_write 0x3C 0x3

    $::_TARGET0 riscv dmi_write 0x39 0xF305004C
    $::_TARGET0 riscv dmi_write 0x3C 0x2020301
    echo "SDRAM has been initialized"
}

$_TARGET0 configure -event reset-init {
    init_clock
    init_sdram
}

$_TARGET0 configure -event gdb-attach {
    reset halt
}
