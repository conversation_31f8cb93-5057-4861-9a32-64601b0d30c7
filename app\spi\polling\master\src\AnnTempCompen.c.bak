/*****************************************************�ļ�˵��******************************************************************************/
/*�ļ����ƣ�COMPENSATION.C                                                                                                                 */
/*�汾�ţ�  Ver 0.1                                                                                                                        */
/*��д����/ʱ�䣺                                                                                                */
/*��д�ˣ�                                                                                                                             */
/*�����ļ���const.h��typedefine.h��math.h��DATASTRUCT.h��EXTERNGLOBALDATA.h��FUNCTION.h��memory.h                                          */
/*������������INSlocusGen.m�ļ���������������������������ģ������������ȱ                                                                  */
/*˵����ļ������˳������õ��Ĺ��Ե������㺯���������ļ�Ϊ��ʼ���԰汾���ļ��������庯�������������Ա�ο�ѡ�ã�                       */
/*******************************************************************************************************************************************/
#include "DATASTRUCT.h"
#include "FUNCTION.h"
//#include <math.h>
//#include <string.h>
#include <EXTERNGLOBALDATA.h>
/*********************************************����˵��*******************************************************/
/*�������ƣ�AccANNCompen_Z_Init                                                                             */
/*��������������Z���ٶȼ�����������ʼ��                                                                     */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void ANNCompen_Init()
{
	GyroANNCompen_X_Init(&g_GyroANNCompen.ANNCompen_X);
	GyroANNCompen_Y_Init(&g_GyroANNCompen.ANNCompen_Y);
	GyroANNCompen_Z_Init(&g_GyroANNCompen.ANNCompen_Z);
	
	AccANNCompen_X_Init(&g_AccANNCompen.ANNCompen_X);
	AccANNCompen_Y_Init(&g_AccANNCompen.ANNCompen_Y);
	AccANNCompen_Z_Init(&g_AccANNCompen.ANNCompen_Z);
}




/*********************************************����˵��*******************************************************/
/*�������ƣ�GyroANNCompen_X_Init                                                                            */
/*��������������X��������������ʼ��                                                                         */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void GyroANNCompen_X_Init(p_ANNCompen lp_GyroANNCompen_X)
{
	IPARA i;
DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
1.998980715870857239e-02,-7.065402269363403320e-01,-1.037713170051574707e+00,-6.967186927795410156e-01,-5.393515825271606445e-01,-3.400408327579498291e-01,-6.187726259231567383e-01,-5.167533755302429199e-01,
1.524949431419372559e+00,-4.627042710781097412e-01,4.308342039585113525e-01,-5.383996367454528809e-01,-6.080510020256042480e-01,1.443571299314498901e-01,2.226777225732803345e-01,9.425804018974304199e-01
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
-1.272417418658733368e-02,
-2.191908061504364014e-01,
2.727769613265991211e-01,
6.919620186090469360e-02,
2.418954521417617798e-01,
2.014748901128768921e-01,
-2.991983592510223389e-01,
6.359317898750305176e-02
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
6.403976678848266602e-01,-2.171368747949600220e-01,5.002707242965698242e-01,6.017520427703857422e-01,
-7.001505494117736816e-01,-3.282522261142730713e-01,-1.462401300668716431e-01,1.905878633260726929e-01,
3.030194714665412903e-02,3.905006647109985352e-01,-7.040250897407531738e-01,-2.020749002695083618e-01,
-4.922134280204772949e-01,-1.021992638707160950e-01,-6.804911494255065918e-01,-5.693972706794738770e-01,
1.558580696582794189e-01,-4.153920710086822510e-01,8.518925905227661133e-01,-2.059116125106811523e+00,
8.783032894134521484e-01,-1.588735729455947876e-01,9.993987679481506348e-01,3.164122104644775391e-01,
-6.561557054519653320e-01,6.142372488975524902e-01,-1.339402318000793457e+00,-4.150192737579345703e-01,
-9.124896526336669922e-01,-1.521605402231216431e-01,-3.099214732646942139e-01,-5.286120176315307617e-01
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
1.646249890327453613e-01,
-2.631069347262382507e-02,
1.633021533489227295e-01,
-8.031579107046127319e-02
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
-9.814355373382568359e-01,
-5.381353497505187988e-01,
-1.122035145759582520e+00,
1.471147060394287109e+00
};
lp_GyroANNCompen_X -> Dense3_Bias = 
4.709845185279846191e-01
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_X -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_X -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
lp_GyroANNCompen_X -> Normalized_Temp_Max =91.76761150000002;
lp_GyroANNCompen_X -> Normalized_Temp_Min =-35.26423150000001;
lp_GyroANNCompen_X -> Normalized_Temp_Mean =0.502611557483801;
lp_GyroANNCompen_X -> Normalized_Temp_Diff_Max =5.068303280750001;
lp_GyroANNCompen_X -> Normalized_Temp_Diff_Min =-4.976285810000004;
lp_GyroANNCompen_X -> Normalized_Temp_Diff_Mean =0.4959088092592743;
lp_GyroANNCompen_X -> Normalized_Output_Max =0.409410007736044;
lp_GyroANNCompen_X -> Normalized_Output_Min =-0.38961703599722447;
lp_GyroANNCompen_X -> Normalized_Output_Mean =0.35426256997836636;


	lp_GyroANNCompen_X -> Correct_Value = -4.487876407742505e-02;
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�GyroANNCompen_Y_Init                                                                            */
/*��������������Y��������������ʼ��                                                                         */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void GyroANNCompen_Y_Init(p_ANNCompen lp_GyroANNCompen_Y)
{
	IPARA i;
      DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
9.387572109699249268e-02,-6.208902597427368164e-01,-9.165185093879699707e-01,2.700256407260894775e-01,-7.993184924125671387e-01,8.405790925025939941e-01,4.157560169696807861e-01,3.814918994903564453e-01,
6.344192624092102051e-01,-8.864954113960266113e-02,-3.245570063591003418e-01,4.257561862468719482e-01,-5.958823561668395996e-01,-1.578035205602645874e-01,-2.887438284233212471e-03,1.020889639854431152e+00
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
1.417464613914489746e-01,
4.132809117436408997e-02,
1.190808191895484924e-01,
3.009025156497955322e-01,
5.547329038381576538e-02,
-8.614403754472732544e-02,
-1.970378495752811432e-02,
1.662241965532302856e-01
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
-8.876186013221740723e-01,2.663125284016132355e-02,-9.409657120704650879e-01,-8.624455928802490234e-01,
7.420908212661743164e-01,-1.002158999443054199e+00,-7.652551531791687012e-01,4.878233671188354492e-01,
6.793098449707031250e-01,2.675061225891113281e-01,1.215553749352693558e-02,4.670124948024749756e-01,
-8.314776420593261719e-02,8.039679527282714844e-01,-1.342208981513977051e+00,3.398536890745162964e-02,
-3.218560218811035156e-01,4.426437318325042725e-01,1.200092911720275879e+00,-6.810199618339538574e-01,
5.674885511398315430e-01,1.288097202777862549e-01,-6.463903933763504028e-02,2.665826976299285889e-01,
6.138943433761596680e-01,-6.849806904792785645e-01,-3.026441298425197601e-02,4.457807242870330811e-01,
7.227377891540527344e-01,-2.440019100904464722e-01,2.527763843536376953e-01,6.132801771163940430e-01
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
-3.143937885761260986e-02,
2.638241052627563477e-01,
-1.899716854095458984e-01,
5.695050954818725586e-02
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
-1.395581603050231934e+00,
6.326306462287902832e-01,
-9.057148098945617676e-01,
-1.504614472389221191e+00
};
lp_GyroANNCompen_Y -> Dense3_Bias = 
2.120583355426788330e-01
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Y -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_Y -> Dense3_Mat[i] = Dense3_Mat[i];
	}


        
lp_GyroANNCompen_Y -> Normalized_Temp_Max =91.76761150000002;
lp_GyroANNCompen_Y -> Normalized_Temp_Min =-35.26423150000001;
lp_GyroANNCompen_Y -> Normalized_Temp_Mean =0.502611557483801;
lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Max =5.068303280750001;
lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Min =-4.976285810000004;
lp_GyroANNCompen_Y -> Normalized_Temp_Diff_Mean =0.4959088092592743;
lp_GyroANNCompen_Y -> Normalized_Output_Max =0.5922685249472588;
lp_GyroANNCompen_Y -> Normalized_Output_Min =0.1229113708431774;
lp_GyroANNCompen_Y -> Normalized_Output_Mean =0.4931048509561429;
   
	lp_GyroANNCompen_Y -> Correct_Value = 9.208667453793662e-02;
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�GyroANNCompen_Z_Init                                                                            */
/*��������������Z��������������ʼ��                                                                         */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void GyroANNCompen_Z_Init(p_ANNCompen lp_GyroANNCompen_Z)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
-5.065555125474929810e-02,-1.304073572158813477e+00,1.178458333015441895e-01,7.727466821670532227e-01,1.951406337320804596e-02,1.874686121940612793e+00,-8.028848469257354736e-02,-1.317210912704467773e+00,
1.627615809440612793e+00,8.355177938938140869e-03,-1.137743473052978516e+00,2.598947584629058838e-01,-1.851714449003338814e-03,-2.399554103612899780e-01,-3.730731010437011719e-01,1.988682597875595093e-01
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
-4.955096635967493057e-03,
6.480405330657958984e-01,
4.428785145282745361e-01,
-2.025756984949111938e-01,
-3.155411034822463989e-02,
-7.540217787027359009e-02,
-8.365701138973236084e-02,
1.401039771735668182e-02
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
-4.273974895477294922e-01,-8.693610318005084991e-03,-5.905637145042419434e-01,-5.711636543273925781e-01,
4.935789406299591064e-01,9.193735718727111816e-01,4.234317839145660400e-01,2.281734228134155273e+00,
7.843812704086303711e-01,7.551310211420059204e-02,1.122228741645812988e+00,-3.512539267539978027e-01,
1.027878820896148682e-01,2.825930714607238770e-01,-2.070291757583618164e+00,1.411293745040893555e-01,
-1.860961467027664185e-01,-4.638438820838928223e-01,-6.577808856964111328e-01,-2.505003213882446289e-01,
3.255383670330047607e-01,9.445267915725708008e-01,-2.310048580169677734e+00,7.577611804008483887e-01,
-3.830610215663909912e-01,3.364689648151397705e-02,-6.434478163719177246e-01,1.001291990280151367e+00,
4.181903004646301270e-01,-8.094003796577453613e-01,1.129157066345214844e+00,-2.125307798385620117e+00
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
5.999050736427307129e-01,
-5.705948472023010254e-01,
5.790016651153564453e-01,
1.060866657644510269e-02
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
-4.485988914966583252e-01,
2.002233982086181641e+00,
-5.303796529769897461e-01,
1.261690497398376465e+00
};
lp_GyroANNCompen_Z -> Dense3_Bias = 
-3.728438913822174072e-01
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_GyroANNCompen_Z -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_GyroANNCompen_Z -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
lp_GyroANNCompen_Z -> Normalized_Temp_Max =91.76761150000002;
lp_GyroANNCompen_Z -> Normalized_Temp_Min =-35.26423150000001;
lp_GyroANNCompen_Z -> Normalized_Temp_Mean =0.502611557483801;
lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Max =5.068303280750001;
lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Min =-4.976285810000004;
lp_GyroANNCompen_Z -> Normalized_Temp_Diff_Mean =0.4959088092592743;
lp_GyroANNCompen_Z -> Normalized_Output_Max =0.09234241196941297;
lp_GyroANNCompen_Z -> Normalized_Output_Min =-0.5976816015303411;
lp_GyroANNCompen_Z -> Normalized_Output_Mean =0.5762262328410215;

        
	lp_GyroANNCompen_Z -> Correct_Value = 5.046496614048418e-02;
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�AccANNCompen_X_Init                                                                            */
/*��������������X���ٶȼ�����������ʼ��                                                                         */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_X_Init(p_ANNCompen lp_AccANNCompen_X)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
5.272706747055053711e-01,-9.055991172790527344e-01,6.792660355567932129e-01,2.134282141923904419e-01,4.215075373649597168e-01,-4.432365298271179199e-01,-8.797404170036315918e-01,-1.568285465240478516e+00,
-7.102225422859191895e-01,-5.009412765502929688e-01,-3.890084326267242432e-01,-1.518284827470779419e-01,-6.817848682403564453e-01,5.781194567680358887e-01,-1.723397821187973022e-01,-1.051044940948486328e+00
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
-3.705012053251266479e-02,
1.636789645999670029e-03,
6.722450722008943558e-03,
-1.297031939029693604e-01,
1.662969589233398438e-01,
2.208682596683502197e-01,
-5.123461037874221802e-02,
-7.869878411293029785e-01
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
-1.552017092704772949e+00,-1.409070014953613281e+00,6.241805553436279297e-01,-3.989861905574798584e-01,
1.521163225173950195e+00,7.242405414581298828e-02,1.613485813140869141e-01,-5.328096747398376465e-01,
-6.515835523605346680e-01,-3.095567226409912109e+00,-4.770236015319824219e-01,6.398660689592361450e-02,
-4.098513126373291016e-01,-5.051180720329284668e-01,4.283728897571563721e-01,-4.800739884376525879e-01,
1.228616714477539062e+00,8.794628381729125977e-01,-6.578648090362548828e-01,4.390043616294860840e-01,
8.181563019752502441e-01,-2.306172065436840057e-02,-5.390801429748535156e-01,-7.704088091850280762e-01,
-1.928330898284912109e+00,2.548933029174804688e-01,3.917382955551147461e-01,1.540830135345458984e+00,
-4.912953853607177734e+00,-1.819871306419372559e+00,5.498756170272827148e-01,6.704981327056884766e+00
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
-2.897628545761108398e-01,
-1.055571138858795166e-01,
-5.933116748929023743e-02,
-4.301586374640464783e-02
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
2.310930728912353516e+00,
1.544533252716064453e+00,
7.042518258094787598e-01,
-3.931199789047241211e+00
};
lp_AccANNCompen_X -> Dense3_Bias = 
-5.069551989436149597e-02
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_X -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_X -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	

	lp_AccANNCompen_X -> Normalized_Temp_Max =91.76761150000002;
	lp_AccANNCompen_X -> Normalized_Temp_Min =-35.26423150000001;
	lp_AccANNCompen_X -> Normalized_Temp_Mean =0.502611557483801;
	lp_AccANNCompen_X -> Normalized_Temp_Diff_Max =5.068303280750001;
	lp_AccANNCompen_X -> Normalized_Temp_Diff_Min =-4.976285810000004;
	lp_AccANNCompen_X -> Normalized_Temp_Diff_Mean =0.4959088092592743;
	lp_AccANNCompen_X -> Normalized_Output_Max =0.007457064502536398;
	lp_AccANNCompen_X -> Normalized_Output_Min =0.004123318019049538;
	lp_AccANNCompen_X -> Normalized_Output_Mean =0.43302126505700206;
       
       


	lp_AccANNCompen_X -> Correct_Value = 1.657692057645018e-03;
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�AccANNCompen_Y_Init                                                                            */
/*��������������Y���ٶȼ�����������ʼ��                                                                         */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_Y_Init(p_ANNCompen lp_AccANNCompen_Y)
{
	IPARA i;
	DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
-3.372149467468261719e-01,8.691054582595825195e-01,4.113445281982421875e-01,-1.424423009157180786e-01,8.831669688224792480e-01,-2.657859921455383301e-01,-5.221847295761108398e-01,-8.027141094207763672e-01,
-2.167114317417144775e-01,5.821629762649536133e-01,-8.626000285148620605e-01,-7.757965922355651855e-01,9.011618494987487793e-01,-5.420004576444625854e-02,-7.584955692291259766e-01,-8.094909787178039551e-01
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
-1.774098128080368042e-01,
4.101034402847290039e-01,
1.763470172882080078e-01,
-3.176590800285339355e-01,
-7.359303534030914307e-02,
-1.444595456123352051e-01,
2.676547169685363770e-01,
1.643396914005279541e-02
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
-6.768946051597595215e-01,-4.643343389034271240e-01,-3.575707674026489258e-01,2.256298512220382690e-01,
2.386389076709747314e-01,-8.126909732818603516e-01,1.394172608852386475e-01,5.499647259712219238e-01,
-1.504775404930114746e+00,7.128223180770874023e-01,1.129290819168090820e+00,2.133760601282119751e-01,
-4.351051747798919678e-01,-6.228035688400268555e-01,-6.044271588325500488e-01,5.915977954864501953e-01,
-5.414037704467773438e-01,7.073153257369995117e-01,7.416278719902038574e-01,4.125677943229675293e-01,
1.904730498790740967e-01,1.649933010339736938e-01,4.027005732059478760e-01,8.032649010419845581e-02,
3.622283041477203369e-01,3.689682483673095703e-01,-6.453426182270050049e-02,1.136574029922485352e+00,
1.517536584287881851e-02,-8.235947489738464355e-01,4.938190281391143799e-01,-2.865819036960601807e-01
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
4.308097437024116516e-02,
1.520179063081741333e-01,
-1.763786524534225464e-01,
-5.821895003318786621e-01
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
-6.776882410049438477e-01,
1.621795058250427246e+00,
6.587825417518615723e-01,
-3.431076288223266602e+00
};
lp_AccANNCompen_Y -> Dense3_Bias = 
-2.761771157383918762e-02
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Y -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_Y -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
lp_AccANNCompen_Y -> Normalized_Temp_Max =91.76761150000002;
lp_AccANNCompen_Y -> Normalized_Temp_Min =-35.26423150000001;
lp_AccANNCompen_Y -> Normalized_Temp_Mean =0.502611557483801;
lp_AccANNCompen_Y -> Normalized_Temp_Diff_Max =5.068303280750001;
lp_AccANNCompen_Y -> Normalized_Temp_Diff_Min =-4.976285810000004;
lp_AccANNCompen_Y -> Normalized_Temp_Diff_Mean =0.4959088092592743;
lp_AccANNCompen_Y -> Normalized_Output_Max =0.00637513454206128;
lp_AccANNCompen_Y -> Normalized_Output_Min =-0.0010403778352164493;
lp_AccANNCompen_Y -> Normalized_Output_Mean =0.5073190822746504;
       
        

	lp_AccANNCompen_Y -> Correct_Value = -2.723564252367471e-03;
}

/*********************************************����˵��*******************************************************/
/*�������ƣ�AccANNCompen_Z_Init                                                                             */
/*��������������Z���ٶȼ�����������ʼ��                                                                     */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
void AccANNCompen_Z_Init(p_ANNCompen lp_AccANNCompen_Z)
{
	IPARA i;
DPARA Dense1_Mat[INPUT_DIM * DENSE_1_CELL_NUM] = {
9.382778406143188477e-02,-6.312169134616851807e-02,4.506324827671051025e-01,-4.679289758205413818e-01,6.218072772026062012e-02,-2.014203220605850220e-01,-1.624733448028564453e+00,5.781469941139221191e-01,
-1.042087316513061523e+00,7.038611173629760742e-01,-7.019187808036804199e-01,-7.043053507804870605e-01,1.286476850509643555e-01,5.340014100074768066e-01,4.172481894493103027e-01,-7.437390685081481934e-01
};
DPARA Dense1_Bias[DENSE_1_CELL_NUM] = {
2.577281296253204346e-01,
3.684269264340400696e-02,
1.989881275221705437e-03,
2.415095418691635132e-01,
-6.510239839553833008e-02,
-1.801779568195343018e-01,
-3.484123349189758301e-01,
3.341184183955192566e-02
};
DPARA Dense2_Mat[DENSE_1_CELL_NUM * DENSE_2_CELL_NUM] = {
-2.022687792778015137e-01,5.830618143081665039e-01,7.985712289810180664e-01,1.273391366004943848e+00,
-6.812507510185241699e-01,8.671404123306274414e-01,5.757626891136169434e-01,-5.885488353669643402e-03,
-4.067657589912414551e-01,-3.818876743316650391e-01,1.747983336448669434e+00,-1.576918363571166992e+00,
-2.730438113212585449e-01,-1.051443219184875488e+00,-1.285480856895446777e-01,-7.531090080738067627e-02,
3.651850819587707520e-01,-3.470714092254638672e-01,6.457670927047729492e-01,-1.384502798318862915e-01,
-1.888638734817504883e-01,-2.187560796737670898e+00,-8.162528276443481445e-01,1.940245479345321655e-01,
-3.927700221538543701e-01,3.663021028041839600e-01,-4.648148417472839355e-01,-1.714499592781066895e+00,
-6.356989145278930664e-01,4.617447555065155029e-01,-2.288526773452758789e+00,-2.315592020750045776e-01
};
DPARA Dense2_Bias[DENSE_2_CELL_NUM] = {
-2.331282384693622589e-02,
5.162835493683815002e-02,
-1.601502299308776855e-01,
4.417222738265991211e-02
};
DPARA Dense3_Mat[DENSE_2_CELL_NUM * OUTPUT_DIM] = { 
-3.642331957817077637e-01,
1.790270090103149414e+00,
-2.303400754928588867e+00,
1.121395945549011230e+00
};
DPARA Dense3_Bias = 
-3.176813721656799316e-01
;
	
	for(i = 0; i < INPUT_DIM * DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense1_Mat[i] = Dense1_Mat[i];
	}
	for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense1_Bias[i] = Dense1_Bias[i];
	}
	
	for(i = 0; i < DENSE_1_CELL_NUM * DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense2_Mat[i] = Dense2_Mat[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM;i++)
	{
			lp_AccANNCompen_Z -> Dense2_Bias[i] = Dense2_Bias[i];
	}
	
	for(i = 0; i < DENSE_2_CELL_NUM * OUTPUT_DIM;i++)
	{
			lp_AccANNCompen_Z -> Dense3_Mat[i] = Dense3_Mat[i];
	}
	
lp_AccANNCompen_Z -> Normalized_Temp_Max =91.76761150000002;
lp_AccANNCompen_Z -> Normalized_Temp_Min =-35.26423150000001;
lp_AccANNCompen_Z -> Normalized_Temp_Mean =0.502611557483801;
lp_AccANNCompen_Z -> Normalized_Temp_Diff_Max =5.068303280750001;
lp_AccANNCompen_Z -> Normalized_Temp_Diff_Min =-4.976285810000004;
lp_AccANNCompen_Z -> Normalized_Temp_Diff_Mean =0.4959088092592743;
lp_AccANNCompen_Z -> Normalized_Output_Max =0.9997597176736575;
lp_AccANNCompen_Z -> Normalized_Output_Min =0.9861827615892071;
lp_AccANNCompen_Z -> Normalized_Output_Mean =0.6311518620753731;
        
       

	lp_AccANNCompen_Z -> Correct_Value = 9.878286998465159e-01;
}
/*********************************************����˵��*******************************************************/
/*��������ANN_Predcit                                                                                       */
/*����������������������Ԥ��                                                                                */
/*�汾�ţ�  Ver 0.1                                                                                         */
/*��д����/ʱ�䣺                                                                                           */
/*��д�ˣ�                                                                                                  */
/*�����������                                                                                              */
/*�����������                                                                                              */
/*������������ȱ                                                                                            */
/*��ע1��                                                                                                   */
/*����ֵ��                                                                                                  */
/************************************************************************************************************/
DPARA ANN_Predict(DPARA Temp,DPARA Temp_Diff,p_ANNCompen lp_ANNCompen)
{
	IPARA i;
	DPARA Output1[DENSE_1_CELL_NUM] = {0};
	DPARA Output2[DENSE_2_CELL_NUM] = {0};
	DPARA Output3 = 0.0;
	DPARA RT_Bias;
	//DPARA Output4 = 0.0;
	
	DPARA Normalized_Temp, Normalized_Temp_Diff;
	DPARA Input1[2];
	//������һ��
	Normalized_Temp = (Temp - lp_ANNCompen -> Normalized_Temp_Min) / (lp_ANNCompen -> Normalized_Temp_Max - lp_ANNCompen -> Normalized_Temp_Min);
	Normalized_Temp = Normalized_Temp - lp_ANNCompen -> Normalized_Temp_Mean;

	Normalized_Temp_Diff = (Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Min) / (lp_ANNCompen->Normalized_Temp_Diff_Max - lp_ANNCompen->Normalized_Temp_Diff_Min);
	Normalized_Temp_Diff = Normalized_Temp_Diff - lp_ANNCompen->Normalized_Temp_Diff_Mean;

	Input1[0] = Normalized_Temp;
	Input1[1] = Normalized_Temp_Diff;
	//��һ���ز�
	/*for(i = 0; i < DENSE_1_CELL_NUM;i++)
	{
		Output1[i] = Normalized_Temp * lp_ANNCompen -> Dense1_Mat[i] + lp_ANNCompen -> Dense1_Bias[i];
	}*/
	Mat_Mul(Input1, (double *)lp_ANNCompen -> Dense1_Mat, Output1, 1, INPUT_DIM, DENSE_1_CELL_NUM);
	for (i = 0; i < DENSE_1_CELL_NUM; i++)
	{
		Output1[i] += lp_ANNCompen->Dense1_Bias[i];
	}
	Relu(Output1, DENSE_1_CELL_NUM);
	//�ڶ����ز�
	Mat_Mul(Output1, (double *)lp_ANNCompen -> Dense2_Mat, Output2, 1,DENSE_1_CELL_NUM, DENSE_2_CELL_NUM);
	for(i = 0;i < DENSE_2_CELL_NUM;i++)
	{
		Output2[i] += lp_ANNCompen -> Dense2_Bias[i];	
	}
	Relu(Output2, DENSE_2_CELL_NUM);
	//������
	MultiDim_Vec_Dot(Output2, (double *)lp_ANNCompen -> Dense3_Mat, &Output3,DENSE_2_CELL_NUM);
	Output3 = Output3 + lp_ANNCompen -> Dense3_Bias;
	RT_Bias = (Output3 + lp_ANNCompen -> Normalized_Output_Mean) * (lp_ANNCompen -> Normalized_Output_Max - lp_ANNCompen -> Normalized_Output_Min) + lp_ANNCompen -> Normalized_Output_Min;
	RT_Bias = RT_Bias - lp_ANNCompen -> Correct_Value;	 
	return RT_Bias;
}

