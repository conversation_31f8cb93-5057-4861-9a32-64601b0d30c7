//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart_Irq.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.12.24
//---------------------------------------------------------

#include "uart_Irq.h"
#include "SetParaBao.h"

ATTR_PLACE_AT_NONCACHEABLE uint8_t grxbuffer[U4RX_MAXCOUNT];
ATTR_PLACE_AT_NONCACHEABLE uint8_t tx_buffer[U4RX_MAXCOUNT];
int grxlen = 0, grxst = 0;
uint16_t tx_counter = 0, rx_counter = 0;
int gbtxcompleted = 1;
uint32_t nbr_data_to_read = BUFFER_SIZE_RX;
uint32_t nbr_data_to_send = BUFFER_SIZE_TX;

void uart_isr(void)
{
    uint8_t irq_id = uart_get_irq_id(TEST_UART);
    if (irq_id == uart_intr_id_rx_data_avail) 
    {
        while (uart_check_status(TEST_UART, uart_stat_data_ready)) 
        {
            if (grxlen < U4RX_MAXCOUNT) 
            {
                grxbuffer[(grxst + grxlen++) % U4RX_MAXCOUNT] = uart_read_byte(TEST_UART);
            }
            else 
            {
                grxbuffer[(grxst + grxlen) % U4RX_MAXCOUNT] = uart_read_byte(TEST_UART);
            }

            //data_buff[buff_index++] = uart_read_byte(TEST_UART);
        }
        //uart_disable_irq(TEST_UART, uart_intr_rx_data_avail_or_timeout);
        //uart_enable_irq(TEST_UART, uart_intr_tx_slot_avail);
    }

    if (irq_id == uart_intr_id_tx_slot_avail) 
    {
        uart_write_byte(TEST_UART, tx_buffer[tx_counter++]);

        //for (uint8_t i = 0; i < buff_index; i++) {
        //    uart_write_byte(TEST_UART, data_buff[i]);
        //}

        if(tx_counter >= nbr_data_to_send)
        {
            /* disable the USART6 transmit interrupt */
            uart_disable_irq(TEST_UART, uart_intr_tx_slot_avail);
            tx_counter = 0;
            gbtxcompleted = 1;
        }

        //buff_index = 0;
        //uart_disable_irq(TEST_UART, uart_intr_tx_slot_avail);
        //uart_enable_irq(TEST_UART, uart_intr_rx_data_avail_or_timeout);
    }
}

SDK_DECLARE_EXT_ISR_M(TEST_UART_IRQ, uart_isr)


void UartIrqSendMsg(char *txbuf, int size)
{
    while(gbtxcompleted == 0);
    gbtxcompleted = 0;
    nbr_data_to_send = size;
    memcpy(tx_buffer, txbuf, size);
    
    uart_enable_irq(TEST_UART, uart_intr_tx_slot_avail);
}


unsigned char gframeParsebuf[1024];
void analysisRxdata(void)
{
    int i, j, bfind2 = 0;
    int rxlenbx = grxlen, isbkexit = 0;
    for (i = 0; i < grxlen; i++) 
    {
          if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 0xFA) 
          {
                rxlenbx = (int)((grxbuffer[(grxst + 6) % U4RX_MAXCOUNT]<<8) + grxbuffer[(grxst + 5) % U4RX_MAXCOUNT]);
                gframeParsebuf[0] = 0xaf;
                gframeParsebuf[1] = 0x55;
                gframeParsebuf[2] = 0xfa;
                for (j = i + 3; j < grxlen; j++) 
                {
                      gframeParsebuf[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
                      if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xAF && grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == 0x55 && grxbuffer[(grxst + j + 2) % U4RX_MAXCOUNT] == 0xFA) 
                      {
                          bfind2 = 1;
                          break;
                      }
              
                      if (j == rxlenbx - 1 && grxbuffer[(grxst + j-1) % U4RX_MAXCOUNT] == 0x00 && grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xff) //新协议 定长len=256，帧尾 0x00 0xFF
                      {
                          bfind2 = 1;
                          j++;       
                          break;     
                      }
                }

                if (bfind2 == 0)	
                    break;

                grxst = (grxst + j) % U4RX_MAXCOUNT;
                grxlen -= j;
                isbkexit = 0;
                break;
          }
	
    }

    if (bfind2) 
    {
        UartDmaRecSetPara((p_dmauart_t)gframeParsebuf);
    }

}

void UartIrqInit(void)
{
    hpm_stat_t stat;

    board_init_uart(TEST_UART);

    uart_config_t config = {0};
    uart_default_config(TEST_UART, &config);
    config.baudrate = stSetPara.Setbaud*100;
    config.src_freq_in_hz = clock_get_frequency(TEST_UART_CLK_NAME);

    stat = uart_init(TEST_UART, &config);
    if (stat != status_success) {
        /* uart failed to be initialized */
        printf("failed to initialize uart\n");
        while(1);
    }
    uart_enable_irq(TEST_UART, uart_intr_rx_data_avail_or_timeout);
    intc_m_enable_irq_with_priority(TEST_UART_IRQ, 1);
}


