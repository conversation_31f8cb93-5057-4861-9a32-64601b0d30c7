#ifndef _EXTERNGLOBALDATA_H
#define _EXTERNGLOBALDATA_H
/***********************************************************************************************************************************/
/*EXTERNGLOBALDATA.h                                                                                                             */
/*  Ver 0.1                                                                                                                        */
/*/                                                                                                */
/*                                                                                                                             */
/*                                                                                                                             */
/*GNSSlocusGen.m                                                              */
/*                 */
/*******************************************************************************************************************************************/
//#include "appmain.h"
#include "DATASTRUCT.h"
extern SysVar g_SysVar;    //

extern SelfTest g_SelfTest;

extern InitBind g_InitBind;//

extern Align g_Align;//

extern Navi g_Navi;//

extern Kalman g_Kalman;//Kalman

extern GNSSData g_GNSSData_GNSS,g_GNSSData_VMC,g_GNSSData_In_Use;//GPS1//GPS1

//extern GNSSData g_GNSSData2;//GPS2

//extern ADCData g_ADCData1;  //1ADC

//extern ADCData g_ADCData2;  //2ADC

extern Compen g_Compen;//

extern InertialSysAlign g_InertialSysAlign;//

extern DynamicInertialSysAlign g_DynamicInertialSysAlign;

extern IMUSmoothAverage g_IMUSmoothAverage;

extern CmdNormalTempCompenData g_CmdNormalTempCompenData;

extern CmdFullTempCompenData g_CmdFullTempCompenData;

extern CmdANNCompenData g_GyroANNCompen;

extern CmdANNCompenData g_AccANNCompen;

extern ZUPT g_ZUPT;



#endif
