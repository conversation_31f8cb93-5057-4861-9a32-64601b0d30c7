//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// 文件名称：arithmetic.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2025.01.15
//---------------------------------------------------------
#ifndef _ARITHMETIC_H
#define _ARITHMETIC_H
#include <stdint.h>
#include "EXTERNGLOBALDATA.h"
#include "align.h"

extern uint32_t TimeStamp;//时间戳

#pragma pack(1)
//定义嵌入式输入到算法接口结构体
typedef struct
{
    unsigned int  time;	//时间戳

    float accel_x;
    float accel_y;
    float accel_z;
    float gyro_x;
    float gyro_y;
    float gyro_z;
    float temp_UNO;

    //后期算法工程师可根据需要添加
    //......

} CombineDataTypeDef, *p_CombineDataTypeDef;


//定义算法处理后的数据接口结构体*****解算结果放这里！！！******
typedef struct
{
    float ACCEL_X;//温补之后的IMU数据
    float ACCEL_Y;
    float ACCEL_Z;
    float GYRO_X;
    float GYRO_Y;
    float GYRO_Z;
    float TempUNO;

    //后期算法工程师可根据需要添加
    //....往外输出数据***暂定姿态相关的量****
    float Pitch;
    float Roll;
    float Yaw;
    //四元数
    float Quart_W;
    float Quart_X;
    float Quart_Y;
    float Quart_Z;
    //IMU零偏
    float GyrBx;
    float GyrBy;
    float GyrBz;
    float AccBx;
    float AccBy;
    float AccBz;
} InavOutDataDef, *p_InavOutDataDef;

#pragma pack()

// 变量实际定义在protocol.c中
extern CombineDataTypeDef combineData;
extern InavOutDataDef InavOutData;

//传递陀螺，加速度计数据
static ANGRATE r_Gyro[3] = { 0.0 };
static ANGRATE r_LastGyro[3] = { 0.0 };

static ACCELER Acc[3] = { 0.0 };
static ACCELER LastAcc[3] = { 0.0 };

void AlgorithmDo(void);
void AlgorithmAct(void);
void INS600mAlgorithmEntry(void);
void NavDataOutputSet(p_CombineDataTypeDef lp_combineData, p_Navi lp_Navi, p_InavOutDataDef lp_InavOutData);
void IMUdataPredo(void);
void IMUdataPredp_algParmCache(void);

#endif //_ARITHMETIC_H