//---------------------------------------------------------
// Copyright (c) 2025,INAV All rights reserved.
//
// 文件名称：arithmetic.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2025.01.15
//---------------------------------------------------------
#include "protocol.h"
#include "arithmetic.h"
#include "ZUPT.h"

#define IMUaxisIsENU
#define PHASE_INIT_BIAS_ESTIMATE  100  // 新阶段宏定义
#define INIT_BIAS_WINDOW_SEC      2
#define INIT_BIAS_SAMPLE_RATE     200  // 假设采样率200Hz，根据实际情况调整
#define INIT_BIAS_WINDOW_SIZE     (INIT_BIAS_WINDOW_SEC * INIT_BIAS_SAMPLE_RATE)

InavOutDataDef InavOutData = {0};
CombineDataTypeDef combineData = {0};

// 初始零偏估计窗口累加和与计数
static ACCELER acc_sum[3] = {0.0};
static ANGRATE gyro_sum[3] = {0.0};
static unsigned int bias_sample_count = 0;
static int bias_estimate_done = 0;
// 新增：用于暂存估计零偏的静态变量
static ACCELER estimated_acc_bias[3] = {0.0};
static ANGRATE estimated_gyro_bias[3] = {0.0};

#define DISABLE_ACC_BIAS_COMPENSATION  // 禁用加速度计零偏补偿

void ApplyBiasCorrectionToCombineData(p_CombineDataTypeDef lp_combineData, p_Navi lp_Navi)
{
#ifndef DISABLE_ACC_BIAS_COMPENSATION
    // 修正加速度计
    lp_combineData->accel_x -= lp_Navi->AccBias[0];
    lp_combineData->accel_y -= lp_Navi->AccBias[1];
    lp_combineData->accel_z -= lp_Navi->AccBias[2];
#endif
    // 修正陀螺仪
    lp_combineData->gyro_x -= lp_Navi->r_GyroBias[0];
    lp_combineData->gyro_y -= lp_Navi->r_GyroBias[1];
    lp_combineData->gyro_z -= lp_Navi->r_GyroBias[2];
}

void IMUdataPredo(void)
{
	//IMU数据进入算法之前，需要确保:
	//1.轴系顺序为北天东;
	//2.角速率单位是rad/s，加速度单位是m/s2

    // 仅在粗对准或纯惯阶段修正IMU数据
    if (g_SysVar.WorkPhase == PHASE_COARSE_ALIGN || g_SysVar.WorkPhase == PHASE_INS_NAVI) {
        ApplyBiasCorrectionToCombineData(&combineData, &g_Navi);
    }

	IMUdataPredp_algParmCache();

	#ifdef  IMUaxisIsENU

		Acc[0] = combineData.accel_y * G;
		Acc[1] = combineData.accel_z * G;
		Acc[2] = combineData.accel_x * G;

		r_Gyro[0] = combineData.gyro_y * D2R;
		r_Gyro[1] = combineData.gyro_z * D2R;
		r_Gyro[2] = combineData.gyro_x * D2R;
	#endif

	#ifdef IMUaxisIsNUE

		Acc[0] = combineData.accel_x * G;
		Acc[1] = combineData.accel_y * G;
		Acc[2] = combineData.accel_z * G;

		r_Gyro[0] = combineData.gyro_x * D2R;
		r_Gyro[1] = combineData.gyro_y * D2R;
		r_Gyro[2] = combineData.gyro_z * D2R;
	#endif

}

void IMUdataPredp_algParmCache(void)
//算法参数缓存
{
	//陀螺与加表数据转存
	for (int i = 0; i < 3; i++)
	{
		r_LastGyro[i] = r_Gyro[i];
		LastAcc[i] = Acc[i];
	}
}

void NavDataOutputSet(p_CombineDataTypeDef lp_combineData, p_Navi lp_Navi, p_InavOutDataDef lp_InavOutData)
{
	// 传递IMU数据
	lp_InavOutData->ACCEL_X = lp_combineData->accel_x;
	lp_InavOutData->ACCEL_Y = lp_combineData->accel_y;
	lp_InavOutData->ACCEL_Z = lp_combineData->accel_z;

	lp_InavOutData->GYRO_X = lp_combineData->gyro_x;
	lp_InavOutData->GYRO_Y = lp_combineData->gyro_y;
	lp_InavOutData->GYRO_Z = lp_combineData->gyro_z;

	lp_InavOutData->TempUNO = lp_combineData->temp_UNO;

	// 传递姿态角数据
	lp_InavOutData->Pitch = (float)lp_Navi->r_Atti[1] * R2D; // 俯仰角
	lp_InavOutData->Roll = (float)lp_Navi->r_Atti[2] * R2D;  // 横滚角
	lp_InavOutData->Yaw = (float)lp_Navi->r_Atti[0] * R2D;   // 航向角

	// 传递四元数数据
	lp_InavOutData->Quart_W = (float)lp_Navi->Q[0];
	lp_InavOutData->Quart_X = (float)lp_Navi->Q[1];
	lp_InavOutData->Quart_Y = (float)lp_Navi->Q[2];
	lp_InavOutData->Quart_Z = (float)lp_Navi->Q[3];

	// 传递零偏数据
	lp_InavOutData->GyrBx = (float)lp_Navi->r_GyroBias[0] * R2D;
	lp_InavOutData->GyrBy = (float)lp_Navi->r_GyroBias[1] * R2D;
	lp_InavOutData->GyrBz = (float)lp_Navi->r_GyroBias[2] * R2D;
	lp_InavOutData->AccBx = (float)lp_Navi->AccBias[0];
	lp_InavOutData->AccBy = (float)lp_Navi->AccBias[1];
	lp_InavOutData->AccBz = (float)lp_Navi->AccBias[2];
}

void InitialBiasEstimate(p_CombineDataTypeDef lp_combineData, p_Navi lp_Navi)
{
    // 使用combineData中的原始IMU数据进行增量累加
    acc_sum[0] += lp_combineData->accel_x;
    acc_sum[1] += lp_combineData->accel_y;
    acc_sum[2] += lp_combineData->accel_z;
    gyro_sum[0] += lp_combineData->gyro_x;
    gyro_sum[1] += lp_combineData->gyro_y;
    gyro_sum[2] += lp_combineData->gyro_z;
    bias_sample_count++;

    // 达到窗口长度或超时
    if (bias_sample_count >= INIT_BIAS_WINDOW_SIZE) {
        for (int i = 0; i < 3; i++) {
            estimated_acc_bias[i] = acc_sum[i] / bias_sample_count;
            estimated_gyro_bias[i] = gyro_sum[i] / bias_sample_count;
        }
        bias_estimate_done = 1;
    }
}

void AlgorithmAct(void)
{
    switch (g_SysVar.WorkPhase)
    {
        case PHASE_STANDBY://待机
        {
            //惯性凝固坐标系对准初始化
            g_InertialSysAlign.AlignEndTime = TIME_COARSE_ALIGN;
            InertialSysAlign_Init(&g_InitBind, &g_InertialSysAlign);
            // 进入初始零偏估计阶段
            g_SysVar.WorkPhase = PHASE_INIT_BIAS_ESTIMATE;
            // 初始化窗口
            for (int i = 0; i < 3; i++) {
                acc_sum[i] = 0;
                gyro_sum[i] = 0;
            }
            bias_sample_count = 0;
            bias_estimate_done = 0;

            // 初始化ZUPT结构体
            ZUPTInit(&g_ZUPT);
            break;
        }
        case PHASE_INIT_BIAS_ESTIMATE: // 初始零偏估计
        {
            InitialBiasEstimate(&combineData, &g_Navi);
            if (bias_estimate_done) {
                g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;
            }
            break;
        }
        case PHASE_COARSE_ALIGN: //粗对准
        {
            if (g_SysVar.isRapidAlign == NO)
            {
                //惯性凝固静基座粗对准计算
                InertialSysAlignCompute(r_Gyro, r_LastGyro, Acc, LastAcc, &g_InertialSysAlign);
                if (g_InertialSysAlign.AlignTime >= g_InertialSysAlign.AlignEndTime)
                {
                    //计算对准参数
                    FinishInertialSysAlign(&g_InertialSysAlign);
                    ////导航初始化
                    Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);
                    // 新增：粗对准完成后，将估计零偏写入g_Navi
                    for (int i = 0; i < 3; i++) {
                        g_Navi.AccBias[i] = estimated_acc_bias[i];
                        g_Navi.r_GyroBias[i] = estimated_gyro_bias[i];
                    }
                    g_SysVar.WorkPhase = PHASE_INS_NAVI;
                }
            }
            break;
        }
        case PHASE_INS_NAVI://纯惯
        {
            //导航计算
            NaviCompute(r_Gyro, r_LastGyro, Acc, LastAcc, &g_Navi);
            break;
        }
    }
}

void INS600mAlgorithmEntry(void)
{
	IMUdataPredo();

	// ZUPT静止检测函数调用
	// 调用条件：初始零偏估计已完成
	if (bias_estimate_done) {
		ZUPTDetection(&combineData);
	}

	AlgorithmAct();
	NavDataOutputSet(&combineData, &g_Navi, &InavOutData);
}

void AlgorithmDo(void)
{
	INS600mAlgorithmEntry();
}









