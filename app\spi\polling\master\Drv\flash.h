//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：flash.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.21
//---------------------------------------------------------


#ifndef _FLASH_H
#define _FLASH_H

#include <stdio.h>
#include "board.h"
#include "hpm_debug_console.h"
#include "hpm_l1c_drv.h"
#include "hpm_romapi.h"
#include "hpm_clock_drv.h"


//#define APP_UPDATE_ADDRESS		0x32000	//0x80001000	

	

//初始化FLASH
int norflash_init(void);
//扇区擦除
int norflash_erase_sector(uint32_t offset);

int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes);

int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes);


//FLASH测试程序
void FlashTest(void);
#endif